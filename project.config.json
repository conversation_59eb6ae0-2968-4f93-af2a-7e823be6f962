{"miniprogramRoot": "dist/", "projectname": "闪租婆", "description": "闪租婆", "appid": "wx5fb830b234320db1", "setting": {"urlCheck": true, "es6": true, "enhance": false, "compileHotReLoad": false, "postcss": false, "minified": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "condition": false, "bigPackageSizeSupport": true}, "compileType": "miniprogram", "libVersion": "3.0.0", "srcMiniprogramRoot": "dist/", "packOptions": {"ignore": [], "include": []}, "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 4}, "simulatorPluginLibVersion": {}}