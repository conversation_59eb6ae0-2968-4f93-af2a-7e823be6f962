/*
 * @Autor: lisong
 * @Date: 2023-08-04 11:44:52
 * @LastEditors: lisong
 * @LastEditTime: 2023-08-16 16:02:45
 */
export default defineAppConfig({
  pages: [
    'pages/index/index',
    'pages/message/message',
    'pages/messagePage/messagePage',
    'pages/chuzu/chuzu',
    'pages/qianbao/qianbao',
    'pages/my/my',
    'pages/login/login',
    'pages/login/loginByCode',
    //'pages/login/forget/forget',
    //'pages/reg/reg',
    'pages/userInfo/userInfo',
    'pages/who/who',
    'pages/repair/repair',
    'pages/repair/detail/detail',
    'pages/article/article',
    'pages/bindDevice/bindDevice',
    // 'pages/conf/conf',
    // 'pages/bill/bill',
    'pages/my/fdd/fdd',
    'pages/my/changePwd/changePwd',
    'pages/my/changeBusinessPwd/changeBusinessPwd',
    'pages/my/changePhone/changePhone',
    'pages/webview/webview',
    'pages/webview/article',
    'pages/qa/index',
    'pages/qa/detail'
  ],
  subpackages: [
    {
      'root': 'pages/house',
      'pages': [
        'house',
        'add/add',
        'edit/edit',
        'addDevice/addDevice',
        'detail/detail',
        'lockPwdEdit/lockPwdEdit',
        'lockPwdAdmin/lockPwdAdmin',
        'addLock/addLock',
        'lockDetail/lockDetail',
        'lockRizhi/lockRizhi',
        'passwordAdmin/passwordAdmin',
        'addWaterDevice/addWaterDevice',
        'bill/bill',
        'conf/conf'
      ]
    },
    {
      'root': 'pages/other',
      'pages': [
        'houseTagAdmin/houseTagAdmin',
        "houseTagEdit/houseTagEdit",
        "publicShare/publicShare",
        "publicShareSonSetting/publicShareSonSetting",
        "publicShareSetting/publicShareSetting",
        "publicLog/publicLog",
        'publicLogDetail/publicLogDetail',
        "waterDetiveDetail/waterDetiveDetail",
        "waterDiNumEdit/waterDiNumEdit",
        "setAdmin/setAdmin"
      ]
    },
    {
      'root': 'pages/tenant',
      'pages': [
        'device/device',
        'deviceDetail/deviceDetail',
        'tempDevice/tempDevice',
        'house/house',
        'repair/repair',
        'repairAdd/repairAdd',
        'repairDetail/repairDetail',
        'contract/contract',
        'bill/bill',
        'order/order',
        'tenantInfo/tenantInfo',
        'tenantGongAnInfo/tenantGongAnInfo',
        'tempOrder/tempOrder'
      ]
    },
    {
      'root': 'pages/device',
      'pages': [
        'device',
        'moreList/moreList',
        'detail/detail',
        'recharge/recharge',
        'log/log',
        'rebind/rebind',
        'failOrder/failOrder',
        'consumption-record/consumption-record',
        'fastCheck/fastCheck',
      ]
    },
    {
      'root': 'pages/contract',
      'pages': [
        'contract',
        'add/add',
        'chooseType/chooseType',
        'createZcontract/createZcontract',
        'tenant/tenant',
        'rent/rent',
        'billList/billList',
        'form/form',
        'detail/detail',
        'bill/bill',
        'breakForm/breakForm',
        'billPayForm/billPayForm',
        'billPay/billPay',
        'billCancel/billCancel',
        'createResult/createResult'
      ]
    },
    {
      'root': 'pages/money',
      'pages': [
        // 'money',
        'stat/stat',
        'card',
        'bindCard',
        'cardInfo',
        'cashLog',
        'draw',
        'bill'
      ]
    }
  ],
  window: {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: 'WeChat',
    navigationBarTextStyle: 'black'
  },
  tabBar: {
    color: '#C7C9D3',
    selectedColor: '#1379FF',
    backgroundColor: '#FFFFFF',
    borderStyle: 'black',
    custom: true,
    list: [
      {
        pagePath: 'pages/index/index',
        text: '首页',
        iconPath: 'assets/images/tabbar-home.png',
        selectedIconPath: 'assets/images/tabbar-home-active.png'
      },
      {
        pagePath: 'pages/chuzu/chuzu',
        text: '出租',
        iconPath: 'assets/images/chuzu.png',
        selectedIconPath: 'assets/images/chuzu-a.png'
      },
      {
        pagePath: 'pages/qianbao/qianbao',
        text: '钱包',
        iconPath: 'assets/images/qianbao.png',
        selectedIconPath: 'assets/images/qianbao-a.png'
      },
      {
        pagePath: 'pages/message/message',
        text: '消息',
        iconPath: 'assets/images/tabbar-msg.png',
        selectedIconPath: 'assets/images/tabbar-msg-active.png'
      },
      {
        pagePath: 'pages/my/my',
        text: '我的',
        iconPath: 'assets/images/tabbar-my.png',
        selectedIconPath: 'assets/images/tabbar-my-active.png'
      }
    ]
  },
  permission: {
    'scope.userLocation': {
      desc: '你的位置信息将用于创建房源时选择您所在的小区'
    },
    "scope.writePhotosAlbum" : {
      "desc" : "用于保存设备的二维码照片"
    }
  },
  requiredPrivateInfos: [
    'getLocation',
    'chooseLocation'
  ],
  "plugins": process.env.TARO_ENV !== 'alipay'? {
    "myPlugin": {
        "version": "3.0.4",
        "provider": "wx43d5971c94455481"
    }
  } : {},
})
