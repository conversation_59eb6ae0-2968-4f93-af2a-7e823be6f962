import { createApp } from 'vue'
import { createPinia } from 'pinia'
import './app.scss'
import Taro from '@tarojs/taro'

const App = createApp({
  onLoad() {
    console.log('App onLoad.')
  },
  onShow (options) {
    console.log('App onShow.')
  },
  onLaunch (options) {
    console.log('App onLaunch.', options)
    console.log(this.$store);
    if (options?.query && options?.query?.qrCode) {
      const queryString = options?.query?.qrCode;
      const params = {};
      
      queryString.substr(queryString.indexOf('?') + 1)
        .split('&')
        .forEach(param => {
          const [key, value] = param.split('=');
          params[key] = decodeURIComponent(value);
        });
        
      // const snN = options?.query?.qrCode.split('?sn=')[1]
      const snN = params['sn'];
      console.log('snN', snN)
      
      if (snN) {
      Taro.setStorageSync('qrcodeSn', snN)
      } else {
      Taro.removeStorageSync('qrcodeSn')
      }
    }
  }
  // 入口组件不需要实现 render 方法，即使实现了也会被 taro 所覆盖
})


App.use(createPinia())

export default App
