@import "taro-ui-vue3/dist/style/components/swipe-action.scss";
@import "taro-ui-vue3/dist/style/components/checkbox.scss";
@import "taro-ui-vue3/dist/style/components/switch.scss";
.at-swipe-action__content  {
  overflow: hidden;
}
.at-swipe-action{
  border-radius: 14px;
}
page, body {
  font-family: OPPOSans;
}
/* text & align */
.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}
.text-ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
/* flex */
.flex {
  display: flex;
}
.flex-half {
  width: 50%;
}
.flex-row {
  flex-direction: row;
  flex-wrap: wrap;
}
.flex-v-center {
  align-items: center;
}
.flex-center {
  justify-content: center;
}
.flex-right {
  justify-content: flex-end;
}
.flex-space-between {
  justify-content: space-between;
}

/*font size*/
.font-24 {
  font-size: 24px;
}
.font-26 {
  font-size: 26px;
}
.font-32 {
  font-size: 32px;
}
.font-28 {
  font-size: 28px;
}
.font-36 {
  font-size: 36px;
  font-family: OPPOSans;
  font-weight: 500;
}

/* text color*/
.color-low {
  color: #B6BEC5
}
.color-primary {
  color: #1453FD;
}

/* button */
.btn-primary {
  background-color: #1352FD;
  color: #FFFFFF;
  font-size: 36px;
  height: 100px;
}
.btn-disabled {
  background: #E3E7EF;
  font-size: 36px;
  font-weight: 500;
  color: #96A6C5;
  height: 100px;
}
.btn-primary-small {
  background-color: #1352FD;
  color: #FFFFFF;
  font-size: 26px;
  height: 60px;
  line-height: 60px;
  padding: 0 29px;
}
.btn-add {
  background: #1352FD;
  border-radius: 20px;
  font-size: 36px;
  color: #FFFFFF;
  font-family: OPPOSans;
  font-weight: 500;
}
.btn-second {
  border: 1px solid #1352FD;
  background-color: #FFFFFF;
  color: #1352FD;
  font-size: 36px;
  height: 100px;
}
.btn-second-small {
  background-color: #FFFFFF;
  color: #1352FD;
  font-size: 26px;
  height: 60px;
  line-height: 60px;
  padding: 0 29px;
}
.btn-text {
  color: #1352FD;
  font-size: 36px;
}
.btn-text-small {
  color: #1352FD;
  font-size: 28px;
}
.btn-inline {
  display: inline-block;
  padding: 0 35px;
}

/* layout */
.mt10 {
  margin-top: 10px;
}
.mt20 {
  margin-top: 20px;
}
.ml33 {
  margin-left: 33px;
}
.mt36 {
  margin-top: 36px;
}
.p20 {
  padding: 20px;
}
.p40 {
  padding: 40px;
}
.m33 {
  margin: 33px 25px;
}
.no-data {
  padding: 50px 0;
  text-align: center;
  font-size: 24px;
  color: #98ADE6;
  font-weight: 200;
}

.footer-fixed {
  background-color: #FFFFFF;
  box-shadow: 0rpx -4rpx 32rpx 0rpx rgba(54,69,193,0.24);
  position: fixed;
  bottom: 0;
  width: 100%;
  box-sizing: border-box;
  text-align: center;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

.text-v-center {
  display: inline-block; vertical-align: middle;
  // 单行文本省略号
  // overflow: hidden;
  text-overflow: ellipsis;
  // white-space: nowrap;
  max-width: 300px;
}


/*tag*/
.tag {
  height: 40px;
  line-height: 40px;
  background-color: #FFFFFF;
  text-align: center;
  display: inline-block;
  font-size: 24px;
  font-family: OPPOSans;
  font-weight: 500;
  padding: 0 13px;
  border-radius: 6px;
}
.tag-default {
  color: #98ADE6;
}
.tag-selected {
  color: #1352FD;
}
.tag-warning-small {
  color: #FFFFFF;
  height: 34px;
  line-height: 34px;
  background: #F35C19;
  font-size: 22px;
}
.tag-link {
  color: #FFFFFF;
  background-color: #4274FF;
  border-radius: 100px;
}
.tag-mini {
  height: 30px;
  line-height: 30px;
  font-size: 20px;
}
@font-face {
  font-family: "iconfont"; /* Project id 4483354 */
  src: url('//at.alicdn.com/t/c/font_4483354_p6ppn5fkgv.woff2?t=1745398246436') format('woff2'),
       url('//at.alicdn.com/t/c/font_4483354_p6ppn5fkgv.woff?t=1745398246436') format('woff'),
       url('//at.alicdn.com/t/c/font_4483354_p6ppn5fkgv.ttf?t=1745398246436') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-duihao:before {
  content: "\eaf1";
}

.icon-shuibiao:before {
  content: "\e691";
}

.icon-setting1:before {
  content: "\e62a";
}

.icon-shuaxin:before {
  content: "\e647";
}

.icon-tanhao:before {
  content: "\e8c6";
}

.icon-search2:before {
  content: "\e611";
}

.icon-qiehuan:before {
  content: "\e607";
}

.icon-dianbiao_shiti:before {
  content: "\eca1";
}

.icon-setting:before {
  content: "\e606";
}

.icon-bianji1:before {
  content: "\e605";
}

.icon-a-jigouguanliduantubiao_huaban1fuben13:before {
  content: "\e661";
}

.icon-nav_icon_cssz_spe:before {
  content: "\e603";
}

.icon-youjiantou1:before {
  content: "\e622";
}

.icon-sousuo:before {
  content: "\e782";
}

.icon-jiahao1:before {
  content: "\eaf3";
}

.icon-close:before {
  content: "\e63e";
}

.icon-xiajiantou1:before {
  content: "\e63c";
}

.icon-shangjiantou:before {
  content: "\e628";
}

.icon-wenhao:before {
  content: "\e72d";
}

.icon-bangding:before {
  content: "\e615";
}

.icon-bianji:before {
  content: "\e604";
}

.icon-lingdang:before {
  content: "\e8c0";
}

.icon-xiajiantou:before {
  content: "\e602";
}

.icon-youjiantou:before {
  content: "\e600";
}

.icon-zuojiantou:before {
  content: "\e601";
}
