<template>
  <View class="navbar" :class="theme === 'white' ? 'navbar-white' : 'navbar-black'" :style="'height:' + (globalStore.windowInfo.statusBarHeight + 55) + 'px'">
    <template v-if="theme === 'black'">
      <view class="navbar-btn" :style="'top:' + (globalStore.windowInfo.statusBarHeight + 10) + 'px'">
        <view style="width: 48rpx; height: 48rpx; display: inline-block;" v-if="pageCount > 1" @tap="handleBack"><image :src="iconBack" class="nav-icon-back"></image></view>
        <view style="width: 48rpx; height: 48rpx; display: inline-block;" v-if="pageCount === 1" @tap="handleBackHome"><image v-if="pageCount === 1" :src="iconBackHome" @tap="handleBackHome" class="nav-icon-back-home" ></image></view>
      </view>
      <view class="navbar-title" :style="'top:' + (globalStore.windowInfo.statusBarHeight + 10) + 'px'" v-if="title">{{title}}</view>
    </template>
    <template v-if="theme === 'white'">
      <view class="navbar-btn" :style="'top:' + (globalStore.windowInfo.statusBarHeight + 10) + 'px'">
        <view style="width: 48rpx; height: 48rpx; display: inline-block;" v-if="pageCount > 1" @tap="handleBack"><image :src="iconBackWhite" class="nav-icon-back"></image></view>
        <view style="width: 48rpx; height: 48rpx; display: inline-block;" v-if="pageCount === 1" @tap="handleBackHome"><image :src="iconBackHomeWhite" class="nav-icon-back-home"></image></view>
      </view>
      <view class="navbar-title" :style="'top:' + (globalStore.windowInfo.statusBarHeight + 10) + 'px'" v-if="title">{{title}}</view>
    </template>
  </View>
</template>

<script setup>
  import { ref } from 'vue'
  import Taro from '@tarojs/taro'

  const iconBack = 'https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-back.png'
  const iconBackHome = 'https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-back-home.png'
  const iconBackWhite = 'https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-back-white.png'
  const iconBackHomeWhite = 'https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-back-home-white.png'

  const props = defineProps({
    title: {
      type: String,
      required: false,
      default: ''
    },
    theme: {
      type: String,
      required: false,
      default: 'black' // white or black
    }
  })

  import { useGlobalStore } from '../../stores'
  const globalStore = useGlobalStore()

  const pageCount = Taro.getCurrentPages().length

  const handleBack = () => {
    console.log('back')
    Taro.navigateBack()
  }
  const handleBackHome = () => {
    Taro.switchTab({
      url: '/pages/index/index'
    })
  }

</script>
<style>
  .navbar {
    position: fixed;
    width: 100%;
    height: 150px;
    left:  0;
    top:  0;
  }
  .navbar-btn {
    position: absolute;
    left:  30px;
    top: 0;
    z-index: 999;
  }
  .navbar-title {
    position: absolute;
    left:  0;
    top:  0;
    text-align: center;
    width: 100%;
    z-index: 998;
    font-size: 34rpx;
    font-family: OPPOSans;
    font-weight: 500;
    padding-top:  5px;
  }
  .nav-icon-back {
    width: 48px;
    height: 48px;
  }
  .nav-icon-back-home {
    width: 52px;
    height: 52px;
  }
  .navbar-black {
    color: #000000;
  }
  .navbar-white {
    background-color: #1352FD;
    color:  #FFFFFF;
  }
  .nav-icon-box {
    display: inline-block;
    padding:  0;
  }
</style>
