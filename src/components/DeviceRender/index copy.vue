<template>
  <view class="device-list">
    <!-- <view class="no-data">暂无信息</view> -->
    <view
      class="device-item"
      v-for="device in items"
      :key="device.id"
      @tap="onDetail(device, $event)"
    >
      <view class="flex device-top">
        <!-- <view class="fast-box" @tap.stop="fastCheckHandel(device)" v-if="globalStore.who != 'tenant'">
          <image src="@/assets/images/fastimg.png" mode="aspectFill" />
          <text class="text" v-if="calculateDaysBetweenDates(new Date(), device?.house?.fast_contract?.expired_at) >= 0 && !isNaN(calculateDaysBetweenDates(new Date(), device?.house?.fast_contract?.expired_at))">租期剩余{{ calculateDaysBetweenDates(new Date(), device?.house?.fast_contract?.expired_at) }}天</text>
          <text class="text" style="color: red;" v-if="calculateDaysBetweenDates(new Date(), device?.house?.fast_contract?.expired_at) < 0 && !isNaN(calculateDaysBetweenDates(new Date(), device?.house?.fast_contract?.expired_at))">租期逾期{{ Math.abs(calculateDaysBetweenDates(new Date(), device?.house?.fast_contract?.expired_at)) }}天</text>
        </view> -->

        <HomeIocnType
          type="1"
          :device="device"
          :fast_contract="device?.house?.fast_contract ||{}"
          :curentType="device.curentType"
        />
        <!-- 门锁 -->
        <HomeIocnType
          type="2"
          :device="device"
          :fast_contract="device?.house?.fast_contract"
          @switchType="device.curentType = 2"
          :curentType="device.curentType"
        />
        <!-- 电表 -->
        <HomeIocnType
          type="4"
          :device="device"
          :fast_contract="device?.house?.fast_contract"
          @switchType="device.curentType = 4"
          :curentType="device.curentType"
        />

        <view style="flex: 1">
          <view class="device-name" v-if="globalStore.who == 'tenant'">电表</view>
          <view class="device-sn" v-if="globalStore.who == 'tenant'">
            <text v-if="device.net_type === 1">4G双模电表号</text
            ><text v-if="device.net_type !== 1">蓝牙电表号</text>:
            {{ device.sn }}
          </view>
        </view>
        <view style="width: auto; display: flex; align-items: center">
          <view class="close-box" v-if="((device.du || 0) < 0.6) && device.status == 2">
            欠费断电
          </view>
          <!-- marginRight:device.net_type !== 1 && globalStore.who != 'tenant' ? '12rpx' : device.net_type == 1 && globalStore.who != 'tenant' ? '-12rpx' : device.net_type == 1 && globalStore.who == 'tenant' ? '482rpx' : '559rpx' -->
          <!-- <view class="device-icon cus" :style="{marginRight:device.net_type !== 1 ? '12rpx' : '-10rpx'}">
            <MyIcon icon="icon-dianbiao" width="52rpx"></MyIcon>
            <text class="sn-text" :style="{marginLeft:device.net_type !== 1 ? '-30rpx' : '5rpx'}"> {{ device.sn }}</text>
          </view>
          <text class="label" v-if="device.net_type === 1">4G</text>
          <text class="label" v-if="device.net_type !== 1"></text>
          <MyIcon
            :icon="'signal/' + device.signal_num"
            width="60rpx"
            height="49rpx"
            style="margin-left: 0rpx"
            v-if="device.net_type === 1"
          ></MyIcon> -->
        </view>
      </view>

      <!-- <view class="device-info flex flex-space-between flex-v-center">
        <view>
          <view class="device-num"
            ><text class="du-t">{{ device.total || 0 }}</text>
            <text>度</text></view
          >
          <view class="device-lab">总电量</view>
        </view>
        <view>
          <view class="device-num"
            ><text class="du-t">{{ device.du || 0 }}</text
            ><text>度</text></view
          >
          <view class="device-lab">剩余电量</view>
        </view>
        <view>
          <view class="device-num">{{ device.price }}<text>元/度</text></view>
          <view class="device-lab">单价</view>
        </view>
      </view> -->

      <itemHeperlBusiness
        :device="device"
        :fast_contract="device?.house?.fast_contract"
        :curentType="device.curentType"
      />

      <view style="padding: 10rpx 20rpx 0 20rpx" class="btm-b">
        <view class="le">
          <!-- <MyIcon icon="icon-house-mini" width="30rpx" height="26rpx"></MyIcon> -->
          <image
            src="https://yimits.oss-cn-beijing.aliyuncs.com/images/fangzi.png"
            mode="scaleToFill"
            class="icon-s"
          />
          <text @tap.stop="editHoseName(device)">
            <text class="text-v-center font-28 _name" v-if="device.house">{{
              device.house.name
            }}</text>
            <text
              v-if="device.house && globalStore.who != 'tenant'"
              class="iconfont icon-bianji"
              style="
                font-size: 25rpx;
                display: inline-block;
                margin: 0 0 0 10rpx;
                transform: translateY(-6rpx);
                font-weight: 400;
                color: #333;
              "
            ></text>
          </text>
          <text class="text-v-center font-28 _name" v-if="!device.house"
            >未绑定房源</text
          >
        </view>
        <view
          class="haodian"
          :data-ignore="true"
          v-if="device.net_type === 1 && device.signal_num > 0"
          @tap.stop="toHaoDianDetailHandel(device)"
        >
          <image
            class="ico-l"
            src="https://yimits.oss-cn-beijing.aliyuncs.com/images/haodian.png"
            mode="scaleToFill"
          />
          <text>耗电记录 </text>
          <image
            class="ico-r"
            src="https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-right-white.png"
            mode="scaleToFill"
          />
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import MyIcon from "@/components/MyIcon";
import Taro from "@tarojs/taro";
import request from "@/utils/request";
import { ref } from "vue";
import HomeIocnType from "@/components/homeIconType";
import itemHeperlBusiness from "./itemHeperlBusiness.vue";
const curentType = ref(4);



import { useGlobalStore } from '@/stores'
  const globalStore = useGlobalStore()

const props = defineProps({
  items: {
    type: Array,
    required: false,
    default() {
      return [];
    },
  },
});

function calculateDaysBetweenDates(date1, date2) {
  const dayInMs = 24 * 60 * 60 * 1000; // 一天的毫秒数
  const date1Ms = new Date(date1).getTime(); // 将日期转换为毫秒
  const date2Ms = new Date(date2).getTime(); // 同上

  let day = Math.round((date2Ms - date1Ms) / dayInMs);
  if (day < 0) {
    day = day + 1;
  } else {
    day = day - 1;
  }

  return day; // 计算差值并转换为天数
}

const emit = defineEmits(["detail"]);

const editHoseName = (item) => {
  emit("editHoseName", item);
};

const onDetail = (item, e) => {
  emit("detail", item.id);
};

const device = ref();

// 跳转快捷入驻
const fastCheckHandel = (item) => {
  request
    .get({
      url: "device/" + item.id,
    })
    .then((res) => {
      device.value = res.data;
      const obj = {
        id: device.value.id,
        need_people: device.value.need_people,
        sn: device.value.sn,
      };
      Taro.navigateTo({
        url: "/pages/device/fastCheck/fastCheck?deveice=" + JSON.stringify(obj),
      });
    });
};

// 跳转到耗电记录
const toHaoDianDetailHandel = (item) => {
  console.log(item);
  Taro.navigateTo({
    url: "/pages/device/consumption-record/consumption-record?id=" + item.id,
  });
};
</script>
<style lang="scss">
.device-list {
  padding: 0 23px;

  .device-item {
    background: #ffffff;
    box-shadow: 0rpx 4rpx 32rpx 0rpx rgba(182, 190, 201, 0.38);
    border-radius: 20rpx;
    border: 1px solid #1452fd;
    padding: 15px;
    margin-top: 23px;
    .close-box {
      font-size: 24px;
      color: #f2811a;
      background-color: #fce9d1;
      padding: 5px 10px;
      margin-right: 10px;
      border-radius: 6px;
    }

    .house-box-title {
      font-size: 36rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #000000;
      line-height: 31rpx;
    }
  }
  .device-top {
    margin-bottom: 7px;

    .device-name {
      font-size: 28rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #000000;
    }
    .device-sn {
      font-size: 24rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #000000;
    }
    .device-icon {
      width: 60px;
      height: 60px;
      background: #e9effa;
      border-radius: 10px;
      margin-right: 12px;
      text-align: center;
      // padding: 2px 0;
      .my-icon {
        margin-top: -10px;
      }
      .sn-text {
        font-size: 16px;
        font-weight: 700;
        margin-top: 10px;
        display: block;
        margin-left: 5px;
      }
      &.cus {
        position: relative;
        width: 88px;
        height: 72px;
        .sn-text  {
          position: absolute;
          left: 50%;
          bottom: 3px;
          font-size: 12px;
          transform: translateX(-50%);
          margin-left: 0 !important;
        }
      }
    }
    .fast-box {
      display: flex;
      flex-direction: column;
      justify-content: center;
      // align-items: center;
      // margin-left: 80px;
      image {
        // width: 125px;
        // height: 90px;
      }
      text {
        color: #000;
        font-size: 20px;
        font-weight: 700;
        // margin-top: -10px;
      }
    }
    .label {
      font-weight: 700;
    font-size: 15px;
    display: inline-block;
    transform: translate(22px, -15px);
    }
  }
  .device-info {
    background: #f0f4ff;
    border-radius: 20px;
    padding: 10px 25px;
    text-align: center;
    > view {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      width: 175px;
      height: 120px;
      background: #f0f4ff;
      border-radius: 20px;
    }

    .device-num {
      font-size: 40rpx;
      color: #1452fd;
      font-family: Bahnschrift;
      font-weight: 400;
      margin-bottom: 10px;
      font-weight: 700;
      display: flex;
      align-items: center;
      .du-t {
        max-width: 150px;
        overflow: hidden;
        display: inline-block;
        text-overflow: ellipsis;
        white-space: nowrap;
        color: #1452fd !important;
        font-size: 40px !important;
        margin-top: 0 !important;
      }
      text {
        font-weight: 500;
        font-size: 18px;
        color: #4460b3;
        margin-top: 5px;
      }
    }
    .device-lab {
      font-size: 26rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #879bd6;
    }
  }

  // -------------
  .btm-b {
    display: flex;
    align-items: center;
    justify-content: space-between;
    ._name {
      font-size: 32px;
      font-weight: 700;
      margin-left: 5px;
      vertical-align: middle !important;
    }
    .icon-s {
      width: 32px;
      height: 32px;
      vertical-align: middle;
    }
    .text-v-center {
      flex-shrink: 0;
      vertical-align: bottom;
    }
    .haodian {
      flex-shrink: 0;

      width: 160px;
      height: 46px;
      background: linear-gradient(90deg, #2a69f6 0%, #519bfd 100%);
      font-size: 20px;
      color: #fff;
      border-radius: 5px;
      line-height: 46px;
      text-align: center;
      .ico-l {
        width: 16px;
        height: 23px;
        vertical-align: middle;
        margin-right: 5px;
      }
      .ico-r {
        width: 9px;
        height: 15px;
        margin-left: 15px;
      }
    }
  }
}
</style>
