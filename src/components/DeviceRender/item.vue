<template>
  <view class="device-list2">
    <view class="device-item" @tap="onDetail(device.id)">
      <view class="flex device-top">
        <!-- 快速入住 -->
        <HomeIocnType
          type="1"
          :device="device"
          :fast_contract="fast_contract"
          :curentType="curentType"
        />
        <!-- 门锁 -->
        <HomeIocnType
          type="2"
          :device="device"
          :fast_contract="fast_contract"
          @switchType="curentType = 2"
          :curentType="curentType"
          v-if="!isAp"
        />
          <!-- 水表 -->
          <HomeIocnType
            type="3"
            :device="device"
            :fast_contract="fast_contract"
            @switchType="curentType = 3"
            :curentType="curentType"
            :tentDianHouseDevices="tentDianHouseDevices"
            :tentWaterHouseDevices="tentWaterHouseDevices"
          />
        <!-- 电表 -->
        <HomeIocnType
          type="4"
          :device="device"
          :fast_contract="fast_contract"
          @switchType="curentType = 4"
          :curentType="curentType"
          :tentDianHouseDevices="tentDianHouseDevices"
          :tentWaterHouseDevices="tentWaterHouseDevices"
        />
        <view style="flex: 1; padding-top: 15rpx">
          <!-- <view class="device-name" v-if="!fast_contract">电表</view> -->
        </view>

        <!-- <view style="width: auto; display: flex; align-items: center">
          <view
            class="close-box"
            v-if="(device.du || 0) < 0.6 && device.status == 2"
          >
            欠费断电
          </view>
          <view
            class="device-icon cus"
            :style="{ marginRight: device.net_type !== 1 ? '12rpx' : '-10rpx' }"
          >
            <MyIcon icon="icon-dianbiao" width="52rpx"></MyIcon>
            <text
              class="sn-text"
              :style="{ marginLeft: device.net_type !== 1 ? '-30rpx' : '5rpx' }"
            >
              {{ device.sn }}</text
            >
          </view>
          <text class="label" v-if="device.net_type === 1">4G</text>
          <text class="label" v-if="device.net_type !== 1"></text>
          <MyIcon
            :icon="'signal/' + device.signal_num"
            width="60rpx"
            height="49rpx"
            style="margin-left: 0rpx"
            v-if="device.net_type === 1"
          ></MyIcon>
        </view> -->
      </view>
      <HomeTypeContainer
        :device="device"
        :fast_contract="fast_contract"
        :curentType="curentType"
      />
    </view>
  </view>
</template>

<script setup>
import MyIcon from "@/components/MyIcon";
import request from "@/utils/request";
import Taro, { useDidShow } from "@tarojs/taro";
import { onMounted, ref } from "vue";
import HomeIocnType from "@/components/homeIconType";
import HomeTypeContainer from "./itemHeperl.vue";
import { useGlobalStore } from '@/stores'
const isAp = process.env.TARO_ENV === 'alipay'
const globalStore = useGlobalStore()
  
const props = defineProps({
  device: {
    type: Object,
    required: false,
    default() {
      return {};
    },
  },
  tentDianHouseDevices: {
    type: Array,
    required: false,
    default() {
      return [];
    },
  },
  tentWaterHouseDevices: {
    type: Array,
    required: false,
    default() {
      return [];
    },
  }
});

const emit = defineEmits(["detail"]);

const curentType = ref(4);

const fast_contract = ref();

const onDetail = (id) => {
  emit("detail", id);
};

// 获取快捷入住详情
const getFastDetail = () => {
  if(!props.device.id) return
  request
    .get({
      url: "contract/fastDetail",
      data: {
        device_id: props.device.id,
      },
      showToast: false,
    })
    .then((res) => {
      fast_contract.value = res.data;
    });
};

useDidShow(() => {
  if (globalStore.tempDeviceList.length <= 0 && globalStore.waterTempDeviceList.length > 0) {
    curentType.value = 3
  }
  getFastDetail();
});

onMounted(() => {
  getFastDetail();
});


</script>
<style lang="scss">
.device-list2 {
  .device-item {
    padding: 35px;

    background: #ffffff;
    box-shadow: 0rpx 4rpx 32rpx 0rpx rgba(182, 190, 201, 0.38);
    border-radius: 20rpx;
    box-sizing: border-box;
    // border: 1px solid #1452fd;
    // padding: 15px;
    // margin-top: 23px;
    height: 395px;

    .house-box-title {
      font-size: 36rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #000000;
      line-height: 31rpx;
    }

    .close-box {
      font-size: 24px;
      color: #f2811a;
      background-color: #fce9d1;
      padding: 5px 10px;
      margin-right: 10px;
      border-radius: 6px;
    }
  }

  .device-top {
    margin-bottom: 7px;

    .device-name {
      font-size: 28rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #000000;
    }

    .device-sn {
      font-size: 24rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #000000;
    }

    .device-icon {
      width: 60px;
      height: 60px;
      background: #e9effa;
      border-radius: 10px;
      margin-right: 12px;
      text-align: center;

      // padding: 2px 0;
      .my-icon {
        margin-top: -10px;
      }

      .sn-text {
        font-size: 16px;
        font-weight: 700;
        margin-top: 10px;
        display: block;
        margin-left: 5px;
      }

      &.cus {
        position: relative;
        width: 88px;
        height: 72px;

        .sn-text {
          position: absolute;
          left: 50%;
          bottom: 3px;
          font-size: 12px;
          transform: translateX(-50%);
          margin-left: 0 !important;
        }
      }
    }

    .fast-box {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 142px;
      height: 97px;
      // background: #ccc;
      background: #f0f5fe;
      border-radius: 10px;

      // align-items: center;
      // margin-left: 80px;
      image {
        width: 105px;
        height: 61px;
      }

      text {
        color: #000;
        font-size: 20px;
        font-weight: 700;
        // margin-top: -10px;
      }
    }

    .label {
      font-weight: 700;
      font-size: 15px;
      display: inline-block;
      transform: translate(22px, -15px);
    }
  }

  .device-info {
    background: #f0f4ff;
    border-radius: 20px;
    padding: 10px 25px;
    text-align: center;

    > view {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      width: 175px;
      height: 120px;
      background: #f0f4ff;
      border-radius: 20px;
    }

    .device-num {
      font-size: 40rpx;
      color: #1452fd;
      font-family: Bahnschrift;
      font-weight: 400;
      margin-bottom: 10px;
      font-weight: 700;
      display: flex;
      align-items: center;

      .du-t {
        max-width: 150px;
        overflow: hidden;
        display: inline-block;
        text-overflow: ellipsis;
        white-space: nowrap;
        color: #1452fd !important;
        font-size: 40px !important;
        margin-top: 0 !important;
      }

      text {
        font-weight: 500;
        font-size: 18px;
        color: #4460b3;
        margin-top: 5px;
      }
    }

    .device-lab {
      font-size: 26rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #879bd6;
    }
  }

  // -------------
  .btm-b {
    display: flex;
    align-items: center;
    justify-content: space-between;

    ._name {
      font-size: 32px;
      font-weight: 700;
      margin-left: 5px;
      vertical-align: middle !important;
    }

    .icon-s {
      width: 32px;
      height: 32px;
      vertical-align: middle;
    }

    .text-v-center {
      flex-shrink: 0;
      vertical-align: bottom;
    }

    .haodian {
      flex-shrink: 0;

      width: 160px;
      height: 46px;
      background: linear-gradient(90deg, #2a69f6 0%, #519bfd 100%);
      font-size: 20px;
      color: #fff;
      border-radius: 5px;
      line-height: 46px;
      text-align: center;

      .ico-l {
        width: 16px;
        height: 23px;
        vertical-align: middle;
        margin-right: 5px;
      }

      .ico-r {
        width: 9px;
        height: 15px;
        margin-left: 15px;
      }
    }
  }
}
</style>
