<template>
  <view class="device-list2">
    <view class="device-item" @tap="onDetail(device.id)">
      <view class="flex device-top">
        <!-- <view class="device-icon">
          <MyIcon icon="icon-dianbiao" width="52rpx"></MyIcon>
        </view> -->
        <view class="fast-box" @tap.stop="fastCheckHandel(device)" v-if="fast_contract">
          <image src="https://yimits.oss-cn-beijing.aliyuncs.com/images/fastimg.png" mode="aspectFill" />
          <!-- <text>租期剩余：200天</text> -->
          <text class="text"
            v-if="calculateDaysBetweenDates(new Date(), fast_contract?.expired_at) >= 0 && !isNaN(calculateDaysBetweenDates(new Date(), fast_contract?.expired_at))">租期剩余{{
      calculateDaysBetweenDates(new Date(), fast_contract?.expired_at) }}天</text>
          <text class="text" style="color: red;"
            v-if="calculateDaysBetweenDates(new Date(), fast_contract?.expired_at) < 0 && !isNaN(calculateDaysBetweenDates(new Date(), fast_contract?.expired_at))">租期逾期{{
      Math.abs(calculateDaysBetweenDates(new Date(), fast_contract?.expired_at)) }}天</text>

        </view>
        <view style="flex: 1;padding-top: 15rpx">
          <view class="device-name" v-if="!fast_contract">电表</view>
          <!-- <view class="device-sn" v-if="!fast_contract">
            <text v-if="device.net_type === 1">4G双模电表号</text><text v-if="device.net_type !== 1">蓝牙电表号</text>:
            {{ device.sn }}
          </view> -->
        </view>

        <view style="width: auto; display: flex; align-items: center">
          <view class="close-box" v-if="((device.du || 0) < 0.6) && device.status == 2">
            欠费断电
          </view>
          <!-- marginRight:device.net_type !== 1 && globalStore.who != 'tenant' ? '12rpx' : device.net_type == 1 && globalStore.who != 'tenant' ? '-12rpx' : device.net_type == 1 && globalStore.who == 'tenant' ? '482rpx' : '559rpx' -->
          <view class="device-icon cus" :style="{ marginRight: device.net_type !== 1 ? '12rpx' : '-10rpx' }">
            <MyIcon icon="icon-dianbiao" width="52rpx"></MyIcon>
            <text class="sn-text" :style="{ marginLeft: device.net_type !== 1 ? '-30rpx' : '5rpx' }"> {{ device.sn
              }}</text>
          </view>
          <text class="label" v-if="device.net_type === 1">4G</text>
          <text class="label" v-if="device.net_type !== 1"></text>
          <MyIcon :icon="'signal/' + device.signal_num" width="60rpx" height="49rpx" style="margin-left: 0rpx"
            v-if="device.net_type === 1"></MyIcon>
        </view>
      </view>
      <view class="device-info flex flex-space-between flex-v-center">
        <view>
          <view class="device-num">{{ Number(device?.total || 0).toFixed(2) }}</view>
          <view class="device-lab">总电量</view>
        </view>
        <view>
          <view class="device-num">{{ device.du || 0 }}</view>
          <view class="device-lab">剩余电量(度)</view>
        </view>
        <view>
          <view class="device-num">{{ device.price }}</view>
          <view class="device-lab">单价(元/度)</view>
        </view>
      </view>
      <view style="padding: 10rpx 20rpx 0 20rpx" class="btm-b">
        <!-- <MyIcon icon="icon-house-mini" width="30rpx" height="26rpx"></MyIcon>  <text class="text-v-center font-28" v-if="device.house">{{device.house.name}}</text>
        <text class="text-v-center font-28" v-if="!device.house">未绑定房源</text> -->
        <view class="le">
          <!-- <MyIcon icon="icon-house-mini" width="30rpx" height="26rpx"></MyIcon> -->
          <image src="https://yimits.oss-cn-beijing.aliyuncs.com/images/fangzi.png" mode="scaleToFill" class="icon-s" />
          <text>
            <text class="text-v-center font-28 _name" v-if="device.house">{{
      device.house.name
              }}</text>
          </text>
          <text class="text-v-center font-28 _name" v-if="!device.house">未绑定房源</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import MyIcon from '@/components/MyIcon'
import request from "@/utils/request";
import Taro, { useDidShow } from "@tarojs/taro";
import { onMounted, ref } from 'vue';



const props = defineProps({
  device: {
    type: Object,
    required: false,
    default () {
      return {}
    }
  }
})

const emit = defineEmits(['detail'])

const fast_contract = ref()

const onDetail = (id) => {
  emit('detail', id)
}

// 获取快捷入住详情
const getFastDetail = () => {
  request
    .get({
      url: "contract/fastDetail",
      data: {
        device_id: props.device.id,
      },
      showToast:false
    })
    .then((res) => {
      fast_contract.value = res.data;
    });
};

useDidShow(() => {
  getFastDetail()
})

onMounted(() => {
  getFastDetail()
})

// 跳转快捷入驻
const fastCheckHandel = (item) => {
  request
    .get({
      url: "device/" + item.id,
    })
    .then((res) => {
      const obj = {
        id: res.data.id,
        need_people: res.data.need_people,
        sn: res.data.sn,
      };
      Taro.navigateTo({
        url: "/pages/device/fastCheck/fastCheck?deveice=" + JSON.stringify(obj),
      });
    });
};

function calculateDaysBetweenDates (date1, date2) {
      if(!date1 || !date2) return NaN
  // 将日期转换为当天的 00:00:00
  const d1 = new Date(date1);
  d1.setHours(0, 0, 0, 0);
  
  const d2 = new Date(date2);
  d2.setHours(0, 0, 0, 0);

  const dayInMs = 24 * 60 * 60 * 1000;
  const diffTime = d2.getTime() - d1.getTime();
  const diffDays = Math.round(diffTime / dayInMs);
  
  return diffDays;
} 

</script>
<style lang="scss">
.device-list2 {

  .device-item {
    padding: 35px;

    background: #ffffff;
    box-shadow: 0rpx 4rpx 32rpx 0rpx rgba(182, 190, 201, 0.38);
    border-radius: 20rpx;
    // border: 1px solid #1452fd;
    // padding: 15px;
    // margin-top: 23px;

    .house-box-title {
      font-size: 36rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #000000;
      line-height: 31rpx;
    }

    .close-box {
      font-size: 24px;
      color: #f2811a;
      background-color: #fce9d1;
      padding: 5px 10px;
      margin-right: 10px;
      border-radius: 6px;
    }
  }

  .device-top {
    margin-bottom: 7px;

    .device-name {
      font-size: 28rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #000000;
    }

    .device-sn {
      font-size: 24rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #000000;
    }

    .device-icon {
      width: 60px;
      height: 60px;
      background: #e9effa;
      border-radius: 10px;
      margin-right: 12px;
      text-align: center;

      // padding: 2px 0;
      .my-icon {
        margin-top: -10px;
      }

      .sn-text {
        font-size: 16px;
        font-weight: 700;
        margin-top: 10px;
        display: block;
        margin-left: 5px;
      }

      &.cus {
        position: relative;
        width: 88px;
        height: 72px;

        .sn-text {
          position: absolute;
          left: 50%;
          bottom: 3px;
          font-size: 12px;
          transform: translateX(-50%);
          margin-left: 0 !important;
        }
      }
    }

    .fast-box {
      display: flex;
      flex-direction: column;
      justify-content: center;

      // align-items: center;
      // margin-left: 80px;
      image {
        // width: 125px;
        // height: 90px;
      }

      text {
        color: #000;
        font-size: 20px;
        font-weight: 700;
        // margin-top: -10px;
      }
    }

    .label {
      font-weight: 700;
      font-size: 15px;
      display: inline-block;
      transform: translate(22px, -15px);
    }
  }

  .device-info {
    background: #f0f4ff;
    border-radius: 20px;
    padding: 10px 25px;
    text-align: center;

    >view {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      width: 175px;
      height: 120px;
      background: #f0f4ff;
      border-radius: 20px;
    }

    .device-num {
      font-size: 40rpx;
      color: #1452fd;
      font-family: Bahnschrift;
      font-weight: 400;
      margin-bottom: 10px;
      font-weight: 700;
      display: flex;
      align-items: center;

      .du-t {
        max-width: 150px;
        overflow: hidden;
        display: inline-block;
        text-overflow: ellipsis;
        white-space: nowrap;
        color: #1452fd !important;
        font-size: 40px !important;
        margin-top: 0 !important;
      }

      text {
        font-weight: 500;
        font-size: 18px;
        color: #4460b3;
        margin-top: 5px;
      }
    }

    .device-lab {
      font-size: 26rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #879bd6;
    }
  }

  // -------------
  .btm-b {
    display: flex;
    align-items: center;
    justify-content: space-between;

    ._name {
      font-size: 32px;
      font-weight: 700;
      margin-left: 5px;
      vertical-align: middle !important;
    }

    .icon-s {
      width: 32px;
      height: 32px;
      vertical-align: middle;
    }

    .text-v-center {
      flex-shrink: 0;
      vertical-align: bottom;
    }

    .haodian {
      flex-shrink: 0;

      width: 160px;
      height: 46px;
      background: linear-gradient(90deg, #2a69f6 0%, #519bfd 100%);
      font-size: 20px;
      color: #fff;
      border-radius: 5px;
      line-height: 46px;
      text-align: center;

      .ico-l {
        width: 16px;
        height: 23px;
        vertical-align: middle;
        margin-right: 5px;
      }

      .ico-r {
        width: 9px;
        height: 15px;
        margin-left: 15px;
      }
    }
  }
}
</style>
