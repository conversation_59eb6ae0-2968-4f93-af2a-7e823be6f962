<script setup>
import MyIcon from "@/components/MyIcon";
import request from "@/utils/request";
import Taro, { useDidShow, useLoad } from "@tarojs/taro";
import { onMounted, ref, unref ,watch, computed} from "vue";
import MyPopup from "@/components/MyPopup";
import QueryPricesPopUp from '@/components/queryPricesPopUp'

import {
  createPwdHandel,
  toReadRecord,
  getLockPwdList,
  getLockList,
} from "@/utils/lock";

import { AES_Encrypt, AES_Decrypt, MD5_Encrypt } from "@/utils/crypto";
import { useGlobalStore } from "@/stores";
const globalStore = useGlobalStore();

const dian = "https://yimits.oss-cn-beijing.aliyuncs.com/picss/dian-bg.png";
const routeimg = "https://yimits.oss-cn-beijing.aliyuncs.com/picss/route.png";
const yuanimg = "https://yimits.oss-cn-beijing.aliyuncs.com/picss/yuan-bg.png";
const suoimg = "https://yimits.oss-cn-beijing.aliyuncs.com/picss/key-2.png";
const suoimgOpen = "https://yimits.oss-cn-beijing.aliyuncs.com/picss/key-1.png";

import { union } from "lodash";
import { clientId, clientSecret } from "@/config";

const conf = ref({})
const confBusiness = ref({}) //业务配置

const priceShow = ref(false)

const props = defineProps({
  type: {
    type: String,
    required: true,
    default: "1", //1：快速入住 2：门锁 3：水表 4：电表
  },
  fast_contract: {
    type: Object,
    default: () => ({}),
  },
  device: {
    type: Object,
    default: () => ({}),
  },
  curentType: {
    type: String,
    default: "4",
  },
});

const keyList = ref([]);

const keyInfo = ref(null); //锁信息

const specialValueObj = ref(); //锁特征

const updateDianTime = ref(Date.now()); //电量更新时间

const lockShow = ref(false); //锁弹窗

const showZF = ref(false)
const showZFWater = ref(false) //水表资费说明

const isOpenLock = ref(false); //是否正在开锁

const itemStyle = (type) => {
  if (type == 1) {
    return props.device?.agent?.type != 2  && conf.value?.show_service_price ? 'width:24.5%;flex-shrink:0;background:transparent;' : '';
  } else if(type == 2) {
    // return props.device?.agent?.type != 2  && conf.value?.show_service_price ? 'width:100%' : '';
   return 'width:114%'
  }
}

const itemText = () => {
  if (props.device?.type == 5 || props.device?.type == 6) {
    if (props.device?.pay_mode == 1) {
      // 预付费
      return '剩余金额(元)'
    } else {
      //后付费
      return '已用金额(元)'
    }
  } else {
    return '剩余电量(度)'
  }
}

const waterDevice = computed(() => {
  return globalStore.waterTempDeviceList[0]
})

onMounted(async () => {
  // await regsion();
  // await getToken()
});

const lookAllHouseName = (allName) => {
  Taro.showModal({
    title: "房源名称",
    content: allName,
    showCancel: false,
    confirmText: "知道了"
  })
}

watch(() => props.device, (v) => {
  console.log(v,"vUUUUUU");
  if (v?.sn) {
    request.get({
      url: `device/new_business_conf`,
      data: {
          sn: v.sn
      },
      showToast:false
    }).then(res => {
      console.log(res);
      if(res.code != 200) return
      conf.value = res.data
    })
    request
    .get({
      url: "business/conf",
      data: {
        businessId: v?.business_id,
      },
      showLoading:false,
      showToast:false
    })
    .then((res) => {
      confBusiness.value = res.data;
    });
  }
})

/**
 * 注册
 */
const regsion = () => {
  return new Promise((resolve, reject) => {
    const option = {
      username: "***********shanzupo",
      password: MD5_Encrypt("***********"),
      date: new Date().getTime(),
    };
    request
      .post({
        url: "/v3/user/register",
        LOCKYAPI: true,
        data: {
          clientId,
          clientSecret,
          ...option,
        },
      })
      .then((res) => {
        console.log(res);
        resolve();
      });
  });
};

/**
 * 获得token
 */
const getToken = () => {
  return new Promise((resolve, reject) => {
    const option = {
      username: `didfj_${globalStore.userInfo?.mobile}shanzupotenant`,
      password: MD5_Encrypt(globalStore.userInfo?.mobile),
    };
    if(process.env.TARO_ENV === 'alipay') return resolve()
    request
      .post({
        url: "/oauth2/token",
        LOCKYAPI: true,
        data: {
          clientId: clientId,
          clientSecret: clientSecret,
          ...option,
          grant_type: "password",
          redirect_uri: "http://www.sciener.cn",
        },
      })
      .then((res) => {
        console.log(res);
        globalStore.setTtlaccessToken(res.access_token);
        resolve();
      });
  });
};

useDidShow(() => {
  // 获取钥匙列表
  console.log("获取钥匙列表");
  getToken().then(() => {
    getLockList().then((res) => {
      console.log(res);
      keyList.value = res.list;
      let pwdList = [];
      //租客可能有多个钥匙
      const pwdPromises = res.list.map(li =>
        getLockPwdList({ keyInfo: li })
      );

      Promise.all(pwdPromises)  // 使用 bluebird 的 Promise.all
        .then((pwdArrays) => {
          pwdArrays.forEach(pwd => {
            pwdList.push(...pwd);
          });
          // 在所有密码获取完成后执行
          console.log("所有密码已获取:", pwdList);
          // 在此处可以进行后续操作，例如更新状态或执行其他逻辑-是否是对应房东发的钥匙
         const curentPwd = pwdList.find(item => item.keyboardPwdName.includes(globalStore.tempDeviceList[0]?.business?.user?.mobile))
         keyInfo.value = res.list.find(item => item.lockId == curentPwd.lockId);
         globalStore.setkeyInfo(keyInfo.value); // 提前设置 keyInfo
         console.log(keyInfo.value, "keyInfo.value ");
        })
        .catch(error => {
          console.error("获取密码列表时发生错误:", error);
          keyInfo.value = res.list[0];
          globalStore.setkeyInfo(keyInfo.value); 
          // 在此处处理错误情况
        });
    });
  });
});

const isOPenLockShow = ref(false); //是否开锁

/**
 * 开锁
 */
const openLockHandel = () => {
  const curentLock = globalStore.userInfo?.tenant_lock.filter(item => item.mobile == globalStore.userInfo?.mobile)[0];
  console.log(curentLock);
  if (curentLock?.status == 0) {
    return Taro.showToast({ title: "该锁已被禁用,请联系房东！", icon: "none" });
  }
  isOpenLock.value = true;
  Taro.showLoading({ title: "正在开锁" });
  requirePlugin("myPlugin", ({ controlLock }) => {
    const start = Date.now();
    // 控制智能锁
    controlLock({
      /* 控制智能锁方式 3 -开锁, 6 -闭锁 */
      controlAction: 3,
      lockData: unref(keyInfo).lockData,
      serverTime: Date.now(),
    }).then((res) => {
      console.log(res);
      if (res.errorCode == 0) {
        Taro.showToast({ icon: "success", title: "已开锁" });
        updateDianTime.value = new Date().getTime();
        isOPenLockShow.value = true;
        // toReadRecord({ keyInfo: keyInfo.value }).then(() => {
        //   updateDianTime.value = new Date().getTime();
        // });
        isOpenLock.value = false;
      } else {
        Taro.hideLoading();
        isOpenLock.value = false;
        isOPenLockShow.value = false;
        Taro.showToast({
          title: `开锁失败：${res.errorMsg}`,
          icon: "none",
          duration: 5000,
        });
      }
    });
  });
};

/**
 * 删除密码
 */
const delPwdHandel = async (keyboardPwdId) => {
  return new Promise((resolve, reject) => {
    request
      .post({
        url: "/v3/keyboardPwd/delete",
        LOCKYAPI: true,
        data: {
          lockId: unref(keyInfo).lockId,
          // clientId: "42362269013d48b3bdaa85c7b83f6356",
          clientId: "7946f0d923934a61baefb3303de4d132",
          accessToken: "91d30a70ba5bdc691d1004a5e6daef94",
          keyboardPwdId,
          deleteType: 1, //1-通过APP走蓝牙删除，不传默认1，必需先通过APP蓝牙删除后再调用该接口 2-通过网关或WiFi锁删除，如果是WiFi锁或有连接网关，则可以传2，直接调用该接口从锁里删除密码
          date: new Date().getTime(),
        },
      })
      .then((res) => {
        console.log(res);
        resolve(res.list);
      });
  });
};

const keyboardPwd = ref(); //普通自定义密码（永久）

/**
 * 查看密码
 */
const lookPwdHandel = async () => {
  const curentLock = globalStore.userInfo?.tenant_lock.filter(item => item.mobile == globalStore.userInfo?.mobile)[0];
  console.log(curentLock);
  if (curentLock?.status == 0) {
    return Taro.showToast({ title: "该锁已被禁用,请联系房东！", icon: "none" });
  }
  const list = await getLockPwdList({ keyInfo: keyInfo.value });
  if (list.length) {
    const idx = list.findIndex((item) => item.keyboardPwdType == "2");
    console.log(list[idx]);
    keyboardPwd.value = list[idx].keyboardPwd;
    Taro.navigateTo({
      url: `/pages/house/lockPwdAdmin/lockPwdAdmin?item=${JSON.stringify(
        list[idx]
      )}`,
    });
  } else {
    // 没有密码 创建一个默认密码
    Taro.showToast({
      title: "暂无密码,请联系房东设置初始密码！",
      icon: "none",
    });
    // await createPwdHandel({
    //   keyboardPwd: String(globalStore.userInfo.mobile).slice(0, 6),
    //   keyboardPwdName: globalStore.userInfo.nickname,
    //   keyInfo: keyInfo.value,
    // });
    // const list = await getLockPwdList({ keyInfo: keyInfo.value });
    // if (list.length) {
    //   const idx = list.findIndex((item) => item.keyboardPwdType == "2");
    //   console.log(list[idx]);
    //   keyboardPwd.value = list[idx].keyboardPwd;
    //   Taro.navigateTo({
    //     url: `/pages/house/lockPwdAdmin/lockPwdAdmin?item=${JSON.stringify(
    //       list[idx]
    //     )}`,
    //   });
    // }
  }
};

function timestampToDate(timestamp) {
  const date = new Date(timestamp);
  return `${date.getFullYear()}-${("0" + (date.getMonth() + 1)).slice(-2)}-${(
    "0" + date.getDate()
  ).slice(-2)} ${("0" + date.getHours()).slice(-2)}:${(
    "0" + date.getMinutes()
  ).slice(-2)}:${("0" + date.getSeconds()).slice(-2)}`;
}

const onDeviceDetail = (id) => {
  Taro.navigateTo({
    url: "/pages/tenant/deviceDetail/deviceDetail?id=" + id,
  });
};
</script>

<template>
  <!-- 快速入住 -->

  <!-- 水表 -->
  <template v-if="curentType == 3">
      <view class="water-container" style="height: 230rpx; color: #1452fd"  v-if="globalStore.waterTempDeviceList.length <= 0">
        <view>
          暂无水表~
        </view>
      </view>
      <template v-if="globalStore.waterTempDeviceList.length > 0" >
        <view @tap="onDeviceDetail(waterDevice?.id)" class="device-info flex flex-space-between flex-v-center" style="background: #f0fcfc;" :style="{justifyContent: (!conf?.show_basic_price && !conf?.show_service_price) ? 'space-evenly' : 'space-evenly' }">
                 <!-- 资费 -->
          <view id="zifei-box" @tap.stop="showZFWater = true">
            <text>资费</text>
            <text class="iconfont icon-wenhao"></text>
          </view>
          <view v-if="device?.agent?.type != 2" :style="itemStyle(1)">
            <view class="device-num">{{ Number(waterDevice?.total || 0).toFixed(2) }}</view>
            <view class="device-lab" :style="itemStyle(2)">总水量</view>
          </view>
          <view v-if="device?.agent?.type != 2 && confBusiness?.elec_show_type == 1" :style="itemStyle(1)">
            <view class="device-num">{{ waterDevice.du || 0 }}</view>
            <view class="device-lab" :style="itemStyle(2)">剩余水量(m³)</view>
          </view>
          <view v-if="device?.agent?.type == 2 || confBusiness?.elec_show_type == 2" :style="itemStyle(1)">
            <view class="device-num">{{ (waterDevice.du * (Number(waterDevice.price) + Number(device?.agent?.service))).toFixed(2) || 0 }}</view>
            <view class="device-lab" :style="itemStyle(2)">剩余金额(元)</view>
          </view>
          <!-- <view v-if="waterDevice?.agent?.type != 2 && !conf?.show_basic_price" :style="itemStyle(1)">
            <view class="device-num">{{ waterDevice.price }}</view>
            <view class="device-lab" :style="itemStyle(2)">单价(元/m³)</view>
          </view>
          <view v-if="conf?.show_basic_price" :style="itemStyle(1)">
            <view class="device-num">{{ waterDevice.basic_price }}</view>
            <view class="device-lab" :style="itemStyle(2)">基础水价(元/m³)</view>
          </view>
          <view v-if="conf?.show_service_price" :style="itemStyle(1)">
            <view class="device-num">{{ waterDevice.service_price || '0.00' }}</view>
            <view class="device-lab" :style="itemStyle(2)">服务费(元/m³)</view>
          </view> -->
        </view>
        <view
          style="padding: 10rpx 20rpx 0 20rpx"
          class="btm-b"
          v-if="globalStore.who == 'tenant'"
        >
          <view class="le">
            <image
              src="https://yimits.oss-cn-beijing.aliyuncs.com/images/fangzi.png"
              mode="scaleToFill"
              class="icon-s"
            />
              <text class=" font-28 _name_" @tap="lookAllHouseName(waterDevice.house.name)" v-if="waterDevice.house">{{
                waterDevice.house.name
              }}</text>
            <text class=" font-28 _name_" v-if="!waterDevice.house"
              >未绑定房源</text
            >
            <text
              class="close-box"
              v-if="(waterDevice.du || 0) < 0.01 && waterDevice.status == 2"
            >
              欠费断水
            </text>
          </view>
          <view class="ri" @tap="onDeviceDetail(waterDevice?.id)">
            <image
              src="https://yimits.oss-cn-beijing.aliyuncs.com/picss/qianbao-home.png"
              mode="scaleToFill"
            />
            <text>充值水表</text>
          </view>
        </view>
      </template>
  </template>

  <!-- 门锁 -->
  <template v-if="curentType == 2">
    <template v-if="keyInfo">
      <view class="lock-container">
        <view class="top" @tap.stop="lookPwdHandel">
          <text class="look">查看密码</text>
        </view>
        <view class="midle">
          <view class="dian">{{ keyInfo?.electricQuantity }}%</view>
          <view class="desc">剩余电量</view>
        </view>
      </view>
      <view class="bot-box">
        <image
          src="https://yimits.oss-cn-beijing.aliyuncs.com/images/fangzi.png"
          mode="scaleToFill"
          class="icon-s"
        />
          <text class=" font-28 _name_" @tap="lookAllHouseName(device.house.name)" v-if="device.house">{{
            device.house.name
          }}</text>
        <text class=" font-28 _name_" v-if="!device.house"
          >未绑定房源</text
        >

        <button class="button" @tap.stop="lockShow = true">一键开锁</button>
      </view>
    </template>
    <template v-else>
      <view class="lock-container" style="height: 230rpx; color: #1452fd">
        暂无门锁~
      </view>
    </template>
  </template>
  <!-- 电表 -->
  <template v-if="curentType == 4">
    <view class="water-container" style="height: 230rpx; color: #1452fd"  v-if="globalStore.tempDeviceList.length <= 0">
        <view>
          暂无电表~
        </view>
    </view>
    <template v-if="globalStore.tempDeviceList.length > 0">
      <view  @tap="onDeviceDetail(device?.id)" class="device-info flex flex-space-between flex-v-center" :style="{justifyContent: (!conf?.show_basic_price && !conf?.show_service_price) ? 'space-evenly' : 'space-evenly' }">
        <!-- 资费 -->
         <view id="zifei-box" @tap.stop="showZF = true">
          <text>资费</text>
          <text class="iconfont icon-wenhao"></text>
         </view>
        <view v-if="device?.agent?.type != 2" :style="itemStyle(1)">
          <view class="device-num">{{ Number(device?.total || 0).toFixed(2) }}</view>
          <view class="device-lab" :style="itemStyle(2)">总电量</view>
        </view>
        <view v-if="device?.agent?.type != 2 && confBusiness?.elec_show_type == 1" :style="itemStyle(1)" :class="{red: (device?.type == 5 || device?.type == 6) && device?.pay_mode == 2}">
          <view class="device-num">{{ device.du || 0 }}</view>
          <view class="device-lab" :style="itemStyle(2)">{{ itemText() }}</view>
        </view>
        <view v-if="device?.agent?.type == 2 || confBusiness?.elec_show_type == 2" :style="itemStyle(1)">
          <view class="device-num">{{ (device.du * (Number(device.price) + Number(device?.agent?.service))).toFixed(2) || 0 }}</view>
          <view class="device-lab" :style="itemStyle(2)">剩余金额(元)</view>
        </view>
        <!-- <view v-if="device?.agent?.type != 2 && !conf?.show_basic_price && device?.type != 5 && device?.type != 6" :style="itemStyle(1)">
          <view class="device-num">{{ device.price }}</view>
          <view class="device-lab" :style="itemStyle(2)">单价(元/度)</view>
        </view> -->
        <view v-if="device?.type == 5 || device?.type == 6" :style="itemStyle(1)" @tap.stop="priceShow = true">
          <view class="device-num">查询</view>
          <view class="device-lab" :style="itemStyle(2)">分段电价</view>
        </view>
        <!-- <view v-if="conf?.show_basic_price && device?.type != 5 && device?.type != 6" :style="itemStyle(1)">
          <view class="device-num">{{ device.basic_price }}</view>
          <view class="device-lab" :style="itemStyle(2)">基础电价(元/度)</view>
        </view>
        <view v-if="conf?.show_service_price && device?.type != 5 && device?.type != 6" :style="itemStyle(1)">
          <view class="device-num">{{ device.service_price || '0.00' }}</view>
          <view class="device-lab" :style="itemStyle(2)">服务费(元/度)</view>
        </view> -->
      </view>
      <view
        style="padding: 10rpx 20rpx 0 20rpx"
        class="btm-b"
        v-if="globalStore.who == 'tenant'"
      >
        <view class="le">
          <image
            src="https://yimits.oss-cn-beijing.aliyuncs.com/images/fangzi.png"
            mode="scaleToFill"
            class="icon-s"
          />
            <text class=" font-28 _name_" @tap="lookAllHouseName(device?.house.name)" v-if="device?.house">{{
              device?.house.name
            }}</text>
          <text class=" font-28 _name_" v-if="!device.house"
            >未绑定房源</text
          >
          <text
            class="close-box"
            v-if="(device?.du || 0) < 0.6 && device.status == 2"
          >
            欠费断电
          </text>
        </view>
        <view class="ri" @tap="onDeviceDetail(device?.id)" style="padding: 13rpx 35rpx;">
          <image
            src="https://yimidb.oss-cn-hangzhou.aliyuncs.com/mp/images/icon-dl.png"
            mode="scaleToFill"
            style="width: 16rpx;height: 23rpx;"
          />
          <text>充值电表</text>
        </view>
      </view>
    </template>
  </template>
  <!-- popup -->
  <MyPopup :show="lockShow" @close="lockShow = false">
    <template #content>
      <view class="contant-lock-pop" @tap.stop="">
        <view class="top-t">
          <text class="close" @tap.stop="lockShow = false">关闭</text>
          <text class="tit">蓝牙开锁</text>
          <text class="look" @tap.stop="lookPwdHandel">查看密码</text>
        </view>
        <view class="content">
          <image :src="dian" class="dianimg" mode="aspectFill"></image>
          <image
            :src="routeimg"
            class="routeimg"
            :class="isOpenLock ? 'route' : 'route paused'"
            mode="aspectFill"
          ></image>
          <image
            :src="yuanimg"
            class="yuanimg"
            mode="aspectFill"
            @tap.stop="openLockHandel"
          ></image>
          <view class="suo" @tap.stop="openLockHandel">
            <image
              :src="isOPenLockShow ? suoimgOpen : suoimg"
              class="suoimg"
              mode="aspectFill"
            ></image>
            <text>{{ isOPenLockShow ? "已开锁" : "点击开锁" }}</text>
          </view>
          <view class="bottom">
            <view class="dian"
              >您的门锁剩余电量
              <text>{{ keyInfo?.electricQuantity }}%</text></view
            >
            <view class="desc"
              >更新于{{ timestampToDate(updateDianTime) }}</view
            >
          </view>
        </view>
      </view>
    </template>
  </MyPopup>
  <QueryPricesPopUp :device="device" :show="priceShow" @close="priceShow = false" />
  <MyPopup :show="showZF" :isScrlloAnimation="false"  :titleStyle="{ color: '#000', fontSize: '36rpx' }" title="资费说明" @close="showZF = false">
    <template #content>
      <view class="recharge-confirm-zf">
        <view v-if="device?.agent?.type != 2 && device?.type != 5 && device?.type != 6 && confBusiness?.show_price != 0">
          <view class="device-lab">单价(元/度)</view>
          <view class="device-num">{{ device?.price }}</view>
        </view>
        <view v-if="conf?.show_basic_price && device?.type != 5 && device?.type != 6">
          <view class="device-lab">基础电价(元/度)</view>
          <view class="device-num">{{ device?.basic_price }}</view>
        </view>
        <view v-if="conf?.show_service_price && device?.type != 5 && device?.type != 6">
          <view class="device-lab">服务费(元/度)</view>
          <view class="device-num">{{ device?.service_price || '0.00' }}</view>
        </view>
      </view>
      <view class="rd-btn-box" style="border:none;">
        <button class="btn-add" style="margin-bottom: 20rpx;width: 88%;" @tap="showZF = false">好的</button>
      </view>
    </template>
  </MyPopup>
  <!-- 水表资费 -->
  <MyPopup :show="showZFWater" :isScrlloAnimation="false"  :titleStyle="{ color: '#000', fontSize: '36rpx' }" title="资费说明" @close="showZFWater = false">
    <template #content>
      <view class="recharge-confirm-zf">
          <view v-if="device?.agent?.type != 2 && device?.type != 5 && device?.type != 6 && confBusiness?.show_price != 0" style="background: transparent;">
            <view class="device-lab">单价(元/m³)</view>
            <view class="device-num">{{ waterDevice?.price }}</view>
          </view>
          <view v-if="conf?.show_basic_price" style="background: transparent;">
            <view class="device-lab">基础水价(元/m³)</view>
            <view class="device-num">{{ waterDevice?.basic_price }}</view>
          </view>
          <view v-if="conf?.show_service_price" style="background: transparent;">
            <view class="device-lab">服务费(元/m³)</view>
            <view class="device-num">{{ waterDevice?.service_price || '0.00' }}</view>
          </view>
      </view>
      <view class="rd-btn-box" style="border:none;">
        <button class="btn-add" style="margin-bottom: 20rpx;width: 88%;" @tap="showZFWater = false">好的</button>
      </view>
    </template>
  </MyPopup>
</template>

<style lang="less">
.red {
  >view {
    color: red !important;
  }
}
.btm-b {
  .ri {
    image {
      width: 50px;
      height: 50px;
      margin-right: 10px;
    }
    background: #1453fd;
    background: linear-gradient(90deg, #2A69F6 0%, #519BFD 100%);
    padding: 5px 20px;
    border-radius: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 26px;
    color: #fff;
  }
  ._name_ {
    width: 150px;
    font-size: 32px;
    display: inline-block;
    align-items: center;
    font-weight: 700;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    vertical-align: bottom;
  }
}
.contant-lock-pop {
  height: 75vh;
  width: 100%;
  background-color: #fff;
  border-top-left-radius: 32px;
  border-top-right-radius: 32px;
  .top-t {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 30px;
    .close {
      font-size: 24px;
      color: #9da5b3;
    }
    .tit {
      font-size: 36px;
      color: #000000;
    }
    .look {
      font-size: 24px;
      color: #1056fa;
    }
  }
  .content {
    position: relative;
    background: url("../../assets//picss/bottom-bg.png") no-repeat;
    background-size: 538px 538px;
    background-position: center 100px;
    height: 100%;
    margin-top: 30px;
    .bottom {
      position: absolute;
      left: 50%;
      bottom: 50px;
      transform: translateX(-50%);
      text-align: center;
      .dian {
        font-weight: 500;
        font-size: 24px;
        color: #000000;
        text {
          color: #196ffc;
        }
      }
      .desc {
        font-weight: 500;
        font-size: 24px;
        color: #9a9a9a;
      }
    }
    .dianimg {
      width: 671px;
      height: 188px;
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
    }
    .routeimg {
      width: 538px;
      height: 538px;
      position: absolute;
      left: 50%;
      top: 100px;
      transform: translateX(-50%);
      &.route {
        animation: route 5s linear infinite;
      }
      &.paused {
        animation-play-state: paused;
      }
      @keyframes route {
        0% {
          transform: translateX(-50%) rotate(0deg);
        }
        100% {
          transform: translateX(-50%) rotate(360deg);
        }
      }
    }
    .yuanimg {
      width: 308px;
      height: 308px;
      position: absolute;
      left: 50%;
      top: 215px;
      transform: translateX(-50%);
    }
    .suo {
      display: flex;
      flex-direction: column;
      align-items: center;
      position: absolute;
      left: 50%;
      top: 320px;
      transform: translateX(-50%);
      font-size: 18px;
      .suoimg {
        width: 48px;
        height: 59px;
        margin-bottom: 10px;
      }
    }
  }
}
.lock-container {
  position: relative;
  height: 159px;
  background: #f0f4ff;
  border-radius: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  .top {
    position: absolute;
    right: 20px;
    top: 20px;
    width: 130px;
    height: 40px;
    background: #f0f4ff;
    border-radius: 15px 13px 13px 15px;
    font-weight: 500;
    font-size: 18px;
    color: #2f61fd;
    border: 1px solid #1453fd;
    display: flex;
    justify-content: center;
    align-items: center;
    .look {
      font-size: 25px;
    }
  }
  .midle {
    text-align: center;
    .dian {
      font-weight: bold;
      font-size: 36px;
      color: #1353fd;
    }
    .desc {
      font-weight: 500;
      font-size: 18px;
      color: #7e869d;
    }
  }
}
.water-container {
  position: relative;
  height: 159px;
  background: #f0f4ff;
  border-radius: 20px;
  display: flex;
  justify-content: center;
  align-items: center; 
}
.bot-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 15px;
  padding: 5px 10px 0 10px;
  .icon-s {
    width: 32px;
    height: 32px;
    margin-right: 10px;
  }
  ._name_ {
    width: 150px;
    font-size: 32px;
    display: inline-block;
    align-items: center;
    font-weight: 700;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    vertical-align: bottom;
  }
  .button {
    width: 60%;
    height: 61px;
    background: #1353fd;
    border-radius: 20px;
    border: none;
    color: #fff;
    font-size: 26px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 0;
    &:active {
      opacity: 0.9;
    }
  }
}
.device-info {
  position: relative;
  background: #f0f4ff;
  border-radius: 20px;
  padding: 10px 25px;
  text-align: center;
  height: 159px;
  box-sizing: border-box;

  > view {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    width: 175px;
    height: 120px;
    background: #f0f4ff;
    border-radius: 20px;
    &#zifei-box {
      position: absolute;
      right: 0px;  
      top: 0px;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      flex-direction: row !important;
      width: 101px !important;
      height: 56px !important;
      background: #e0e8ff !important;
      border-radius: 0px 20px 0px 20px !important;
      color: #3C6AEC !important;
      font-size: 20px !important;
      padding: 0 !important;
      .iconfont {
        color: #3C6AEC; 
        font-size: 20px !important;
        margin: 2px 0 0 5px;
      }
    }
  }

  .device-num {
    font-size: 40rpx;
    color: #1452fd;
    font-family: Bahnschrift;
    font-weight: 400;
    margin-bottom: 10px;
    font-weight: 700;
    display: flex;
    align-items: center;

    .du-t {
      max-width: 150px;
      overflow: hidden;
      display: inline-block;
      text-overflow: ellipsis;
      white-space: nowrap;
      color: #1452fd !important;
      font-size: 40px !important;
      margin-top: 0 !important;
    }

    text {
      font-weight: 500;
      font-size: 18px;
      color: #4460b3;
      margin-top: 5px;
    }
  }

  .device-lab {
    font-size: 26rpx;
    font-family: OPPOSans;
    font-weight: 500;
    color: #879bd6;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 220px;
    // 单行溢出省略号
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}

.close-box {
  display: inline-block;
  font-size: 24px;
  color: #f2811a;
  background-color: #fce9d1;
  padding: 5px 10px;
  margin-right: 10px;
  border-radius: 6px;
  margin-left: 20px;
  vertical-align: middle;
}

.recharge-confirm-zf {
  padding: 65px;
  padding-top: 30px;
  >view {
    display: flex;
    margin-top: 32px;
    justify-content: space-between;
    .device-lab {
      font-size: 30px;
      color: #9BA3BA;
    }
    .device-num {
      font-size: 30px;
      color: #000000;
    }
  }
}
</style>
