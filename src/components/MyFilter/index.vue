<template>
  <view class="my-filter">
    <view class="flex flex-space-between flex-v-center">
      <template v-for="(opt, index) in options" :key="opt.key">
        <view class="my-filter-item" @tap="onChoose(opt, index)">
          {{ opt.name }}
          <image
            :src="openIndex === index ? IconArrowUp : IconArrowDown"
            class="icon-arrow-down"
          ></image>
        </view>
      </template>
    </view>

    <template v-for="(opt, index) in options" :key="opt.key">
      <view class="my-filter-popup" v-show="index === openIndex">
        <view class="my-filter-popup-mark" @tap="onClose"></view>
        <scroll-view
          :scroll-y="true"
          style="height: 600rpx"
          class="my-filter-item-child"
          :style="'top:' + 193 + 'rpx'"
        >
          <template v-for="child in opt.options" :key="child.value">
            <template v-if="child.key">
              <view class="my-filter-title">{{ child.name }}</view>
              <template v-for="d in child.options" :key="d.value">
                <view
                  class="my-filter-item-child-item"
                  @tap="onChooseOption(d, child.key)"
                >
                  <template v-if="openLabel === d.label"
                    ><image :src="IconRight" class="my-filter-icon-right"></image
                  ></template>
                  <text
                    :class="openLabel === d.label ? 'filter-item-selected' : ''"
                    >{{ d.label }}</text
                  >
                </view>
              </template>
            </template>
            <template v-else>
              <view
                class="my-filter-item-child-item"
                @tap="onChooseOption(child, opt.key)"
              >
                <template v-if="openLabel === child.label"
                  ><image :src="IconRight" class="my-filter-icon-right"></image
                ></template>
                <text
                  :class="
                    openLabel === child.label ? 'filter-item-selected' : ''
                  "
                  >{{ child.label }}</text
                >
              </view>
            </template>
          </template>
        </scroll-view>
      </view>
    </template>
  </view>
</template>

<script setup>
import { ref } from "vue";

const IconArrowDown = "https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-arrow-down.png";
const IconArrowUp = "https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-arrow-up.png";
const IconRight = "https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-right.png";

import { useGlobalStore } from "../../stores";
const globalStore = useGlobalStore();

const props = defineProps({
  options: {
    type: Array,
    required: true,
    default() {
      return [];
    },
  },
});

const openIndex = ref(-1);
const openLabel = ref("");
const onClose = () => {
  console.log("on mark click");
  openIndex.value = -1;
};
const onChoose = (item, index) => {
  openIndex.value = index;
};

const emit = defineEmits(["change"]);
const onChooseOption = (child, opt) => {
  openLabel.value = child.label;
  openIndex.value = -1;
  emit("change", child, opt);
};
</script>
<style>
/*search*/
.my-filter {
  background-color: #1352fd;
  height: 52px;
  padding: 20px 60px 18px 60px;
}
.my-filter-item {
  text-align: center;
  color: #ffffff;
  font-size: 30px;
}
.icon-arrow-down {
  width: 16px;
  height: 9px;
  vertical-align: middle;
  margin-left: 10px;
}
.my-filter-item-child {
  position: absolute;
  top: 70px;
  left: 0;
  width: 100%;
  background-color: #ffffff;
  z-index: 10002;
  box-shadow: 0 10px 5px 2px rgba(0, 0, 0, 0.1);
}
.my-filter-item-child-item {
  border-bottom: 1px solid #dfdfdf;
  padding: 20px 40px;
}
.my-filter-popup {
  position: absolute;
  height: 100%;
  width: 100%;
  left: 0;
  top: 0;
}
.my-filter-popup-mark {
  position: absolute;
  height: 100%;
  width: 100%;
  left: 0;
  top: 0;
  z-index: 10001;
}
.my-filter-icon-right {
  width: 30px;
  height: 30px;
  display: inline-block;
  margin-right: 20px;
}
.filter-item-selected {
  color: #3f83ff;
}

.my-filter-title {
  padding: 20rpx;
}
</style>
