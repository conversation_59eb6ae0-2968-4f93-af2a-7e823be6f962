<template>
  <view class="my-icon"><image :src="'https://yimidb.oss-cn-hangzhou.aliyuncs.com/mp/images/' + icon + '.png'" :style="{width: width, height: height}" class="my-icon" mode="widthFix"></image></view>
</template>
<script setup>
  const props = defineProps({
    icon: {
      type: String,
      required:false,
      default: ''
    },
    width: {
      type: String,
      required:false,
      default: '30rpx',
    },
    height: {
      type: String,
      required:false,
      default: '30rpx',
    }
  })

</script>
<style>
.my-icon {
  display: inline-block;
  vertical-align: middle;
}
</style>