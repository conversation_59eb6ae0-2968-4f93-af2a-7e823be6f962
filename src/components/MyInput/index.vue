<template>
  <view class="my-input">
    <view class="my-input-prefix" v-if="$slots.prefix"><slot name="prefix"></slot></view>
    <view class="my-input-prefix" v-if="!$slots.prefix && prefix">{{prefix}}</view>
    <view class="my-input-content">
      <slot name="content"></slot>
    </view>
    <view class="my-input-suffix" v-if="$slots.suffix"><slot name="suffix"></slot></view>
    <view class="my-input-suffix" v-if="!$slots.suffix && suffix">{{suffix}}</view>
  </view>
</template>

<script setup>
  const props = defineProps({
    val: {
      type: String,
      required: false,
      default: ''
    },
    placeholder: {
      type: String,
      required:false,
      default: ''
    },
    prefix: {
      type: String,
      required:false,
      default: ''
    },
    suffix: {
      type: String,
      required:false,
      default: ''
    },
    type: {
      type: String,
      required:false,
      default: 'text'
    }
  })

  const emit = defineEmits(['change'])

  const onChange = (e) => {
    emit('change', e.detail.value)
  }

</script>
<style>
.my-input {
  border-bottom: 1px solid #B9C4D4;
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}
.my-input-prefix {
  width: 120px;
  text-align: left;
  font-size: 28px;
  line-height: 83px;
  font-family: OPPOSans;
  font-weight: 500;
}
.my-input-content {
  flex: 1;
}
.my-input-m {
  font-size: 28px;
}
.my-input-m:-ms-input-placeholder {
  color: #E9F2FF;
}
.my-input-suffix {
  max-width: 160px;
  min-width: 80px;
  text-align: right;
  line-height: 83px;
  font-size: 28px;
}
</style>
