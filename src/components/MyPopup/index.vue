<template>
  <view class="my-popup" v-show="show">
    <view class="my-popup-mark" @tap.stop="onClose"></view>
    <view class="my-popup-content" :class="'my-popup-position-' + position">
      <view class="my-popup-title" v-if="title && isScrlloAnimation">
       <view :style="titleStyle" :class="{'my-popup-title-animation': isScrlloAnimation}"> 
        <view :style="{width: textWidth + 'rpx'}">{{title}}</view>
       </view>
       <slot name="header"></slot>
      </view>
      <view class="my-popup-title" v-if="title && !isScrlloAnimation">
        {{ title }}
       <slot name="header"></slot>
      </view>
      <text :id="props.id" :style="measureStyle">{{ title }}</text>
      <slot name="content"></slot>
    </view>
  </view>
</template>
<script setup>
import { watchEffect,ref,nextTick,computed,onMounted,watch } from 'vue';
import { pxToRpx } from '@/utils/index'
import Taro from '@tarojs/taro'
  const props = defineProps({
    position: {
      type: String,
      required: false,
      default: 'bottom'
    },
    show: {
      type: Boolean,
      required: false,
      default: false
    },
    title: {
      type: String,
      required: false,
      default: ''
    },
    titleStyle: {
      type: Object,
      required: false,
      default: () => ({})
    },
    isScrlloAnimation: {
      type: Boolean,
      required: false,
      default: false
    },
    id: {
      type: String,
      required: false,
      default: 'showRechargeTip'
    }
  })

  const emit = defineEmits(['close'])
  
  const textWidth = ref(1224);

  const measureStyle = computed(() => ({
    position: 'absolute',
    visibility: 'hidden',
    whiteSpace: 'nowrap',
    left: '-9999px',
    top: '-9999px',
    fontSize: `${props.titleStyle?.fontSize || '32rpx'}`,
  }));

watch(() => props.show, (val) => {
    if(!val) return
    nextTick(() => {
      Taro.createSelectorQuery().select('#'+props.id).boundingClientRect(function(rect){
        rect.left    // 节点的左边界坐标
        rect.right   // 节点的右边界坐标
        rect.top     // 节点的上边界坐标
        rect.bottom  // 节点的下边界坐标
        rect.width   // 节点的宽度
        rect.height  // 节点的高度
          }).exec((res) => {
            console.log(res[0].width, "queryline");
            console.log(pxToRpx(res[0].width), "queryline");
            textWidth.value = pxToRpx(res[0].width)
        })
    })
  })

  onMounted(() => {
   
  })


  const onClose = () => {
    console.log('onClose')
    emit('close')
  }

</script>
<style lang="scss">
.my-popup {
  position: fixed;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  z-index: 99999;
}
.my-popup-mark {
  width: 100%;
  height: 100%;
  background-color: rgba(0,0,0, 0.6);
  z-index: 99998;
  position: fixed;
}
.my-popup-title {
  font-size: 36px;
  font-family: OPPOSans;
  font-weight: 500;
  color: #000000;
  text-align: center;
  padding:  30px 0;
}
.my-popup-title-animation {
  width: 82%;
  overflow: hidden;
  white-space: nowrap;
  position: relative;
  height: 60px;
  margin-left: 50%;
  transform: translateX(-50%);

  /* 匀速 */
  view {
    white-space: nowrap;
    transform: translateX(50%);
    animation: my-popup-title-animation 10s linear infinite;
  }
}
@keyframes my-popup-title-animation {
  0% {
    transform: translateX(50%);
  }
  100% {
    transform: translateX(-100%);
  }
}
.my-popup-content {
  width: 100%;
  background: #FFFFFF;
  z-index: 99999;
  position: fixed;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.my-popup-position-left {
  left:  0;
}
.my-popup-position-top {
  top:  0;
  border-radius: 0 0 30px 30px;
}
.my-popup-position-right {
  right: 0;
}
.my-popup-position-bottom {
  bottom: 0;
  border-radius: 30px 30px 0 0;
}
</style>
