<template>
  <view
    class="my-search search-home"
    :style="{ backgroundColor: props.dark ? '#1452fd' : '#fff' }"
  >
    <view class="my-search-box flex flex-v-center">
      <view
        class="switch-box"
        v-if="isShowSwitchTag"
        @tap="emit('onSwitchTag')"
      >
        <text class="tit">{{ props.switchText }}</text>
        <text
          class="iconfont icon-xiajiantou"
          :style="{
            transform: showDialog ? 'rotate(180deg)' : 'rotate(0deg)',
            display: 'inline-block',
            transition: 'all 0.3s',
          }"
        ></text>
        <view class="notice" v-if="false">
          <view class="con">1</view>
        </view>
      </view>
      <view
        :style="{ display: 'flex', width: '100%' }"
        :class="isAlipay ? 's-p-17-a' : 's-p-17'"
      >
        <view class="my-search-input"
          ><input
            class="_my-search-input"
            :placeholder="placeholder"
            confirmType="search"
            @confirm="onChange"
            @input="onIpnut"
            :cursor="posCur"
        /></view>
        <image
          @tap="onChangeClick"
          :src="IconSearch"
          style="width: 38rpx; height: 38rpx"
          mode="aspectFill"
        ></image>
      </view>
    </view>
  </view>
</template>

<script setup>
const props = defineProps({
  placeholder: {
    type: String,
    required: false,
    default: "",
  },
  isShowSwitchTag: {
    type: Boolean,
    required: false,
    default: false,
  },
  showDialog: {
    type: Boolean,
    required: false,
    default: false,
  },
  switchText: {
    type: String,
    required: false,
    default: "选择楼幢",
  },
  dark: {
    type: Boolean,
    required: false,
    default: false,
  },
});

import { ref, unref } from "vue";
const IconSearch =
  "https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-search.png";

const isAlipay = process.env.TARO_ENV === "alipay";

const emit = defineEmits(["search", "onSwitchTag"]);

const value = ref("");

const posCur = ref(-1)

const bindReplaceInput = (e) => {
  let pos = e.detail.cursor
  posCur.value = pos
}

const onChange = (e) => {
  emit("search", e.detail.value);
};

const onIpnut = (e) => {
  bindReplaceInput(e)
  value.value = e.detail.value;
  if (!e.detail.value) {
    emit("search", "");
  }
};

const onChangeClick = () => {
  console.log(unref(value));
  emit("search", unref(value));
};
</script>
<style lang="scss">
/*search*/
.my-search {
  position: relative;
  background-color: #1452fd;
  height: 72px;
  padding: 18px;
}
.search-home {
  background-color: #fff;
}

.switch-box {
  // position: absolute;
  // left: 18px;
  // top: 18px;
  font-size: 28px;
  color: #fff;
  flex-shrink: 0;
  // z-index: 1;
  background-color: #1452fd;
  border-top-left-radius: 36px;
  border-bottom-left-radius: 36px;
  height: 79px;
  width: 155px;
  line-height: 80px;
  padding-left: 20px;
  display: flex;
  align-items: center;
  padding-right: 10px;
  .tit {
    width: 120px;
    // 单行文本溢出显示省略号
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .iconfont {
    font-size: 30px;
    // margin-left: 10px;
  }
  .notice {
    position: absolute;
    top: -50px;
    right: -20px;
    width: 98px;
    height: 98px;
    background-size: 100%;
    background: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGZpbGw9Im5vbmUiIHdpZHRoPSI0MiIgaGVpZ2h0PSI0NiI+PGRlZnM+PGZpbHRlciBpZD0icHJlZml4X19hIiBmaWx0ZXJVbml0cz0idXNlclNwYWNlT25Vc2UiIGNvbG9yLWludGVycG9sYXRpb24tZmlsdGVycz0ic1JHQiIgeD0iMSIgeT0iMSIgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIj48ZmVGbG9vZCBmbG9vZC1vcGFjaXR5PSIwIiByZXN1bHQ9IkJhY2tncm91bmRJbWFnZUZpeCIvPjxmZUNvbG9yTWF0cml4IGluPSJTb3VyY2VBbHBoYSIgdmFsdWVzPSIwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAxMjcgMCIvPjxmZU9mZnNldCBkeT0iMiIvPjxmZUdhdXNzaWFuQmx1ciBzdGREZXZpYXRpb249IjIiLz48ZmVDb2xvck1hdHJpeCB2YWx1ZXM9IjAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAuMDc5OTk5OTk4MjExODYwNjYgMCIvPjxmZUJsZW5kIGluMj0iQmFja2dyb3VuZEltYWdlRml4IiByZXN1bHQ9ImVmZmVjdDFfZHJvcFNoYWRvdyIvPjxmZUJsZW5kIGluPSJTb3VyY2VHcmFwaGljIiBpbjI9ImVmZmVjdDFfZHJvcFNoYWRvdyIgcmVzdWx0PSJzaGFwZSIvPjwvZmlsdGVyPjwvZGVmcz48ZyBmaWx0ZXI9InVybCgjcHJlZml4X19hKSIgc3R5bGU9Im1peC1ibGVuZC1tb2RlOnBhc3N0aHJvdWdoIj48cGF0aCBkPSJNNSAzMy40NzZDNSAzNC4zMTggNS42ODIgMzUgNi41MjQgMzVIMjFjOC44MzcgMCAxNi03LjE2MyAxNi0xNlMyOS44MzcgMyAyMSAzIDUgMTAuMTYzIDUgMTl2MTQuNDc2eiIgZmlsbD0iIzI4NzhGRiIvPjxwYXRoIGQ9Ik00IDMzLjQ3NlYxOXEwLTMuNDU4IDEuMzM2LTYuNjE3IDEuMjktMy4wNTEgMy42NDMtNS40MDQgMi4zNTMtMi4zNTIgNS40MDQtMy42NDNRMTcuNTQyIDIgMjEgMnQ2LjYxNyAxLjMzNnEzLjA1MSAxLjI5IDUuNDA0IDMuNjQzIDIuMzUyIDIuMzUzIDMuNjQzIDUuNDA0UTM4IDE1LjU0MiAzOCAxOXQtMS4zMzYgNi42MTdxLTEuMjkgMy4wNTEtMy42NDMgNS40MDQtMi4zNTMgMi4zNTItNS40MDQgMy42NDNRMjQuNDU5IDM2IDIxIDM2SDYuNTI0cS0xLjA0NiAwLTEuNzg1LS43NFE0IDM0LjUyMyA0IDMzLjQ3N3ptMiAwcTAgLjIxNy4xNTMuMzcuMTU0LjE1NC4zNy4xNTRIMjFxMy4wNTIgMCA1LjgzOC0xLjE3OCAyLjY5Mi0xLjEzOSA0Ljc2OS0zLjIxNSAyLjA3Ni0yLjA3NyAzLjIxNS00Ljc2OVEzNiAyMi4wNTIgMzYgMTl0LTEuMTc4LTUuODM4cS0xLjEzOS0yLjY5Mi0zLjIxNS00Ljc2OS0yLjA3Ny0yLjA3Ni00Ljc2OS0zLjIxNVEyNC4wNTIgNCAyMSA0dC01LjgzOCAxLjE3OHEtMi42OTIgMS4xMzktNC43NjkgMy4yMTUtMi4wNzYgMi4wNzctMy4yMTUgNC43NjlRNiAxNS45NDggNiAxOXYxNC40NzZ6IiBmaWxsPSIjRkZGIi8+PC9nPjwvc3ZnPg==)
      no-repeat;
    background-position: center;
    .con {
      position: absolute;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      font-size: 30px;
      top: -5px;
    }
  }
}
.my-search-box {
  background-color: #ffffff;
  border-radius: 36px;
  border: 1px solid #1452fd;
}
.s-p-17 {
  padding: 17px;
}
.s-p-17-a {
  padding: 0 17px;
}
.my-search-icon {
  width: 38px;
}
.my-search-input {
  flex: 1;
  margin-left: 10px;
}
._my-search-input {
  font-size: 30px;
}
._my-search-input:-ms-input-placeholder {
  color: #c0cbe7;
}
.icon-search {
  width: 38px;
  height: 38px;
  display: block;
}
</style>
