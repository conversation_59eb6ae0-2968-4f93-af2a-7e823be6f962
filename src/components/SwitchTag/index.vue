<script setup>
import { ref } from "vue";
import request from "@/utils/request";
import Taro, { useDidShow, useLoad } from "@tarojs/taro";
import YModal from "@/components/YModal";
import emtyImg from "@/assets/pic/emty.png";
import { watch } from "vue";
import { AtCheckbox } from 'taro-ui-vue3/lib'
import { useGlobalStore } from "@/stores";
const globalStore = useGlobalStore();


const show = ref(false);

const classList = ref([]);

const checkedList = ref([]);

const props = defineProps({
  showDialog: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(["close", "confirm"]);

const handleChange = (value) => {
  console.log(value);
  if (value.length == 0) {
    classList.value = classList.value.map((o,i) => {
        return {
          ...o,
          disabled: false,
        }
    });
    return checkedList.value = []
  }
  if (value[0] == 'all') {
    classList.value = classList.value.map((o,i) => {
      if (i !== 0) {
        return {
          ...o,
          disabled: true,
        }
      } else {
        return {
          ...o,
          disabled: false,
        }
      }
    });
  } else {
    classList.value[0].disabled = true;
  }
  checkedList.value = value;
}

watch(
  () => props.showDialog,
  (newVal) => {
    console.log(newVal);
    show.value = newVal;
  }
);

watch(
  () => show.value,
  (newVal) => {
    console.log(newVal);
    if (!newVal) {
      emit("close");
      checkedList.value = []
    } else {
      getList();
    }
  }
);

useDidShow(() => {
  if(!show.value) return
  getList();
})

// 获取分类列表
const getList = () => {
  request
    .get({
      url: "houseClass/list",
      data: {
        type: "edit",
      },
    })
    .then((res) => {
      if (res.code != 200) return;
      console.log(res);
      if (res.data.length <= 0) return classList.value = [];
      classList.value = [
        {
          id: "",
          label: "全部",
          houses: [],
          value: 'all'
        },
        ...res.data.map(o => {
          return {
            ...o,
            value: o.id,
            label: `${o.label} (${o.houses.length})`,
          }
        }),
      ];
      handleChange(checkedList.value);
    });
};

const yesHandel = () => {
  console.log(checkedList.value);
  if (checkedList.value.length <= 0) {
    return Taro.showToast({
      title: "请先勾选楼栋！",
      icon: "error",
    })
  }
  const obj = {
    ids: checkedList.value.some(o => o == 'all') ? '' : checkedList.value.join(','),
    labels: checkedList.value.map(o => {
      const item = classList.value.find(i => i.id == o);
      return !item?.label || item.label == 'all' ? '全部' : item.label.replace(/\s*\(.*?\)\s*/g, "");
    }),
  }
  emit("confirm", obj);
  emit("close");
  console.log(obj,"obj");
  checkedList.value = [];
}

const createHandel = () => {
  Taro.navigateTo({
    url: "/pages/other/houseTagAdmin/houseTagAdmin",
  });
};

const itemClickHandel = (item) => {
  console.log(item);
  emit("confirm", item);
  emit("close");
  // Taro.navigateTo({
  //   url: `/pages/other/houseTagAdmin/houseTagAdmin?classId=${item.id}`,
  // });
}
</script>

<template>
  <view class="switch-tag-index">
    <YModal
      title="选择楼幢"
      confirmText="no"
      :show="show"
      isTagSetting
      @close="show = false"
      :showCancel="false"
      maskClose
      :bodyStyle="{
        backgroundColor: '#fff',
        paddingBottom: '30rpx',
        width: '650rpx',
        borderRadius: '40rpx',
        zIndex: 9999999999,
      }"
    >
      <template #content>
        <view class="tip-container">
          <view v-if="classList.length <= 0">
            <image :src="emtyImg" class="img" mode="aspectFit"></image>
            <view class="emty-txt">暂无分组</view>
          </view>

          <view class="list" v-if="classList.length > 0">
            <at-checkbox
              :options="classList"
              :selectedList="checkedList"
              @change="handleChange"
            />
            <!-- <view
              class="item"
              v-for="(item, idx) in classList"
              :key="item.id"
              @tap="itemClickHandel(item)"
              :style="{
                borderColor: classList.length - 1 === idx ? '#fff' : '#f4f4f4',
              }"
            >
              <text>{{ item.label }} <text v-if="item?.id">({{ item.houses.length }})</text></text>
              <text class="iconfont icon-youjiantou1"></text>
            </view> -->
          </view>

        </view>
        <!-- <view v-if="classList.length > 0" style="color: #1352FD;text-align: center;padding-top: 10rpx;" @tap="emit('close')">取消</view> -->
         <view class="btn-box" v-if="!globalStore?.isAdmin">
           <view class="btn" @tap="createHandel">去创建</view>
           <view class="btn" @tap="yesHandel">确认</view>
         </view>
         <view class="btn-box2" v-else>
           <view class="btn" @tap="yesHandel">确认</view>
         </view>
      </template>
    </YModal>
  </view>
</template>

<style lang="scss">
@import "taro-ui-vue3/dist/style/components/checkbox.scss";
.switch-tag-index {
  .btn-box ,.btn-box2{
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .btn-box2 {
    justify-content: center;
    .btn {
      width: 100%;
      border-radius: 0;
    }
  }
  .btn {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 48%;
      height: 98px;
      background: #1352fd;
      border-radius: 20px;
      border: 2px solid #2e4dcf;
      color: #fff;
      margin-top: 20px;
    }
  .tip-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    // justify-content: center;
    height: 100%;
    max-height: 50vh;
    overflow-y: auto;
    .img {
      width: 195rpx;
      height: 151rpx;
      margin-top: 30px;
    }
    .emty-txt {
      color: #d8d8d8;
      margin-bottom: 70px;
      text-align: center;
    }

    .list {
      width: 100%;
      padding-bottom: 50px;
      margin-top: 40px;
      .item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        height: 98px;
        border-bottom: 1px solid #c9c2c2;
        color: #000;
        padding-left: 20px;
        box-sizing: border-box;
        padding-right: 20px;
        font-size: 38px;
        // font-weight: 700;
        .iconfont {
          font-size: 30px;
          color: #c9c2c2;
        }
      }
    }
  }
}
</style>
