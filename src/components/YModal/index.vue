<script setup>
import Taro, { useDidShow, useLoad } from "@tarojs/taro";
import { watch } from 'vue';
import { useGlobalStore } from "@/stores";
const globalStore = useGlobalStore();

const props = defineProps({
  title: String,
  confirmText: String,
  cancelText: String,
  showCancel: {
    type: Boolean,
    default: false,
  },
  show: {
    type: Boolean,
    required: true,
    default: false,
  },
  maskClose: {
    type: Boolean,
    default: false,
  },
  bodyStyle: {
    type: Object,
    default: () => {},
  },
  cancelStyle: {
    type: Object,
    default: () => {},
  },
  maskStyle: {
    type: Object,
    default: () => {},
  },
  isTagSetting: {
    type: Boolean,
    default: false,
  },
  showClose: {
    type: Boolean,
    default: true,
  }
});

watch(() => props.show, (newVal) => { 
  try {
    if (process.env.TARO_ENV === 'weapp') {
      if(newVal) {
        wx.setPageStyle({
          style: {
            overflow: 'hidden' // ‘auto’
          }
        })
      } else {
        wx.setPageStyle({
          style: {
            overflow: 'auto' // ‘auto’
          }
        })
      }
    } 
  } catch (e) {
    console.log(e);
  }
})

const emit = defineEmits(["confirm", "close"]);

const confirmHandel = () => {
  emit("confirm");
};

const cancelHandel = () => {
  emit("cancel");
  emit("close");
}

const maskHandel = (e) => {
  if(e.mpEvent.target.id != 'mask') return
  if (props.maskClose) {
    emit('close')
  }
}

const toTagAdmin = () => {
  Taro.navigateTo({
    url: "/pages/other/houseTagAdmin/houseTagAdmin",
  });
}
</script>

<template>
  <view class="y-modal-mask" :style="maskStyle" v-if="props.show" @tap.stop="maskHandel" id="mask">
    <view class="y-modal-container" :style="bodyStyle">
      <view class="title" v-if="props.title !== 'no'"> 
        <view class="setting"  v-if="props.isTagSetting && !globalStore.isAdmin" @tap="toTagAdmin">
            <view class="iconfont icon-setting"></view>
            <view>设置</view>
        </view>
        <text>{{ props.title }}</text>
        <text class="iconfont icon-close" v-if="showClose" @tap="emit('close')"></text>
      </view>
      <view class="content">
        <slot name="content"></slot>
      </view>
      <view class="bottom-utils" v-if="props.confirmText !== 'no'" >
        <view class="confirm" v-if="!$slots?.confirm" @tap="confirmHandel"
          >{{ props.confirmText }}
        </view>
        <slot name="confirm" v-else></slot>
        <view class="cancel" :style="props.cancelStyle" v-if="props.showCancel"
          ><text @tap="cancelHandel">{{
            props.cancelText || "取消"
          }}</text></view
        >
      </view>
    </view>
  </view>
</template>

<style lang="scss">
.y-modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
  .icon-close {
    position: absolute;
    right: 20px;
    top: 0px;
    font-size: 45px;
    font-weight: normal;
  }
  .setting {
    position: absolute;
    left: 20px;
    top: 42px;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 168px;
    height: 43px;
    background: #3562e4;
    border-radius: 10px;
    transition: all 0.3s;
    .iconfont {
      margin-right: 10px;
      color: #ffffff;
    }
    view {
      font-size: 30rpx;
      color: #ffffff;
      line-height: 38px;
      text-align: center;
      font-weight: 700;
    }
  }
  // .icon-setting {
  //   font-size: 32px;
  //   color: #000;
  //   // margin-left: 160px;
  //   position: absolute;
  //   right: 85px;
  //   top: 0px;
  // }
  .y-modal-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 600px;
    min-height: 320px;
    background-color: #fff;
    border-radius: 10px;
    color: #000;
    padding: 20px;
    z-index: 1000;
    box-sizing: border-box;
    animation: open 0.3s forwards;
    padding-bottom: 30px;
    @keyframes open {
      0% {
        opacity: 0;
        transform: translate(-50%, -30%);
      }
      100% {
        opacity: 1;
        transform: translate(-50%, -50%);
      }
    }
    .title {
      text-align: center;
      font-size: 35px;
      color: #000;
      height: 80px;
      line-height: 80px;
      font-weight: 700;
    }
    .bottom-utils {
      .confirm {
        width: 70%;
        height: 80px;
        background-color: #1658fd;
        border-radius: 10px;
        text-align: center;
        line-height: 80px;
        color: #fff;
        font-size: 28px;
        margin: 0 auto;
        &:active {
          background-color: blue;
        }
      }
      .cancel {
        color: #999;
        font-size: 28px;
        text-align: center;
        margin-top: 15px;
      }
    }
  }
}
</style>
