import {defineComponent, onMounted, watch, h, mergeProps} from "vue";
import {View} from "@tarojs/components";
import {delayQuerySelector} from "../../utils/common";
const AtSwipeActionOptions = defineComponent({
  name: "AtSwipeActionOptions",
  props: {
    componentId: {
      type: String,
      default: ""
    },
    options: {
      type: Array,
      default: []
    },
    onQueryedDom: {
      type: Function,
      default: () => ({width}) => {
      }
    }
  },
  setup(props, {attrs, slots}) {
    watch(() => props.options, (options, preOptions) => {
      if (options !== preOptions) {
        trrigerOptionsDomUpadte();
      }
    });
    function trrigerOptionsDomUpadte() {
      delayQuerySelector(this, `#swipeActionOptions-${props.componentId}`, 100).then((res) => {
        const arr = [...res];
        if (<PERSON><PERSON><PERSON>(arr[0])) {
          props.onQueryedDom(arr[0]);
        }
      });
    }
    onMounted(() => {
      trrigerOptionsDomUpadte();
    });
    return () => h(View, mergeProps(attrs, {
      id: `swipeActionOptions-${props.componentId}`,
      class: "at-swipe-action__options"
    }), {default: () => slots.default && slots.default()});
  }
});
var options_default = AtSwipeActionOptions;
export {
  options_default as default
};
