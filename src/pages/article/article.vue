<script setup>
import { ref } from 'vue'
import Taro, { useDidShow, useDidHide, useReady, useLoad, usePullDownRefresh, useShareAppMessage } from '@tarojs/taro'
import request from '@/utils/request'
import { useGlobalStore } from '@/stores'

import './article.scss'

const isAp = process.env.TARO_ENV === 'alipay'


const globalStore = useGlobalStore()

const detail = ref({
  title: '',
  content: ''
})
const key = ref('')

const getDetail = () => {
  request.get({
    url: 'article/detail?article_key=' + key.value,
  }).then(res => {
    detail.value = {
      title: res.data.title,
      content: res.data.content
    }
    if (isAp) {
      let wrappedStr = `<div>${res.data.content.replaceAll('<!--[endif]-->','').replaceAll('<!-- [if !supportLists]-->','')}</div>`
      detail.value = {
        title: res.data.title,
        content: wrappedStr
      }
      }
    Taro.setNavigationBarTitle({
      title: res.data.title,
    })
  })
}

useLoad((options) => {
  key.value = options.key
  getDetail()
})

</script>
<template>
  <view class="container">
    <rich-text :nodes="detail.content"></rich-text>
  </view>
</template>
