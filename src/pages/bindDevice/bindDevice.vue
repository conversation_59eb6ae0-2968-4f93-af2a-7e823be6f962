<template>
  <view class="house-container">
    <view class="house-form">
      <view class="device flex">
        <view class="device-img">
          <MyIcon v-if="deviceAll?.type != 2" icon="img-device-large" width="205rpx" height="242rpx"></MyIcon>
         <image style="width: 205rpx;height: 242rpx;margin-top: -8rpx;" v-else :src="shuiImg" mode="aspectFill"></image>
        </view>
        <view class="device-info2">
          <view class="device-type"> {{ deviceAll?.type == 2 ?  rederText(deviceAll?.net_type,deviceAll?.type) : rederText(deviceAll?.net_type,deviceAll?.type) }}  </view>
          <view class="device-sn">设备编号：{{device.sn}}</view>
        </view>
      </view>

      <!-- <MyCell prefix="所在地" required arrow suffix="请选择"></MyCell> -->
      <MyCell prefix="小区名" required arrow :suffix="formState.estate_name ? '' : '请选择'" @tap="onChooseLocation" align="right">
        <template #content>{{ formState.estate_name }}</template>
      </MyCell>
      <MyCell
        prefix="分组"
        required
        v-if="globalStore.isAdmin"
        suffixWidth="200"
        arrow
        :suffix="selectClassNames.join(',') ? '' : '请选择分组'"
        @tap="classShow = true"
        align="right"
      >
        <template #content>{{ selectClassNames.join(',') }}</template>
      </MyCell>
      <MyCell prefix="房源名" required align="right">
        <template #content>
          <input name="name" :cursor="posCur" @input="bindReplaceInput" @focus="utilsClass = ''" @blur="utilsClass = 'footer-fixed'"  v-model="formState.name" cursor-spacing="120" placeholder="几栋几单元几室" />
        </template>
      </MyCell>
      <MyCell v-if="deviceAll?.type != 5 && deviceAll?.type != 6" :prefix="deviceAll?.type == 2 ? '基础水价' : '基础电价'" required align="right" :suffix="deviceAll?.type == 2 ? '元/m³' : '元/度'">
        <template #content>
          <input type="digit"  :cursor="posCur" @input="bindReplaceInput"  @focus="utilsClass = ''" @blur="utilsClass = 'footer-fixed'" name="price" cursor-spacing="120" v-model="device.price" placeholder="" />
        </template>
      </MyCell>
      <MyCell prefix="服务费" v-if="deviceAll?.type != 5 && deviceAll?.type != 6"  align="right" :suffix="deviceAll?.type == 2 ? '元/m³' : '元/度'">
        <template #content>
          <input type="digit" :cursor="posCur" @input="bindReplaceInput" @focus="utilsClass = ''" @blur="utilsClass = 'footer-fixed'" name="price" cursor-spacing="120"  v-model="ServicePrice" placeholder="输入服务费" />
        </template>
      </MyCell>
      <!-- <MyCell prefix="设备费"  align="right" suffix="元/度" v-if="deviceAll?.agent?.type == 2">
        <template #content>
          <text style="color: #333;"> {{ deviceAll?.agent?.service }} </text>
        </template>
      </MyCell> -->

      <!-- 谷峰表电价 -->
      <MyCell v-if="deviceAll?.type == 5 || deviceAll?.type == 6" prefix="尖峰电价" required align="right" suffix="元/度">
        <template #content>
          <input type="digit"  :cursor="posCur" @input="bindReplaceInput"  @focus="utilsClass = ''" @blur="utilsClass = 'footer-fixed'" name="price" cursor-spacing="120" v-model="device.coef4" placeholder="" />
        </template>
      </MyCell>
      <MyCell v-if="deviceAll?.type == 5 || deviceAll?.type == 6" prefix="高峰电价" required align="right" suffix="元/度">
        <template #content>
          <input type="digit"  :cursor="posCur" @input="bindReplaceInput"  @focus="utilsClass = ''" @blur="utilsClass = 'footer-fixed'" name="price" cursor-spacing="120" v-model="device.coef3" placeholder="" />
        </template>
      </MyCell>
      <MyCell v-if="deviceAll?.type == 5 || deviceAll?.type == 6" prefix="平段电价" required align="right" suffix="元/度">
        <template #content>
          <input type="digit"  :cursor="posCur" @input="bindReplaceInput"  @focus="utilsClass = ''" @blur="utilsClass = 'footer-fixed'" name="price" cursor-spacing="120" v-model="device.coef2" placeholder="" />
        </template>
      </MyCell>
      <MyCell v-if="deviceAll?.type == 5 || deviceAll?.type == 6" prefix="低谷电价" required align="right" suffix="元/度">
        <template #content>
          <input type="digit"  :cursor="posCur" @input="bindReplaceInput"  @focus="utilsClass = ''" @blur="utilsClass = 'footer-fixed'" name="price" cursor-spacing="120" v-model="device.coef1" placeholder="" />
        </template>
      </MyCell>


      <view class="cell" v-if="deviceAll?.type != 5 && deviceAll?.type != 6">
        <text style="color: #333;">{{ deviceAll?.type == 2 ? '综合水价(基础水价+服务费)：' : '综合电价(基础电价+服务费)：' }} {{ totalPrice }} {{ deviceAll?.type == 2 ? '元/m³' : '元/度' }}</text>
      </view>
    </view>
    <contantModal :show="isContact2" @close="isContact2 = false" :isInlineServe="false" :phone="globalStore.userInfo.service_te" title="绑定异常，请联系您的专属客服"/>

    <view :class="utilsClass">
      <button class="btn-add m33" @tap="handleSubmit">添加设备</button>
    </view>
    <ClassPopUp 
      :classShow="classShow" 
      @close="classShow = false"
      @confirm="onConfirm"
    />
  </view>
</template>
<script setup>
  import { ref } from 'vue'
  import './bindDevice.scss'
  import Taro, { useDidShow, useDidHide, useReady, useLoad, usePullDownRefresh, useReachBottom} from '@tarojs/taro'
  import request from '@/utils/request'
  import { debounce } from 'lodash'
  import {validation} from "@/utils/validation"

  import MyInput from '@/components/MyInput'
  import MyCell from '@/components/MyCell'
  import MyIcon from '@/components/MyIcon'
  import contantModal from '../../pages/my/components/contantModal/index.vue'
  import ClassPopUp from "@/components/ClassPopUp";
  import { useGlobalStore } from '@/stores'
  import { computed } from 'vue'
  const globalStore = useGlobalStore()
  import shuiImg from '@/assets/water/device.png'

  const sn = ref('')

  const posCur = ref(-1)

  const bindReplaceInput = (e) => {
    let pos = e.detail.cursor
    posCur.value = pos
  }

  const classShow = ref(false)

  const checkedList = ref([]);

  const selectClassNames = ref([])
  
  const isContact2 = ref(false);

  const utilsClass = ref('footer-fixed')

  const device = ref({
    sn: '',
    net_type: '',
    price: '',
    coef1: '',
    coef2: '',
    coef3: '',
    coef4: '',
  })

  const deviceAll = ref()

  // 综合电价
  const totalPrice = computed(() => {
  //  return  deviceAll.value?.agent?.type == 2 ? (+device.value.price + Number(ServicePrice.value) + Number(deviceAll.value?.agent?.service)).toFixed(2)  : (+device.value.price + +ServicePrice.value).toFixed(2)
    // 统一 服务费+基础电价
    return  (+device.value.price + +ServicePrice.value).toFixed(2)
  })

  const ServicePrice = ref('')

  const formState = ref({
    estate_name: '',
    name: '',
    province: '',
    city: '',
    district: '',
    country: '',
    lat:"",
    lng:'',
    fang: 1,
    ting: 1,
    wei: 1,
    chu: 1,
    size: 0,
    rent: 0
  })

  const onConfirm = (val,val2) => {
    selectClassNames.value = val;
    checkedList.value = val2;
  }

const rederText = (net_type,type) => {
  let text = '4G双模预付费单相导轨电表'
  if (net_type == 1) {
    text = '4G双模预付费单相导轨电表'
    if (type == 2) {
      text = '4G双模预付费单相导轨水表'
    }
  } else {
    text = '预付费智能电表'
    if (type == 2) {
      text = '预付费智能水表'
    }
  }
  return text
}

  useLoad((options) => {
    if (!options.sn) {
      // Taro.showToast({
      //   title: '设备不存在',
      //   icon: 'error'
      // })
      return
    }
    globalStore.getUserInfo();
    sn.value = options.sn
    getDeviceDetail(sn.value)
    if (globalStore.ServicePrice) {
      ServicePrice.value = globalStore.ServicePrice
    }
    // 如果有缓存的地址
    if (globalStore.cacheLocation) {
      formState.value.province = globalStore.cacheLocation.province
      formState.value.city = globalStore.cacheLocation.city
      formState.value.district = globalStore.cacheLocation.district
      formState.value.estate_name = globalStore.cacheLocation.estate_name
      formState.value.country = globalStore.cacheLocation?.country
      formState.value.lat = globalStore.cacheLocation?.lat
      formState.value.lng = globalStore.cacheLocation?.lng
    }
  })

  const handleSubmit = debounce(function() {
    console.log(formState.value);
    console.log(device.value);
    globalStore.setcacheServicePrice(device.value.ServicePrice)
    validation(formState.value, {
      estate_name: {
        type: 'required',
        message: '请输入小区名'
      },
      name: {
        type: 'required',
        message: '请输入房源名'
      },
      // price: {
      //   type: 'required',
      //   message: '请输入电费定价'
      // }
    }).then(() => {
      validation(device.value, deviceAll.value.type != 5 && deviceAll.value.type != 6 ?{
        price: {
          type: "required",
          message: "请输入电费定价",
        },
      } : {}).then(() => {
        if (device.value.price <= 0 && deviceAll.value.type != 5 && deviceAll.value.type != 6) {
          Taro.showToast({
            title: '电费定价必须大于0',
            icon: 'none'
          })
          return
        }
        if (isNaN(totalPrice.value) && deviceAll.value.type != 5 && deviceAll.value.type != 6) {
          Taro.showToast({
            title: '请输入正确的价格!',
            icon: 'none'
          })
          return
        }
        if (globalStore.isAdmin) {
          if (selectClassNames.value.length == 0) {
            return Taro.showToast({
              title: "请选择分类",
              icon: "error",
            })
          }
        }
        globalStore.setcacheDevicePrice(device.value.price)
        globalStore.setcacheServicePrice(device.value.ServicePrice)
        // 更新峰谷表电价
        if (deviceAll.value.type == 5 || deviceAll.value.type == 6) {
          request.post({
            url: 'business/updateCoef',
            data: {
              coef1: device.value.coef1,
              coef2: device.value.coef2,
              coef3: device.value.coef3,
              coef4: device.value.coef4,
              sn: deviceAll.value.sn
            }
          })
        }
        request.post({
          url: 'house/create',
          data: {
            house: formState.value,
            device: {
              ...device.value,
              basic_price: device.value.price,
              service_price: ServicePrice.value,
              price: totalPrice.value
            },
           classId: selectClassNames.value.length ? JSON.stringify(checkedList.value) : '',
          },
          showToast:false
        }).then(data => {
          Taro.showModal({
            title: '提示',
            showCancel: false,
            content: '设备添加成功！',
            success: function (res) {
              Taro.navigateBack()
            }
          })
        }).catch((err) => {
          console.log(err,"err");
          if (err.data.message.includes('专属客服')) {
            isContact2.value = true
          } else {
            Taro.showToast({
              title: err.data.message,
              icon: 'none'
            })
          }
        })
      }).catch((err) => {
        Taro.showToast({
            title: err,
            icon: 'error'
        })
    })
    }).catch((err) => {
      Taro.showToast({
        title: err,
        icon: 'error'
      })
    })


  }, 500)

  	// 防止精度丢失
	function addDecimals(a, b) {
		let multiplier = Math.pow(10, 10); // 选择一个适当的倍数
		let intA = Math.round(a * multiplier);
		let intB = Math.round(b * multiplier);
		let result = (intA + intB) / multiplier;
		return result;
	}

  const onChooseLocation = () => {

    Taro.chooseLocation({
        success: (res) => {
          console.log(res)
          formState.value.estate_name = res.name

          // let reg = /.+?(省|市|自治区|自治州|县|区)/g
          let reg = /.+?(省|市|自治区|自治州|县|区|镇|乡|街道)/g;
          const area = res.address.match(reg)
          console.log(area);
          formState.value.province = area[0]
          formState.value.city = area[1]
          formState.value.district = area[2]
          formState.value.country = area[3] || ''
          formState.value.lat = res.latitude
          formState.value.lng = res.longitude
          Promise.resolve().then(() => {
            globalStore.setcacheLocation(formState.value)
          })
        }
        })
        return
    Taro.showLoading({
      title: '正在获取当前位置'
    })
    Taro.getLocation({
      isHighAccuracy: true,
      success: (local) => {
        console.log(local)
        Taro.hideLoading()
        if (!formState.value.estate_name) {
          // getAddress(local.latitude, local.longitude)
          // 微信有偏差需要处理
          if (process.env.TARO_ENV === 'weapp') {
            getAddress(addDecimals(local.latitude, 0.000312), addDecimals(local.longitude, 0.006611))
          } else {
            getAddress(local.latitude, local.longitude)
          }
          return
        }
        Taro.chooseLocation({
          // 微信有偏差需要处理
          // latitude: addDecimals(local.latitude, 0.000312),
          // longitude: addDecimals(local.longitude, 0.006611),
          success: (res) => {
            console.log(res)
            formState.value.estate_name = res.name

            let reg = /.+?(省|市|自治区|自治州|县|区)/g
            const area = res.address.match(reg)
            formState.value.province = area[0]
            formState.value.city = area[1]
            formState.value.district = area[2]
          }
        })
      },
      fail: (e) => {
        console.log(e);
        if (e.errCode == 404) {
          Taro.showModal({
          title: '提示',
          content: '获取定位失败！点击重启！',
          showCancel:false,
          success: function (res) {
            if (res.confirm) {
              console.log('用户点击确定')
              wx.restartMiniProgram({
                path:'/pages/index/index'
              })
            } else if (res.cancel) {
              console.log('用户点击取消')
            }
          }
        })
        }
        if (e.errMsg == 'getLocation:fail auth deny') {
          Taro.showModal({
            title: '提示',
            content: '获取位置失败请不要拒绝授权，是否前往设置手动开启权限',
            success: function(res) {
              if (res.confirm) {
                console.log('用户点击确定');
                Taro.openSetting({
                  success (res) {
                  console.log(res.authSetting)
                  }
                })
              } else if (res.cancel) {
                console.log('用户点击取消');
              }
            }
          });
        }
        Taro.hideLoading()
      }
    })

  }

  const getDeviceDetail = (sn) => {
    request.get({
      url: 'device/search',
      data: {
        sn: sn
      }
    }).then(res => {
      deviceAll.value = res.data
      device.value = {
        sn: res.data.sn,
        net_type: res.data.net_type,
        price: res.data.price,
        coef1: res.data.coef1,
        coef2: res.data.coef2,
        coef3: res.data.coef3,
        coef4: res.data.coef4,
      }
      if (!res.data.price) {
          // 如果有缓存的电价
        if (globalStore.DevicePrice) {
          device.value.price = globalStore.DevicePrice
        }
      }
    }).catch(() => {
      // 如果有缓存的电价
      if (globalStore.DevicePrice) {
        device.value.price = globalStore.DevicePrice
      }
    })
  }

  const getAddress = (latitude, longitude) => {
    request.get({
      url: 'getAddress',
      data: {
        location: latitude + ',' + longitude
      }
    }).then(res => {
      console.log(res.data)
      if (res.data.status === 0) {
        formState.value.estate_name = res.data.result.formatted_addresses.recommend
        formState.value.province = res.data.result.address_component.province
        formState.value.city = res.data.result.address_component.city
        formState.value.district = res.data.result.address_component.district
        console.log(formState)
      }
    })
  }

</script>
