<!--
 * @Autor: lisong
 * @Date: 2023-08-10 15:14:49
 * @LastEditors: lisong
 * @LastEditTime: 2023-08-11 15:30:09
-->
<template>
  <MySearch placeholder="关健词搜索" @search="onSearch"></MySearch>
  <view class="house-list">
    <!-- <view class="no-data">暂无信息</view> -->
    <view
      class="house-item"
      v-for="item in houseList"
      :key="item.id"
      :class="'house-status-' + item.status_key"
      @tap="onChoose(item.id)"
    >
      <view class="house-title">
        <image
          :src="IconHouseMini"
          class="icon-house-mini"
          mode="widthFix"
        ></image
        >{{ item.name }}
      </view>
      <view
        ><text class="house-rent">￥{{ item.rent }}/月</text></view
      >
      <view class="mt36 flex flex-v-center flex-space-between">
        <view
          ><text class="house-tenant">{{ item.tenant_name }}</text></view
        >
        <view class="house-day">
          <image
            :src="IconQuan"
            style="width: 26rpx; height: 26rpx; vertical-align: middle"
          ></image>
          <text
            v-if="item.status_key === 'vacant'"
            style="vertical-align: middle"
          >
            空置 {{ item.create_days }} 天</text
          >
          <text
            v-if="item.status_key === 'hired'"
            style="vertical-align: middle"
          >
            租期还剩 {{ item.days }} 天</text
          >
          <text
            v-if="item.status_key === 'expire'"
            style="vertical-align: middle"
          >
            合同还有 {{ item.days }} 天到期</text
          >
          <text
            v-if="item.status_key === 'expired'"
            style="vertical-align: middle"
          >
            租期已过 {{ item.days }} 天</text
          >
          <text
            v-if="item.status_key === 'fail'"
            style="vertical-align: middle"
          >
            账单逾期 {{ item.fail_days }} 天</text
          >
        </view>
      </view>
      <view class="house-status">{{ item.status }}</view>
      <view class="house-left-border"></view>
    </view>
  </view>
</template>

<script setup>
import { ref } from "vue";
import request from "@/utils/request";
import MySearch from "@/components/MySearch";
const IconAboutStatus = "https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-about-status.png";
const IconHouseMini = "https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-house-mini.png";
const IconQuan = "https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-quan.png";
//  constant
definePageConfig({
  navigationBarTitleText: "选择房源",
  navigationBarBackgroundColor: "#1352FD",
  navigationBarTextStyle: "white",
});

import Taro, {
  useDidShow,
  useDidHide,
  useReady,
  useLoad,
  usePullDownRefresh,
  useReachBottom,
} from "@tarojs/taro";

useLoad((options) => {
  getHouseList();
});

const onSearch = (keyword) => {
  houseParams.keyword = keyword;
  fetch();
};

const fetch = () => {
  houseList.value = [];
  houseParams.page = 1;
  getHouseList();
};

const houseParams = ref({
  page: 1,
  status: "vacant",
});

const houseList = ref([]);

const getHouseList = () => {
  request
    .get({
      url: "house",
      data: {
        ...houseParams.value,
      },
    })
    .then((res) => {
      houseList.value = [...houseList.value, ...res.data.items];
      if (res.data.currentPage >= res.data.lastPage) {
        isLastPage.value = true
      }
    });
};

const isLastPage = ref(false)

useReachBottom(() => {
  if (!isLastPage.value) {
    houseParams.value.page += 1
    getHouseList()
  }
})

const onChoose = (id) => {
  console.log(id);
  Taro.redirectTo({
    url: "/pages/contract/chooseType/chooseType?id=" + id,
  });
};
</script>

<style lang="scss">
.house-list {
  padding: 50px 25px;

  .house-item {
    border-radius: 14px;
    background-color: #f4edf8;
    margin-bottom: 31px;
    padding: 26px 21px 29px 48px;
    position: relative;

    .house-title {
      font-family: OPPOSans;
      font-weight: 500;
      font-size: 36px;
    }

    .icon-house-mini {
      width: 36px;
      height: 31px;
      vertical-align: middle;
      display: inline-block;
      margin-right: 10px;
    }
    .house-day {
      font-size: 26px;
      font-weight: 500;
    }
    .house-rent {
      background-color: #f86d37;
      padding: 5px 12px;
      font-size: 24px;
      color: #ffffff;
      display: inline-block;
      border-radius: 12px;
      margin-top: 14p;
    }
    .house-tenant {
      font-size: 26px;
    }
    .house-status {
      width: 134rpx;
      height: 47rpx;
      background: #7b459a;
      border-radius: 14rpx 0rpx 0rpx 14rpx;
      font-size: 26rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #ffffff;
      line-height: 47rpx;
      position: absolute;
      right: 0;
      top: 20px;
      text-align: center;
    }
    .house-left-border {
      width: 20rpx;
      height: 100%;
      background: #7b459a;
      border-radius: 14rpx 0rpx 0rpx 14rpx;
      position: absolute;
      left: 0;
      top: 0;
    }
  }
  .house-status-vacant {
    background-color: #eff5ff;

    .house-status {
      background-color: #3f83ff;
    }
    .house-left-border {
      background-color: #3f83ff;
    }
  }
  .house-status-signing {
    background-color: #ecfdea;
    .house-status {
      background-color: #6bd65a;
    }
    .house-left-border {
      background-color: #74d964;
    }
  }
  .house-status-expire {
    background-color: #fdeef6;
    .house-status,
    .house-left-border {
      background-color: #e6479c;
    }
  }
  .house-status-expired {
    background-color: #fff1e6;
    .house-status {
      background-color: #f86d37;
    }
    .house-left-border {
      background-color: #f86d37;
    }
  }
  .house-status-fail {
    background-color: #feece9;

    .house-status {
      background-color: #f93a1b;
    }
    .house-left-border {
      background-color: #f93a1a;
    }
  }
}
</style>
