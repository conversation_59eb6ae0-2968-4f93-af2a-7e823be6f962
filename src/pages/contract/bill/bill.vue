<!--
 * @Autor: lisong
 * @Date: 2023-08-13 10:48:25
 * @LastEditors: lisong
 * @LastEditTime: 2023-08-13 20:37:20
-->
<template>
  <view
    class="list"
    v-for="item in items"
    :key="item.sn"
    @tap="openDetail(item)"
  >
    <view class="list-top">
      <view>{{ item.title }}</view>
      <view class="list-amount">{{ item.amount }}</view>
    </view>
    <view class="list-address">
      <view>{{ item.house?.estate_name }}{{ item.house?.name }}</view>
      <view v-if="item.is_fail && !item.is_close" class="list-label"
        >已逾期</view
      >
      <view v-if="item.is_paid && !item.is_close" class="list-label"
        >已支付</view
      >
      <view v-if="item.is_close" class="list-label">已作废</view>
    </view>
    <view class="list-time">
      <view>租房周期 {{ item.start_date }} ~ {{ item.end_date }}</view>
      <view>应收时间 {{ item.pay_date }}</view>
    </view>
    <view
      class="list-action"
      v-if="!item.is_paid && !item.is_close && globalStore.who === 'business'"
    >
      <view class="list-btn" @tap="handlePay(item.id)">确认收款</view>
      <view class="list-more_action" @tap="handleAction(item)">
        <image
          class="list-icon"
          src="https://yimidb.oss-cn-hangzhou.aliyuncs.com/mp/images/contract/icon-action.png"
        ></image>
        <view>操作</view>
      </view>
    </view>
  </view>
  <MyPopup :show="showAction" @close="onShowAction" title="更多操作">
    <template #content>
      <view>
        <MyCell prefix="作废" arrow @tap="onBillCancel"></MyCell>
        <MyCell prefix="改期" arrow align="right">
          <template #content>
            <picker mode="date" @change="onDateChange" :start="selectItem.pay_date">
              <view class="picker">
                {{ selectItem.pay_date }}
              </view>
            </picker>
          </template>
        </MyCell>
      </view>
      <view class="p20 text-center">
        <text class="btn-text" @tap="onShowAction">取消</text>
      </view>
    </template>
  </MyPopup>
</template>

<script setup>
import { ref } from "vue";
import request from "@/utils/request";
import MyPopup from "@/components/MyPopup"
import MyCell from "@/components/MyCell"
import { useGlobalStore } from "@/stores";
import Taro, {
  useDidShow,
  useDidHide,
  useReady,
  useLoad,
  usePullDownRefresh,
  useReachBottom,
} from "@tarojs/taro";
const globalStore = useGlobalStore();
definePageConfig({
  navigationBarTitleText: "费用明细",
  navigationBarBackgroundColor: "#1352FD",
  navigationBarTextStyle: "white",
});
let contract_id = 0;

useLoad((options) => {
  contract_id = options.contract_id;
});

useDidShow(() => {
  getContractDetail();
});

const items = ref([]);
const getContractDetail = () => {
  request
    .get({
      url: "contract/" + contract_id,
    })
    .then((res) => {
      items.value = res.data.bills;
    });
};

let selectItem = ref({});
const newPayDate = ref('');

const showAction = ref(false);
const onShowAction = () => {
  showAction.value = !showAction.value;
}

const handleAction = (item) => {
  selectItem.value = item
  onShowAction()
  // Taro.showActionSheet({
  //   itemList: ["作废", "改期"],
  //   success: ({ tapIndex }) => {
  //     console.log(tapIndex);
  //     if (tapIndex === 0) {
  //       onBillDestroy();
  //     }
  //     if (tapIndex === 1) {
  //
  //     }
  //   },
  // });
};
const onBillCancel = () => {
  onShowAction();
  Taro.navigateTo({
    url: '/pages/contract/billCancel/billCancel?id=' + selectItem.value.id
  });
}

const onDateChange = (e) => {
  request.post({
    url: 'contract/bill/' + selectItem.value.id + '/changePayDate',
    data: {
      pay_date: e.detail.value
    }
  }).then(res => {
    getContractDetail()
    selectItem.value.pay_date = e.detail.value
  })
}

const handlePay = (id) => {
  Taro.navigateTo({
    url: `/pages/contract/billPayForm/billPayForm?id=${id}`,
  });
};

const openDetail = (item) => {
  if (item.is_paid && !item.is_close) {
    Taro.navigateTo({
      url: `/pages/contract/billPay/billPay?id=${item.id}`,
    });
  }
};
</script>

<style lang="scss">
page {
  background: #f1f3f7;
}

.list {
  margin: 26rpx;
  background: #fff;
  border-radius: 14rpx;
  overflow: hidden;
  .list-top {
    padding: 32rpx 32rpx 0;
    line-height: 1;
    font-size: 36rpx;
    font-family: OPPOSans;
    font-weight: 500;
    color: #000000;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .list-amount {
      font-size: 48rpx;
      &::before {
        content: "￥";
        font-size: 30rpx;
      }
    }
  }
  .list-address {
    padding: 0 32rpx;

    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 26rpx;
    font-family: OPPOSans;
    font-weight: 500;
    color: #000000;
    margin-top: 10rpx;
    .list-label {
      padding: 5rpx 14rpx;
      font-size: 24rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #ffffff;
      border-radius: 14rpx;
      background-image: linear-gradient(90deg, #fd7300, #fd9102);
    }
  }
  .list-time {
    margin-top: 50rpx;
    font-size: 26rpx;
    font-family: OPPOSans;
    font-weight: 500;
    color: #a8b1ca;
    margin-bottom: 32rpx;
    padding: 0 32rpx;
  }
  .list-action {
    height: 100rpx;
    border-top: 1rpx dashed #dee5f4;
    padding: 0 32rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .list-btn {
      width: 160rpx;
      height: 60rpx;
      background: #1352fd;
      border-radius: 6rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 26rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #ffffff;
    }
    .list-more_action {
      display: flex;
      align-items: center;
      font-size: 26rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #1352fd;
      .list-icon {
        width: 28rpx;
        height: 28rpx;
        margin-right: 5rpx;
      }
    }
  }
}
</style>
