<template>
  <view>
    <view class="bill-cancel-top">
      <view class="warn"><MyIcon icon="icon-warning" width="64rpx" height="60rpx"></MyIcon> <text class="text-v-center">作废账单请谨慎核对信息！</text></view>
    </view>
    <view class="bill-cancel-bg">
      <view class="bill-cell flex flex-space-between flex-v-center">
        <view class="bill-lab">应收金额</view>
        <view class="bill-val"><text class="cny">￥</text>{{bill.amount}}</view>
      </view>
      <view class="bill-cell flex flex-space-between flex-v-center">
        <view class="bill-lab">合同编号</view>
        <view class="bill-val">{{bill.contract?.sn}}</view>
      </view>
      <view class="bill-cell flex flex-space-between flex-v-center">
        <view class="bill-lab">手机号码</view>
        <view class="bill-val">{{bill.contract?.master_tenant?.mobile}}</view>
      </view>
      <view class="bill-cell flex flex-space-between flex-v-center">
        <view class="bill-lab">应收时间</view>
        <view class="bill-val">{{bill.pay_date}}</view>
      </view>
    </view>
  </view>
  <view class="footer-fixed">
    <button class="btn-add m33" @tap="onDestroy">确认作废</button>
  </view>
</template>
<script setup>
import { ref } from "vue";
import request from "@/utils/request";
import { useGlobalStore } from "@/stores";
import MyIcon from "@/components/MyIcon"
import Taro, {
  useDidShow,
  useDidHide,
  useReady,
  useLoad,
  usePullDownRefresh,
  useReachBottom,
} from "@tarojs/taro";
const globalStore = useGlobalStore();
definePageConfig({
  navigationBarTitleText: "作废"
});
let billId = 0;
const bill = ref({})

useLoad((options) => {
  billId = options.id;
  getDetail()
});

const getDetail = () => {
  request.get({
    url: "contract/bill/" + billId,
  }).then((res) => {
    bill.value = res.data;
  });
}

const onDestroy = () => {
  request
    .delete({
      url: `contract/bill/${billId}/destroy`,
    })
    .then((res) => {
      Taro.navigateBack()
    });
}

</script>
<style lang="scss">
page,body {
  background: #F7F9FF;
}
.bill-cancel-top {
  width: 724px;
  height: 202px;
  background: url('https://yimidb.oss-cn-hangzhou.aliyuncs.com/mp/images/img-bill-cancel.png') no-repeat center;
  background-size: contain;

  .warn {
    padding: 94px 0;
    text-align: center;
    font-size: 34px;
    font-weight: 500;
    color: #000000;
  }
}
.bill-cancel-bg {
  width: 724px;
  height: 430px;
  background: url('https://yimidb.oss-cn-hangzhou.aliyuncs.com/mp/images/img-bill-cancel-bg.png') no-repeat center;
  background-size: contain;
  text-align: center;
  .bill-cell {
    border-bottom: 1px solid #DFDFDF;
    height: 100px;
    width: 593px;
    margin-left: auto;
    margin-right: auto;

    .bill-lab {
      font-size: 30px;
      font-weight: 500;
      color: #B6BEC5;
      padding-left: 28px;
    }
    .bill-val {
      font-size: 32px;
      font-weight: 400;
      color: #000000;
      padding-right: 28px;
      .cny {
        font-size: 32px;
      }
    }
  }
}

</style>
