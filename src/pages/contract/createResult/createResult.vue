<!--
 * @Autor: lisong
 * @Date: 2023-08-15 17:41:31
 * @LastEditors: lisong
 * @LastEditTime: 2023-08-16 14:58:08
-->
<template>
  <view class="main">
    <template v-if="contract.type === 2">
      <view class="box">
        <view class="box-left">
          <image
            class="box-left_icon"
            src="https://yimidb.oss-cn-hangzhou.aliyuncs.com/mp/images/contract/step-active.png"
          ></image>
          <view class="box-left_line active"></view>
        </view>
        <view class="box-right">
          <view class="title">合同提交成功!</view>
          <view class="card">
            <view class="card-title">{{
              contract.house?.estate_name + contract.house?.name
            }}</view>
            <view class="card-bottom">
              <view>租客姓名：{{ contract.master_tenant?.name }}</view>
              <view
                >租赁时间：{{ contract.start_at }} ~
                {{ contract.expired_at }}</view
              >
            </view>
          </view>
        </view>
      </view>
      <view class="box">
        <view class="box-left">
          <image
            class="box-left_icon"
            src="https://yimidb.oss-cn-hangzhou.aliyuncs.com/mp/images/contract/step.png"
          ></image>
          <view class="box-left_line"></view>
        </view>
        <view class="box-right">
          <view class="title">等待租客签字</view>
          <view class="content">
            <view>租客用手机号登录小程序进入我的合同中进行签署</view>
            <view>新用户密码为手机后6位,请尽快修改密码 </view>
            <view>截图可保存二维码分享给租客</view>
          </view>
          <view class="content"><image :src="contract.mp_qrcode" class="mini-img"></image></view>
<!--          <view class="content">-->
<!--            您还可以复制签署链接，通过微信发给租客，在微信环境中打开链接进行签署-->
<!--          </view>-->
<!--          <view class="content">-->
<!--            签署链接：{{ fdd.actorSignTaskUrl }}-->
<!--            <text class="content-btn" @tap="handleCopy(fdd.actorSignTaskUrl)"-->
<!--              >点击复制链接</text-->
<!--            >-->
<!--          </view>-->
              <view class="content">
                <view>租客账号：{{ contract.master_tenant?.mobile }}</view>
                <view>初始密码：手机后6位</view>
              </view>
        </view>
      </view>
    </template>

    <view v-if="contract.type === 1">
      <view class="main-title">合同提交成功！</view>
      <view class="main-content">
        <view>租客用手机号登录小程序进入我的合同中查看</view>
        <view>新用户密码为手机后6位,请尽快修改密码 </view>
        <view>截图可保存二维码分享给租客</view>
      </view>
      <view class="main-mini_title">小程序二维码</view>
      <image :src="contract.mp_qrcode" class="mini-img"></image>
      <view class="main-mini_sub">扫码登录</view>
      <view class="main-bottom">
        <view>租客账号：{{ contract.master_tenant?.mobile }}</view>
        <view>初始密码：手机后6位</view>
      </view>
    </view>

    <view class="footer-fixed">
      <view class="flex flex-v-center p20">
        <view style="width: 28%"><text class="color-primary" @tap="onHome">返回</text></view>
        <view style="width: 70%;"><button class="btn btn-primary" @tap="onDetail">查看合同详情</button></view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from "vue";
import request from "@/utils/request";
import Taro, {
  useDidShow,
  useDidHide,
  useReady,
  useLoad,
  usePullDownRefresh,
  useReachBottom,
} from "@tarojs/taro";
definePageConfig({
  navigationBarTitleText: "提交完成",
  navigationBarBackgroundColor: "#1352FD",
  navigationBarTextStyle: "white",
});

const id = ref(0);
const contract = ref({});

const fdd = ref({});

useLoad((options) => {
  id.value = options.id;
  getDetail();
});

const getDetail = () => {
  request
    .get({
      url: "contract/" + id.value,
    })
    .then((res) => {
      contract.value = res.data;
      if (contract.value.type === 2) {
        request
          .get({
            url: "fdd/getSignTaskUrl/" + id.value,
          })
          .then((res) => {
            fdd.value = res.data;
          });
      }
    });
};

const onDetail = () => {
  Taro.redirectTo({
    url: "/pages/contract/detail/detail?id=" + id.value,
  });
};

const handleCopy = (data) => {
  Taro.setClipboardData({
    data,
  });
};

const onHome = () => {
  Taro.switchTab({
    url: '/pages/index/index'
  })
}

</script>

<style lang="scss">
.main {
  padding: 50rpx 0;
}

.box {
  padding: 0 25rpx;
  display: flex;
  min-height: 364rpx;
  .box-left {
    width: 32rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    flex-shrink: 0;
    .box-left_icon {
      width: 32rpx;
      height: 32rpx;
    }
    .box-left_line {
      flex: 1;
      background: rgba(199, 211, 223, 1);
      width: 2rpx;
    }

    .active {
      background: rgba(19, 82, 253, 1);
    }
  }
  .box-right {
    flex: 1;
    margin-left: 12rpx;
    .title {
      font-size: 36rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #000000;
      line-height: 1;
    }
    .content {
      font-size: 30rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #becad5;
      line-height: 48rpx;
      margin: 20rpx 0;
      .content-btn {
        color: rgba(19, 82, 253, 1);
      }
    }
    .card {
      width: 637rpx;
      height: 215rpx;
      background: #ffffff;
      box-shadow: 0rpx 4rpx 32rpx 0rpx rgba(182, 190, 201, 0.38);
      border-radius: 20rpx;
      margin: 28rpx 0;
      box-sizing: border-box;
      padding: 44rpx 34rpx;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .card-title {
        font-size: 30rpx;
        font-family: OPPOSans;
        font-weight: 500;
        color: #000000;
        line-height: 1;
      }
      .card-bottom {
        font-size: 24rpx;
        font-family: OPPOSans;
        font-weight: 500;
        color: #000000;
      }
    }
  }
}

.main-title {
  font-size: 40rpx;
  font-family: OPPOSans;
  font-weight: 500;
  color: #000000;
  line-height: 1;
  margin: 157rpx auto 40rpx;
  text-align: center;
}

.main-content {
  font-size: 30rpx;
  font-family: OPPOSans;
  font-weight: 500;
  color: #becad5;
  text-align: center;
  line-height: 48rpx;
}

.main-mini_title {
  font-size: 30rpx;
  font-family: OPPOSans;
  font-weight: 500;
  color: #000000;
  text-align: center;
  margin: 60rpx 0 32rpx;
}

.mini-img {
  width: 235rpx;
  height: 235rpx;
  background: #c7d3df;
  margin: 0 auto;
  display: block;
}

.main-mini_sub {
  font-size: 30rpx;
  font-family: OPPOSans;
  font-weight: 500;
  color: #becad5;
  margin-top: 22rpx;
  text-align: center;
}

.main-bottom {
  font-size: 30rpx;
  font-family: OPPOSans;
  font-weight: 500;
  color: #becad5;
  line-height: 40rpx;
  margin-top: 50rpx;
  text-align: center;
}
</style>
