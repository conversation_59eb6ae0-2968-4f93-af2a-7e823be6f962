<!--
 * @Autor: lisong
 * @Date: 2023-08-11 15:21:54
 * @LastEditors: 无期 <EMAIL>
 * @LastEditTime: 2025-06-23 22:56:29
-->
<template>
  <view class="top">
    <view class="top-title"
      >{{ contract?.house?.estate_name }} {{ contract?.house?.name }}</view
    >
    <view class="top-sn">合同编号：{{ contract?.sn }}</view>
  </view>

  <view class="list-box">
    <view class="list" @tap="handleTenant">
      <view class="list-label">承租人</view>
      <view
        :class="[
          'list-right',
          {
            'list-success': contract.mobile,
          },
        ]"
        >{{ contract.mobile ? "已设置" : "未设置" }}</view
      >
    </view>
    <view class="list" @tap="handleRent">
      <view class="list-label">房屋租金</view>
      <view
        :class="[
          'list-right',
          {
            'list-success': contract.contract_rent,
          },
        ]"
        >{{ contract.contract_rent ? "已设置" : "未设置" }}</view
      >
    </view>
  </view>

  <view class="list-box" v-if="contract.type === 1">
    <view class="list" @tap="handleUpload(2)">
      <view class="list-label no-required">扫描合同件上传</view>
      <view
        :class="[
          'list-right',
          {
            'list-success':
              contract.contract_images && contract.contract_images.length,
          },
        ]"
        >{{
          contract.contract_images && contract.contract_images.length > 0
            ? "已上传"
            : "未上传"
        }}</view
      >
    </view>
  </view>
  <view class="list-box" v-if="contract.type === 2">
    <view class="list" @tap="handlePreviewContract">
      <view class="list-label">合同模板</view>
      <view
        :class="[
          'list-right',
          {
            'list-success': contract.template_id,
          },
        ]"
        >{{ contract.template_id ? template.signTemplateName : "请选择" }}</view
      >
    </view>
  </view>
  <view class="card" style="padding: 30rpx;box-sizing: border-box;" v-if="globalStore.who === 'business'">
    <textarea @blur="bindTextAreaBlur" v-model="contract.remark" maxlength="250"  placeholder="可输入水、电、气单价以及家具家电等费用约定" confirm-type="done" @confirm="onSave"/>
    <view class="btn-save" @tap="onSave">
      保存
    </view>
  </view>

  <myBottomBtn btnTxt="确认提交" @click="handleConfirm">
    <view class="bottomBtn" @tap="handleClick">删除合同</view>
  </myBottomBtn>

  <view class="mask" v-if="showDel">
    <view class="mask-content">
      <view class="mask-title">删除合同</view>
      <view class="mask-info"
        >删除合同后, 该合同所有内容清零, 不可恢复，是否删除?
      </view>
    </view>
    <view class="mask-bottom">
      <view class="mask-delBtn" @tap="onDestroy">确认删除</view>
      <view class="mask-conBtn" @tap="handleClick">取消</view>
    </view>
  </view>

  <view class="mask" v-if="showConfirm">
    <view class="mask-content">
      <view class="mask-title">确认信息</view>
      <view class="mask-info">
        <view class="mask-info_list">
          <view>租赁时间</view>
          <view>{{ contract.start_at + " ~ " + contract.expired_at }}</view>
        </view>
        <view class="mask-info_list">
          <view>付款方式</view>
          <view>{{
            contract.contract_rent.rent_num_name
          }}</view>
        </view>
        <view class="mask-info_list">
          <view>房屋租金</view>
          <view>￥{{ contract.contract_rent?.amount }}/月</view>
        </view>
        <view class="mask-info_list">
          <view>房屋押金</view>
          <view>￥{{ contract.contract_rent?.deposit }}</view>
        </view>
      </view>
    </view>
    <view class="mask-bottom">
      <view class="mask-delBtn" @tap="handleConfirm">返回修改</view>
      <view class="mask-conBtn" @tap="onSubmit">确认无误</view>
    </view>
  </view>
</template>

<script setup>
import { ref } from "vue";
import request from "@/utils/request";
import myBottomBtn from "@/components/MyBottomBtn/MyBottomBtn";
import Taro, {
  useDidShow,
  useDidHide,
  useReady,
  useLoad,
  usePullDownRefresh,
  useReachBottom,
} from "@tarojs/taro";
definePageConfig({
  navigationBarTitleText: "填写合同",
  navigationBarBackgroundColor: "#1352FD",
  navigationBarTextStyle: "white",
});

import { useGlobalStore } from '@/stores'
  const globalStore = useGlobalStore()

useLoad((options) => {
  id.value = options.id;
});

useDidShow(() => {
  getContractDetail();
});

const id = ref(0);
const contract = ref({});
const getContractDetail = () => {
  request
    .get({
      url: "contract/" + id.value,
    })
    .then((res) => {
      contract.value = res.data;
      console.log(contract.value.contract_images);
      // this.setData({
      //   contract: {... res.data},
      //   house: res.data.house
      // })
      if (res.data.type === 2) {
        getTemplateList();
      }
    });
};

const onDestroy = () => {
  request
    .delete({
      url: `contract/${contract.value.id}/destroy`,
    })
    .then((res) => {
      Taro.navigateBack();
    });
};

const handleTenant = () => {
  Taro.navigateTo({
    url: "/pages/contract/tenant/tenant?contract_id=" + contract.value.id,
  });
};

const handleRent = () => {
  Taro.navigateTo({
    url: "/pages/contract/rent/rent?contract_id=" + contract.value.id,
  });
};

const handleUpload = (type) => {
  Taro.navigateTo({
    url:
      "/pages/contract/form/form?contract_id=" +
      contract.value.id +
      "&type=" +
      type,
  });
};

const template = ref({});
const templateList = ref([]);
const handleChooseTemplate = () => {
  const options = templateList.value.map((v) => {
    return v.signTemplateName;
  });
  Taro.showActionSheet({
    itemList: options,
    success: (res) => {
      console.log(res.tapIndex);
      template.value = templateList.value[res.tapIndex];
      contract.value.template_id = template.value.signTemplateId;
    },
    fail(res) {
      console.log(res.errMsg);
    },
  });
};

const getTemplateList = () => {
  request
    .get({
      url: "fdd/signTemplateList",
    })
    .then((res) => {
      templateList.value = res.data.signTemplates;
      if (templateList.value.length > 0) {
        template.value = templateList.value[0];
        contract.value.template_id = template.value.signTemplateId;
      }
    });
};

const onSubmit = () => {
  request
    .post({
      url: `contract/${contract.value.id}/submit`,
      data: {
        template_id: template.value.signTemplateId,
      },
    })
    .then((res) => {
      if (contract.value.type === 2) {
        createSignTask(contract.value.id);
      } else {
        Taro.redirectTo({
          url:
            "/pages/contract/createResult/createResult?id=" + contract.value.id,
        });
      }
      //handleConfirm();
    });
};

const createSignTask = (id) => {

  request
    .post({
      url: "fdd/createSignTask",
      data: {
        contractId: id,
      },
    })
    .then((res) => {
      // Taro.redirectTo({
      //   url:
      //     "/pages/contract/createResult/createResult?id=" + contract.value.id,
      // });
      request
      .get({
        url: "fdd/getSignTaskUrl/" + id,
        data: {
          actorId: '房东'
        }
      })
      .then((res) => {
        Taro.navigateTo({
          url: '/pages/webview/webview?url=' + encodeURIComponent(res.data.actorSignTaskEmbedUrl)
        })
      });
      //handleClick();
    });
};

const handlePreviewContract = () => {
  Taro.navigateTo({
    url: "/pages/article/article?key=hetongmoban",
  });
};

const showDel = ref(false);
const showConfirm = ref(false);
const handleClick = () => {
  showDel.value = !showDel.value;
};

const handleConfirm = () => {
  if (!showConfirm.value && !contract.value.contract_rent) {
    Taro.showToast({
      title: "请先完善租金",
      icon: "none",
    });
    return;
  }
  showConfirm.value = !showConfirm.value;
  onSave()
};

const onSave = () => {
  request.post({
    url: 'contract/' + contract.value.id + '/update',
    data: {
      remark: contract.value.remark
    }
  }).then(res => {
    Taro.showToast({
      title: '更新成功！'
    })
  })
}
</script>

<style lang="scss">
page {
  background: #f7f9ff;
}

.top {
  height: 158px;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding-left: 40px;
  .top-title {
    font-size: 32px;
    font-family: OPPOSans;
    font-weight: 500;
    color: #000000;
  }
  .top-sn {
    font-size: 26px;
    font-family: OPPOSans;
    font-weight: 500;
    color: #b6bec5;
    margin-top: 12px;
  }
}

.list-box {
  margin-top: 32px;
}
.card {
  width: 700rpx;
  background: #ffffff;
  box-shadow: 0rpx 4rpx 32rpx 0rpx rgba(182, 190, 201, 0.38);
  border-radius: 20rpx;
  margin: 24rpx;
  overflow: hidden;
  textarea{
    font-size: 26px;
    width: 100%;
  }
  .btn-save{
    width: 100%;
    height: 88rpx;
    background-color: #1352fd;
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 32rpx;
    border-radius: 10rpx;
    &:active{
      opacity: 0.75;
    }
  }
  .card-top {
    height: 118rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 38rpx;
    font-size: 36rpx;
    font-family: OPPOSans;
    font-weight: 500;
    color: #000000;
    .card-top_right {
      font-size: 24rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #1352fd;
    }
  }
  .card-item {
    padding: 0 38rpx 24rpx;
    font-size: 26rpx;
    font-family: OPPOSans;
    font-weight: 500;
    color: #b6bec5;
    display: flex;
    align-items: center;
    .card-item_content {
      color: #000;
      font-size: 24rpx;
      margin-left: 22rpx;
      display: flex;
      align-items: center;
      .card-item_phone {
        width: 30rpx;
        height: 30rpx;
        margin-left: 16rpx;
      }
    }
    .card-item_look {
      color: #1352fd;
    }
  }
}

.list {
  height: 120px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #dfdfdf;
  padding: 0 40px;
  .list-label {
    font-size: 32px;
    font-family: OPPOSans;
    font-weight: 500;
    color: #000000;
    &::before {
      content: "*";
      color: #ff1616;
      margin-right: 4px;
    }
     &.no-required {
        &::before {
          content: "";
        }
      }
  }
  
  .list-right {
    padding-right: 30px;
    background: url("https://yimidb.oss-cn-hangzhou.aliyuncs.com/mp/images/contract/list-more.png")
      right center no-repeat;
    background-size: 15px auto;
    font-size: 30px;
    font-family: OPPOSans;
    font-weight: 500;
    color: #1352fd;
  }
  .list-success {
    color: #000;
  }
}

.bottomBtn {
  margin: 0 75px 0 50px;
}

.mask {
  width: 100vw;
  height: 100vh;
  position: fixed;
  left: 0;
  bottom: 0;
  z-index: 10;
  background-color: rgba($color: #000000, $alpha: 0.7);
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  .mask-bottom {
    border-top: 1rpx solid #dfdfdf;
    width: 750rpx;
    height: 154rpx;
    background: #ffffff;
    padding-bottom: env(safe-area-inset-bottom);
    display: flex;
    align-items: center;
    .mask-delBtn {
      width: 264rpx;
      height: 88rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 30rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #000000;
    }
    .mask-conBtn {
      flex: 1;
      height: 88rpx;
      background: #1352fd;
      border-radius: 20rpx;
      margin-right: 25rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 36rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #ffffff;
    }
  }
  .mask-content {
    width: 750rpx;
    background: #ffffff;
    border-radius: 30rpx 30rpx 0rpx 0rpx;
    .mask-title {
      height: 120rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 36rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #000000;
    }
    .mask-info {
      padding: 0 44rpx 50rpx;
      font-size: 30rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #000000;
    }
    .mask-info_list {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 34rpx;
      font-size: 30rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #000000;
    }
  }
}
</style>
