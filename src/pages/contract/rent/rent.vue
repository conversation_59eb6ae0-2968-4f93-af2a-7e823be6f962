<!--
 * @Autor: lisong
 * @Date: 2023-08-12 14:12:04
 * @LastEditors: lisong
 * @LastEditTime: 2023-08-15 17:38:58
-->
<template>
  <view class="list-box">
    <view class="list">
      <view class="list-label">共计</view>
      <view class="list-right_box">
        <input
          v-model="form.month_num"
          type="number"
          placeholder="请输入"
          class="list-right"
        />
        <view class="list-right_unit" style="margin-left: 20rpx">个月</view>
      </view>
    </view>
    <view class="list">
      <view class="list-label">开始时间</view>
      <picker mode="date" :value="form.start_at" @change="handleDateChange">
        <view
          :class="[
            'list-right',
            'list-more',
            {
              'list-none': !form.start_at,
            },
          ]"
          >{{ form.start_at || "请选择时间" }}</view
        >
      </picker>
    </view>
    <view class="list">
      <view class="list-label">结束时间</view>
      <view
        :class="[
          'list-right',
          {
            'list-none': !form.end_at,
          },
        ]"
        >{{ form.end_at || "结束日期" }}</view
      >
    </view>
    <view class="list">
      <view class="list-label">租金</view>
      <view class="list-right_box">
        <input
          v-model="form.amount"
          type="digit"
          placeholder="请输入"
          class="list-right"
          @tap="onInputFocus('amount')"
        />
        <view class="list-right_unit" style="margin-left: 20rpx">元/月</view>
      </view>
    </view>
    <view class="list" v-if="pageType !== 'xuzu'">
      <view class="list-label">押金</view>
      <input
        v-model="form.deposit"
        type="digit"
        placeholder="请输入"
        class="list-right"
        @tap="onInputFocus('deposit')"
      />
    </view>
    <view class="list">
      <view class="list-label">收租方式</view>
      <view class="list-right_box">
        <view
          :class="[
            'list-right_label',
            {
              'list-right_labelSelect': form.pay_type === item.status,
            },
          ]"
          v-for="item in labelList"
          :key="item.status"
          @tap="handleSelect(item.status)"
          >{{ item.name }}</view
        >
      </view>
    </view>
    <view class="list" v-if="form.pay_type === 2">
      <view class="list-label">固定日期</view>
<!--      <input-->
<!--        v-model="form.pay_day"-->
<!--        type="number"-->
<!--        class="list-right"-->
<!--        placeholder="请输入"-->
<!--      />-->
      <view
        @tap="onShowDay"
        :class="[
          'list-right',
          {
            'list-none': !form.pay_day,
          },
        ]"
      ><text v-if="form.pay_day">{{form.pay_day + '号'}}</text><text v-else>请指定</text></view>
    </view>
    <view class="list" v-if="form.pay_type === 1">
      <view class="list-label">提前天数</view>
      <input
        type="number"
        v-model="form.days"
        class="list-right"
        placeholder="请输入"
      />
    </view>
    <view class="list">
      <view class="list-label">付款方式</view>
      <picker
        mode="selector"
        :range="yjList"
        range-key="label"
        @change="handleYjSelect"
        class="picker"
      >
        <view
          :class="[
            'list-right',
            'list-more',
            {
              'list-none': !yajin,
            },
          ]"
          >{{ yajin || "请选择方式" }}</view
        >
      </picker>
    </view>
    
  </view>
  <view class="list-box" v-if="pageType === 'xuzu'">
    <view class="list-user_box">
      <view class="list-user">
        <view class="list-user_label">租客姓名</view>
        <view class="list-user_content">{{
          contract.master_tenant?.name
        }}</view>
      </view>
      <view class="list-user">
        <view class="list-user_label">身份证号</view>
        <view class="list-user_content">{{
          contract.master_tenant?.id_no
        }}</view>
      </view>
      <view class="list-user">
        <view class="list-user_label">证件照片</view>
        <view class="list-user_content">
          <view
            class="card-item_look"
            v-if="
              contract.master_tenant?.id_card_image1 ||
              contract.master_tenant?.id_card_image2
            "
            @tap="handlePreviewIdImage"
          >
            查看
          </view>
          <view v-else>未上传</view>
        </view>
      </view>
    </view>
  </view>

  <myBottomBtn
    v-if="pageType !== 'xuzu'"
    btnTxt="账单明细"
    @click="handleSubmit"
  >
  </myBottomBtn>
  <myBottomBtn v-else btnTxt="确认保存" @click="handleSave">
    <view @tap="onPreviewBill" style="padding: 0 30rpx">账单明细</view>
  </myBottomBtn>
  <MyPopup :show="showDay" title="选择固定收租日期" @close="onShowDay">
    <template #content>
      <scroll-view :scroll-y="true" style="height: 300rpx;">
        <view class="pay-day" :class="pay_day === d ? 'pay-day-active' : ''" v-for="d in dayArr" :key="d" @tap="onChoosePayDay(d)">{{d}}号</view>
      </scroll-view>
      <view class="p20"><button class="btn-primary" @tap="onConfirmPayDay">确认修改</button></view>
    </template>
  </MyPopup>
</template>

<script setup>
import { ref } from "vue";
import request from "@/utils/request";
import myBottomBtn from "@/components/MyBottomBtn/MyBottomBtn";
import MyPopup from "@/components/MyPopup";
import { getApiRoot } from "@/config";
import { addMonth, addDay } from "@/utils";
import {validation} from "@/utils/validation";
import { useGlobalStore } from "@/stores";
import Taro, {
  useDidShow,
  useDidHide,
  useReady,
  useLoad,
  usePullDownRefresh,
  useReachBottom,
} from "@tarojs/taro";
const globalStore = useGlobalStore();
definePageConfig({
  navigationBarTitleText: "填写租金",
  navigationBarBackgroundColor: "#1352FD",
  navigationBarTextStyle: "white",
});

let pageType = ref("");
useLoad((options) => {
  form.value.contract_id = options.contract_id;
  if (options.type === "xuzu") {
    pageType.value = options.type;
    Taro.setNavigationBarTitle({
      title: "续租",
    });
  }
  getContractDetail();
  for (let i = 1; i <= 31; i ++) {
    dayArr.value.push(i)
  }
});

const dayArr = ref([])
const showDay = ref(false)
const onShowDay = () => {
  console.log('onshowDay')
  showDay.value = !showDay.value
}
const pay_day = ref(1)
const onChoosePayDay = (d) => {
  pay_day.value = d
}

const onConfirmPayDay = () => {
  form.value.pay_day = pay_day.value
  onShowDay()
}
const contract = ref({});
const getContractDetail = () => {
  request
    .get({
      url: "contract/" + form.value.contract_id,
    })
    .then((res) => {
      if (res.data.contract_rent) {
        if (pageType.value === "xuzu") {
          // 参数重置
          res.data.contract_rent.start_at = addDay(new Date(res.data.contract_rent.end_at), 1);
          res.data.contract_rent.month_num = 12;
          res.data.contract_rent.end_at = addMonth(
            new Date(res.data.contract_rent.start_at),
            res.data.contract_rent.month_num
          );
        }
        form.value = { ...form.value, ...res.data.contract_rent };
        yajin.value = res.data.contract_rent.rent_num_name;
        pay_day.value = form.value.pay_day;
        //yajin.value = `押${res.data.contract_rent.deposit_num}付${res.data.contract_rent.rent_num}`;
      } else {
        form.value = {
          ...form.value,
          ...{
            amount: res.data.house.rent
          },
        };
      }
      contract.value = res.data;
    });
};

const form = ref({
  month_num: 12,
  pay_type: 2,
  pay_day: 1,
  deposit: 0,
  is_deposit_paid: 0,
  rent_num: 3
});

const labelList = [
  {
    name: "固定日收租",
    status: 2,
  },
  {
    name: "提前收租",
    status: 1,
  },
];

const yjList = [
  { label: "月付", value: 0 },
  { label: "季付", value: 1 },
  { label: "半年付", value: 2 },
  { label: "年付", value: 3 },
];

const yjValue = [
  { deposit_num: 0, rent_num: 1 },
  { deposit_num: 0, rent_num: 3 },
  { deposit_num: 0, rent_num: 6 },
  { deposit_num: 0, rent_num: 12 },
];

const yajin = ref("季付");

const handleDateChange = (e) => {
  form.value.start_at = e.detail.value;
  if (form.value.start_at && form.value.month_num) {
    form.value.end_at = addMonth(
      new Date(form.value.start_at),
      form.value.month_num
    );
  }
};

const handleSelect = (type) => {
  form.value.pay_type = type;
};

const handleYjSelect = (e) => {
  const index = e.detail.value;
  yajin.value = yjList[index].label;
  const obj = yjValue[index];
  Object.assign(form.value, obj);
};

const handleSubmit = () => {
  
  let rules = {
    month_num: {
      type: 'required',
      message: '请输入租期'
    },
    start_at: {
      type: 'required',
      message: '请输入开始时间'
    },
    amount: {
      type: 'number',
      min: 1,
      message: '请输入正确的租金'
    }
  }
  if (pageType.value != 'xuzu') {
    rules.deposit = {
      type: 'number',
      min: 0.01,
      message: '请输入正确的押金'
    }
  }

  validation(form.value, rules).then(() => {
    globalStore.setYjData(form.value);
    Taro.navigateTo({
      url: "/pages/contract/billList/billList",
    });
  }).catch((err) => {
    Taro.showToast({
      title: err,
      icon: 'error'
    })
  })
};

const handleSave = () => {

    let rules = {
    month_num: {
      type: 'required',
      message: '请输入租期'
    },
    start_at: {
      type: 'required',
      message: '请输入开始时间'
    },
    amount: {
      type: 'number',
      min: 1,
      message: '请输入正确的租金'
    }
  }
  if (pageType.value != 'xuzu') {
    rules.deposit = {
      type: 'number',
      min: 0.01,
      message: '请输入正确的押金'
    }
  }

  validation(form.value, rules).then(() => {
    request
    .post({
      url: `contract/${form.value.contract_id}/changeExpireDate`,
      data: {
        ...form.value,
      },
    })
    .then((res) => {
      Taro.navigateBack();
    });
  }).catch((err) => {
    Taro.showToast({
      title: err,
      icon: 'error'
    })
  })
};

const handlePreviewIdImage = () => {
  let images = [];
  if (contract.value.master_tenant.id_card_image1) {
    images.push(contract.value.master_tenant.id_card_image1);
  }
  if (contract.value.master_tenant.id_card_image2) {
    images.push(contract.value.master_tenant.id_card_image2);
  }
  Taro.previewImage({
    current: "", // 当前显示图片的http链接
    urls: images, // 需要预览的图片http链接列表
  });
};

const onPreviewBill = () => {
  if (!form.value.month_num || !form.value.start_at || !form.value.amount) {
    Taro.showToast({
      title: "表单填写不全",
      icon: "error",
    });
    return;
  }
  globalStore.setYjData(form.value);
  Taro.navigateTo({
    url: "/pages/contract/billList/billList?type=xuzu",
  });
}
const onInputFocus = (field) => {
  console.log('onInputFocus', field)
  if (form.value[field] == 0) {
    form.value[field] = ''
  }
}

</script>

<style lang="scss">
page {
  background: #f7f9ff;
}

.list-box {
  margin-top: 32rpx;
}

.list {
  height: 120rpx;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1rpx solid #dfdfdf;
  padding: 0 40rpx;
  .list-label {
    font-size: 32rpx;
    font-family: OPPOSans;
    font-weight: 500;
    color: #000000;
    flex-shrink: 0;
  }
  .list-right {
    font-size: 30rpx;
    font-family: OPPOSans;
    font-weight: 500;
    color: #000;
    text-align: right;
    flex: 1;
  }
  .list-none {
    color: #b6bec5;
  }
  .list-more {
    padding-right: 30rpx;
    background: url("https://yimidb.oss-cn-hangzhou.aliyuncs.com/mp/images/contract/list-more.png")
      right center no-repeat;
    background-size: 15rpx auto;
  }
}

.list-right_box {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  .sendCode {
    margin-left: 40rpx;
  }
  .list-right_unit {
    flex-shrink: 0;
  }
}

.list-right_label {
  padding: 8rpx 14rpx;
  background: #f1f3f7;
  border-radius: 6rpx;
  font-size: 24rpx;
  font-family: OPPOSans;
  font-weight: 400;
  color: #b6bec5;
  margin-left: 16rpx;
}

.list-right_labelSelect {
  background: #1352fd;
  color: #fff;
}

.picker {
  flex: 1;
}

.list-user_box {
  padding: 22rpx 0;
  background-color: #fff;
}

.list-user {
  background: #fff;
  padding: 22rpx 40rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .list-user_label {
    font-size: 30rpx;
    font-family: OPPOSans;
    font-weight: 500;
    color: #b6bec5;
  }
  .list-user_content {
    font-size: 30rpx;
    font-family: OPPOSans;
    font-weight: 500;
    color: #000000;
  }
  .card-item_look {
    color: #1352fd;
  }
}
.pay-day {
  font-size: 32px;
  padding: 30px;
  text-align: center;
  border-top: 1px solid #dfdfdf;
}
.pay-day-active {
  border-left: 2px solid #1352FD;
  color: #1352FD;
  font-weight: 500;
}
</style>
