.container {
  padding: 0 0 240px 0;
  .emty {
    text-align: center;
    line-height: 50px;
    color: #b6bec5;
    margin-top: 100px;
  }
  .scrollview {
    /* 滚动条样式 */
    ::-webkit-scrollbar {
      width: 0px; /* 滚动条宽度 */
    }

    /* 滚动条轨道颜色 */
    ::-webkit-scrollbar-track {
      background-color: transparent; /* 透明背景 */
    }

    /* 滚动条滑块颜色 */
    ::-webkit-scrollbar-thumb {
      background-color: rgba(0, 0, 0, 0); /* 半透明黑色滑块 */
      // border-radius: 10px; /* 圆角滑块 */
    }
    .status-filter {
      display: flex;
      justify-content: space-between;
      align-items: center;
      // padding: 0 20px;
      margin-top: 10px;

      .tag {
        height: 43px;
        background: #ebefff;
        border-radius: 21px;
        border: 1px solid #aebbef;
        text-align: center;
        line-height: 43px;
        opacity: 0.9;
        font-size: 24px;
        padding: 0 15px;
        flex-shrink: 0;
        margin-right: 25px;
      }
    }
  }
}
