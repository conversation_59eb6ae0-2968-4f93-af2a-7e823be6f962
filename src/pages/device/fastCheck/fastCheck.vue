<template>
  <view>
    <scroll-view scroll-y class="scroll-box">
      <view class="info-box">
        <template v-if="isOverdue || isOverdue == 'emty'">
          <view class="top-box">
            <view class="title" v-if="detailData"> 入住信息 </view>
            <text class="edit" @tap="xuzuHandel(1)" v-if="detailData">保存</text>
            <my-cell
              prefix="租客电话（必填）"
              prefixWidth="320"
              align="right"
              required
            >
              <template #content
                ><input
                  class="my-input-m"
                  v-model="From.phone"
                  type="number"
                  placeholder="请输入"
                  :disabled="isOverdue && curItem?.lockId && From.phone.length == 11"
              /></template>
            </my-cell>
            <my-cell prefix="房屋租金" prefixWidth="280" align="right">
              <template #content>
                <view class="content">
                  <input
                    class="my-input-m"
                    v-model="From.amount"
                    type="digit"
                    placeholder="请输入"
                  />
                  <text>元/月</text>
                </view>
              </template>
            </my-cell>
            <my-cell prefix="房屋押金" prefixWidth="280" align="right">
              <template #content>
                <view class="content">
                  <input
                    class="my-input-m"
                    v-model="From.money"
                    type="digit"
                    placeholder="请输入"
                  />
                  <text>元</text>
                </view>
              </template>
            </my-cell>
            <my-cell prefix="下次交租日" prefixWidth="280" align="right">
              <template #content>
                <picker mode="date" @change="onDateChange">
                  <view class="picker">
                    <text v-if="!From.date">请选择日期</text>
                    <text v-else>{{ From.date }}</text>
                  </view>
                </picker>
              </template>
            </my-cell>
            <my-cell prefix="逾期断电" prefixWidth="600" align="right">
              <template #prefix>
                <view>
                  <view>逾期断电</view>
                  <view style="font-size: 24rpx;">开通逾期断电后逾期次日18:00-20:00自动断电</view>
                </view>
              </template>
              <template #content>
                <AtSwitch
                  title=""
                  :checked="From.is_overdue"
                  @change="changeAutoSwitch($event)"
                  size="small"
                  style="
                  "
                />
              </template>
            </my-cell>
            <my-cell
              v-if="curItem?.need_people == 1 || curItem?.need_people == 2"
              prefix="入住人数"
              prefixWidth="280"
              align="right"
              required
            >
              <template #content>
                <view class="content">
                  <input
                    class="my-input-m"
                    v-model="From.num"
                    placeholder="请输入"
                  />
                  <text>人</text>
                </view>
              </template>
            </my-cell>
          </view>
          <!-- <view class="yuqi-box">开通后逾期次日18:00-20:00自动断电</view> -->
        </template>
        <!-- 如果没到期展示 -->
        <view class="look-box" v-else>
          <view class="title"> 入住信息 </view>
          <text
            class="edit"
            @tap="isOverdue = true"
            v-if="globalStore.who == 'business'"
            >编辑</text
          >
          <view class="item">
            <text class="label">租客电话</text>
            <text class="text">{{ From.phone }}</text>
          </view>
          <view class="item">
            <text class="label">房屋租金</text>
            <text class="text">{{ From.amount }}元/月</text>
          </view>
          <view class="item">
            <text class="label">房屋押金</text>
            <text class="text">{{ From.money }}元</text>
          </view>
          <view class="item">
            <text class="label">下次交租日</text>
            <text class="text">{{ From.date }}</text>
          </view>
          <view
            class="item"
            v-if="curItem?.need_people == 1 || curItem?.need_people == 2"
          >
            <text class="label">入住人数</text>
            <text class="text">{{ From.num }}人</text>
          </view>
          <view
            class="item"
            v-if="calculateDaysBetweenDates(new Date(), From.date) >= 0"
          >
            <text class="label">租期剩余</text>
            <text class="text"
              >{{ calculateDaysBetweenDates(new Date(), From.date) }}天</text
            >
          </view>
          <view class="item" v-else>
            <text class="label" style="color: red">租期逾期</text>
            <text class="text" style="color: red"
              >{{
                Math.abs(calculateDaysBetweenDates(new Date(), From.date))
              }}天</text
            >
          </view>
          <view class="item" style="height: 70rpx;">
            <text class="label">逾期断电</text>
            <AtSwitch
              title=""
              class="text"
              disabled=""
              :checked="From.is_overdue"
              size="small"
              style="
              "
            />
           <view class="yuqi-box">开通逾期断电后逾期次日18:00-20:00自动断电</view>
          </view>
        </view>
      </view>
    </scroll-view>
    <view v-if="detailData?.contract_tenant.length" class="zuke">
      <view class="title"> 租客信息 </view>
      <view class="tabs">
        <scroll-view scroll-x="true" class="tabs-scr">
          <view class="content">
            <view
              class="tab-item"
              :class="{ active: item === tabActiveIndex }"
              v-for="(item, idx) in num"
              :key="item"
              @tap="tabActiveIndex = item"
            >
              第{{ item }}位租客
              <text
                class="icon-close iconfont"
                @tap.stop="del(idx)"
                v-if="isEdit"
              ></text>
            </view>
          </view>
        </scroll-view>
      </view>
      <view scroll-y class="scroll-box2">
        <view
          class="box"
          v-for="(item, idx) in num"
          :key="item"
          v-show="item == tabActiveIndex"
        >
          <view class="top-card">
            <view>
              <view class="left" v-if="detailData?.need_people == 1">
                <text class="b">头像面</text>
                <text>上传您的证件头像面</text>
              </view>
              <view class="left" v-else-if="detailData?.need_people == 2">
                <text class="b">自主申报截图</text>
                <text>租客上传的自主申报截图</text>
              </view>
              <view class="right" @tap="uploadImage(0, idx, $event)">
                <image :src="userFrom[idx].idRen" mode="aspectFit" />
                <view class="demo-mask" v-if="detailData?.need_people == 2">
                  <view>点击放大查看</view>
                </view>
                <!-- <text
                class="icon-close iconfont"
                v-if="userFrom[idx].id_card_image1"
                id="close"
                @tap.stop="delImg(0, idx)"
              ></text> -->
              </view>
            </view>
            <view v-if="userFrom[idx].id_card_image2">
              <view class="left">
                <text class="b">国徽面</text>
                <text>上传您的证件国徽面</text>
              </view>
              <view class="right" @tap="uploadImage(1, idx, $event)">
                <image :src="userFrom[idx].idBei" mode="scaleToFill" />
                <!-- <text
                class="icon-close iconfont"
                v-if="userFrom[idx].id_card_image2"
                id="close"
                @tap.stop="delImg(1, idx)"
              ></text> -->
              </view>
            </view>
          </view>
          <view class="bot-box">
            <my-cell prefix="联系电话" align="right" required>
              <template #content
                ><input
                  class="my-input-m"
                  v-model="userFrom[idx].mobile"
                  :disabled="true"
                  placeholder="请输入"
              /></template>
            </my-cell>
            <my-cell
              prefix="承租人(乙方)"
              align="right"
              prefixWidth="280"
              required
            >
              <template #prefix>
                <text>{{
                  userFrom[idx].is_master == 0 ? "入住人(乙方)" : "承租人(乙方)"
                }}</text>
              </template>
              <template #content
                ><input
                  class="my-input-m"
                  :disabled="true"
                  v-model="userFrom[idx].name"
                  placeholder="请输入"
              /></template>
            </my-cell>
            <my-cell prefix="证件号码" align="right" required>
              <template #content
                ><input
                  class="my-input-m"
                  v-model="userFrom[idx].id_no"
                  :disabled="true"
                  placeholder="请输入"
              /></template>
            </my-cell>
            <my-cell
              prefix="是否承租人"
              prefixWidth="280"
              align="right"
              required
            >
              <template #content>
                <view class="select-box">
                  <view :class="userFrom[idx].is_master == 1 ? 'active' : ''"
                    >承租人</view
                  >
                  <view
                    :class="userFrom[idx].is_master == 0 ? 'active' : ''"
                    v-if="num > 1"
                    >入住人</view
                  >
                </view>
              </template>
            </my-cell>
            <my-cell
              prefix="是否申报"
              prefixWidth="280"
              v-if="detailData?.need_people == 2"
              align="right"
              required
            >
              <template #content>
                {{
                  detailData?.need_people == 2 && detailData?.check_status == 0
                    ? "未确认申报"
                    : detailData?.need_people == 2 &&
                      detailData?.check_status == 1
                    ? "已确认申报"
                    : detailData?.check_status == 2
                    ? "已拒绝申报"
                    : "未知"
                }}
              </template>
            </my-cell>
            <my-cell
              v-if="
                detailData?.need_people == 2 &&
                detailData?.check_status == 0 &&
                globalStore.who == 'business'
              "
              prefixWidth="0"
            >
              <template #content>
                <view class="shenbao-box">
                  <view class="shenbao-btn">
                    <view
                      :class="{ active: true }"
                      @tap="shenheHandel(userFrom[idx], 2)"
                      >审核不通过</view
                    >
                  </view>
                  <view class="shenbao-btn">
                    <view
                      :class="{ active: true }"
                      @tap="shenheHandel(userFrom[idx], 1)"
                      >审核通过</view
                    >
                  </view>
                </view>
              </template>
            </my-cell>
          </view>
        </view>
      </view>
      <view class="footer-fixed" v-if="globalStore.who == 'business'">
        <view class="p20">
          <button class="btn-primary" @tap="onSubmitBindBank">确定保存</button>
        </view>
      </view>
    </view>
    <view
      class="footer-fixed"
      v-if="(isOverdue || isOverdue == 'emty') && globalStore.who == 'business'"
    >
      <view class="p20">
        <!-- <button @tap="onSubmitBindBank" :open-type="curItem?.estate_name ? 'share' : ''" :plain="true" size="mini" class="share-btn" >
        </button> -->
        <button class="btn-primary" @tap="onSubmitBindBank">
          {{ curItem?.estate_name ? "确认并分享" : "确认" }}
        </button>
      </view>
    </view>
    <view
      class="footer-fixed"
      v-if="detailData && globalStore.who == 'business'"
    >
      <view class="box">
        <picker mode="date" class="btn-primary" @change="onDateChangeXuzu">
            <view class="picker" style="display: flex;justify-content: center;align-items: center;height: 88rpx;">
                续租
            </view>
        </picker>
        <button class="btn-primary" @tap="shareHandel">分享给租客</button>
        <button class="btn-primary" @tap="tuizuHandel">退租</button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, unref, computed, watch } from "vue";
import request from "@/utils/request";
import MyCell from "@/components/MyCell";
import MyPopup from "@/components/MyPopup";
import MyInput from "@/components/MyInput";
import { clientId, clientSecret } from "@/config";
import { AES_Encrypt, AES_Decrypt, MD5_Encrypt } from "@/utils/crypto";
import upload from "@/utils/upload";
import {
  sendKeyHandel,
  getKeyHandel,
  delKeyHandel,
  sendKey,
} from "@/utils/lock";
import { AtSwitch } from "taro-ui-vue3/lib";

import Taro, {
  useDidShow,
  useDidHide,
  useUnload,
  useReady,
  useLoad,
  usePullDownRefresh,
  useReachBottom,
  useShareAppMessage,
} from "@tarojs/taro";
import { useGlobalStore } from "@/stores";
definePageConfig({
  navigationBarTitleText: "快速入住",
  navigationBarBackgroundColor: "#1352fd",
  navigationBarTextStyle: "white",
  enableShareAppMessage: true,
});

const globalStore = useGlobalStore();

function calculateDaysBetweenDates (date1, date2) {
  if(!date1 || !date2) return NaN
  // 将日期转换为当天的 00:00:00
  const d1 = new Date(date1);
  d1.setHours(0, 0, 0, 0);
  
  const d2 = new Date(date2);
  d2.setHours(0, 0, 0, 0);

  const dayInMs = 24 * 60 * 60 * 1000;
  const diffTime = d2.getTime() - d1.getTime();
  const diffDays = Math.round(diffTime / dayInMs);
  
  return diffDays;
} 
const daysDifference = ref(); // 计算日期差值

const From = reactive({
  phone: "",
  amount: "",
  money: "",
  date: "",
  num: "1",
  is_overdue: false, // 是否逾期断电 0:不断电 1:断电
});

const curItem = ref(null);

const timer = ref(null);

const isOverdue = ref("emty"); //是否过期

watch(() => isOverdue.value, (newVal) => {
  if (newVal === true) {
    clearInterval(timer.value);
  }
})

// 校验
const rules = reactive({
  phone: [{ message: "请输入租客电话" }],
  // amount: [{ message: "请输入房租金额/月" }],
  // money: [{ message: "请输入押金金额" }],
  // date: [{ message: "请选择租金到期日！" }],
});

const shareHandel = () => {
  globalStore.setShare(true);
  setTimeout(() => {
    Taro.navigateTo({
      url: "/pages/device/detail/detail?id=" + curItem.value.id,
    });
  }, 0);
};

const keyId = ref();
useLoad(({ deveice }) => {
  console.log(deveice);
  if (deveice) {
    const item = JSON.parse(deveice);
    curItem.value = item;
    // 10秒轮询一次
    timer.value = setInterval(() => {
      getFastDetail();
    }, 1000 * 15);
    getFastDetail();
    if (item.need_people == 1 || item.need_people == 2) {
      // 需要搜集人数
      rules.num = [{ message: "请输入入住人数" }];
    }
  }
  // 获取钥匙
  if (curItem.value?.lockId) {
    getKeyHandel({ lockId: curItem.value?.lockId }).then((res) => {
      keyId.value = res?.list[0]?.keyId;
    });
  }else {
    getLockId();
  }
});

useDidHide(() => {
  console.log("hide");
  clearInterval(timer.value);
});

useUnload(() => {
  console.log("unload");
  clearInterval(timer.value);
});

// 获取锁id如果有的话
const getLockId = () => {
    request.get({
        url: "device/" + curItem.value.id,
    }).then((res) => {
      request.get({
        url: "house",
        data: {
          page: 1,
          keyword: res.data?.house.name
        },
      }).then((res2) => {
        const loid = res2.data.items[0]?.ttlock && res2.data.items[0]?.ttlock.length
              ? res2.data.items[0]?.ttlock[res2.data.items[0]?.ttlock.length - 1]?.lock_id
              : undefined
        if (!curItem.value.lockId) {
          curItem.value.lockId = loid;
        }
        if (!curItem.value?.house_id) {
          curItem.value.house_id = res2.data.items[0]?.id;
        }
        getKeyHandel({ lockId: loid }).then((res) => {
          keyId.value = res?.list[0]?.keyId;
        });
      })
    })
};

const detailData = ref(null);

// 获取快捷入住详情
const getFastDetail = () => {
  request
    .get({
      url: "contract/fastDetail",
      data: {
        device_id: curItem.value.id,
      },
    })
    .then((res) => {
      detailData.value = res.data;
      if (res.code == 200 && res.data) {
        init();
        isOverdue.value = false;
        From.phone = res.data.mobile;
        From.amount = res.data.contract_rent.amount;
        From.money = res.data.contract_rent.deposit;
        From.date = res.data.expired_at;
        From.is_overdue = res.data.is_fail_close == 1 ? true : false;
        if (
          unref(curItem)?.need_people == 1 ||
          unref(curItem)?.need_people == 2
        ) {
          From.num = res.data.teanet_num;
        }
        // 如果没到期 == 2 ? true : false
        // isOverdue.value = res.data.expire_status == 2 ? true : false;
      }
    });
};

const idRen = ref(
  "https://yimits.oss-cn-beijing.aliyuncs.com/images/ren-id.png"
);

const idBei = ref(
  "https://yimits.oss-cn-beijing.aliyuncs.com/images/ren-id-2.png"
);

const tabActiveIndex = ref(1);

const num = ref(1); //几人

const contract_id = ref("");

const isAp = process.env.TARO_ENV === "alipay";

const curentTime = ref("请选择日期");
const isEdit = ref(false);

const userFrom = ref([
  {
    id: null,
    mobile: "",
    name: "",
    id_no: "",
    id_card_image1: "",
    id_card_image2: "",
    is_master: 0,

    idRen: idRen.value,
    idBei: idBei.value,
  },
]);

const init = () => {
  num.value = detailData.value.contract_tenant.length || 1;
  for (let i = 1; i <= detailData.value.contract_tenant.length; i++) {
    userFrom.value.push({
      id: null,
      mobile: "",
      name: "",
      id_no: "",
      id_card_image1: "",
      id_card_image2: "",
      is_master: 0,

      idRen: idRen.value,
      idBei: idBei.value,
    });
    if (i == 1) {
      userFrom.value[0].is_master = 1;
    }
  }
  console.log(detailData.value.contract_tenant);
  detailData.value.contract_tenant.forEach((el, idx) => {
    userFrom.value[idx] = el;
    userFrom.value[idx].idRen = el.id_card_image1;
    userFrom.value[idx].idBei = el.id_card_image2;
  });
};

const changeAutoSwitch = (e) => {
  console.log(e);
  From.is_overdue = e
};

// 点击预览
const previewImage = (type, index) => {
  if (type == 0) {
    Taro.previewImage({
      urls: [userFrom.value[index].idRen],
      current: userFrom.value[index].idRen,
    });
  } else if (type == 1) {
    Taro.previewImage({
      urls: [userFrom.value[index].idBei],
      current: userFrom.value[index].idBei,
    });
  } else if (type == 2) {
    console.log("cloick");
    Taro.previewImage({
      urls: [qiyeFrom.yingImg],
      current: qiyeFrom.yingImg,
    });
  }
};

// 预览
const uploadImage = (type, index, event) => {
  console.log(event);
  if (event.mpEvent.target.id == "close") return;
  if (type == 0 && !unref(userFrom)[index].idRen.includes("ren-id"))
    return previewImage(type, index);
  if (type == 1 && !unref(userFrom)[index].idBei.includes("ren-id"))
    return previewImage(type, index);
  // type: 0是身份证人像  1是身份证背面
  upload.chooseImage(1).then((images) => {
    images.map((img) => {
      upload.uploadFile(img).then((url) => {
        if (type == 0) {
          userFrom.value[index].idRen = url;
          userFrom.value[index].id_card_image1 = url;
          // idRen.value = url;
        } else if (type == 1) {
          // idBei.value = url;
          userFrom.value[index].idBei = url;
          userFrom.value[index].id_card_image2 = url;
        }
        console.log(url);
        imageDataHandel(type, url, index);
      });
    });
  });
};

const onDateChange = (e) => {
  From.date = e.detail.value;
};

const onDateChangeXuzu = (e) => {
  From.date = e.detail.value;
  xuzuHandel(0)
};

const validate = () => {
  let valid = true;
  for (let i = 0; i < Object.keys(From).length; i++) {
    const key = Object.keys(From)[i];
    console.log(From[Object.keys(From)[i]]);
    if (From[Object.keys(From)[i]] == "") {
      if (rules[key]) {
        Taro.showToast({
          title: rules[key][0].message,
          icon: "none",
        });
        valid = false;
        break;
      }
    }
  }
  return valid;
};

const onSubmitBindBank = () => {
  if (!validate()) return;
  if (/^(?:(?:\+|00)86)?1[3-9]\d{9}$/.test(From.phone) == false) {
    Taro.showToast({
      icon: "none",
      title: "请输入正确的手机号!",
    });
    return;
  }
  if (From.num <= 0) {
    Taro.showToast({
      icon: "none",
      title: "入住人数最少为一人!",
    });
    return;
  }
  console.log("ok");
  request
    .post({
      url: "contract/createFast",
      data: {
        device_id: unref(curItem).id,
        mobile: From.phone,
        teanet_num:
          unref(curItem)?.need_people == 1 || unref(curItem)?.need_people == 2
            ? From.num
            : undefined,
        end: From.date,
        amount: From.amount,
        deposit: From.money,
        is_fail_close: From.is_overdue ? 1 : 0, // 是否逾期断电
      },
    })
    .then(async (res) => {
      if (res.code == 200) {
        // 发钥匙
        Taro.showToast({
          icon: "success",
          title: "保存成功",
        });
        if (curItem.value?.estate_name) {
          globalStore.setShare(true);
          setTimeout(() => {
            Taro.navigateBack({
              delta: 1,
            });
          }, 300);
        } else {
          setTimeout(() => {
            Taro.navigateBack({
              delta: 1,
            });
          }, 1000);
        }
        sendKey({
          lockId: curItem.value?.lockId,
          phone: From.phone,
          house_id: curItem.value?.house_id, // 房源id
        });
        getFastDetail();
      }
    });
};

// const sendKey = async () => {
//   return new Promise((resolve, reject) => {
//     if (!curItem.value?.lockId) return resolve();
//     const option = {
//       username: `${From.phone}shanzupotenant`,
//       password: MD5_Encrypt(From.phone),
//       date: new Date().getTime(),
//     };
//     request
//       .post({
//         url: "/v3/user/register",
//         LOCKYAPI: true,
//         data: {
//           clientId,
//           clientSecret,
//           ...option,
//         },
//       })
//       .then((res) => {
//         sendKeyHandel({
//           lockId: curItem.value?.lockId,
//           receiverUsername: `didfj_${globalStore.userInfo?.mobile}shanzupotenant`,
//           keyName: `${globalStore.userInfo?.nickname || "租客"}-${
//             globalStore.userInfo?.mobile
//           }`,
//         })
//           .then(() => {
//             resolve();
//           })
//           .catch((err) => {
//             resolve();
//           });
//       })
//       .catch((err) => {
//         resolve();
//       });
//   });
// };

// 续租
const xuzuHandel = (t = 0) => {
  if (!validate()) return;
  request
    .post({
      url: "contract/fastLease",
      data: {
        id: unref(detailData).id,
        mobile: From.phone,
        teanet_num:
          unref(curItem)?.need_people == 1 || unref(curItem)?.need_people == 2
            ? From.num
            : undefined,
        end: From.date,
        amount: From.amount,
        deposit: From.money,
        is_fail_close: From.is_overdue ? 1 : 0, // 是否逾期断电
      },
    })
    .then((res) => {
      if (res.code == 200) {
        Taro.showToast({
          icon: "success",
          title: t == 0 ? "续租成功" : "保存成功",
        });
          sendKey({
            lockId: curItem.value?.lockId,
            phone: From.phone,
            house_id: curItem.value?.house_id, // 房源id
          });
        setTimeout(() => {
          getFastDetail();
          // Taro.navigateBack({
          //   delta: 1,
          // });
        }, 1500);
      }
    });
};

// 退租
const tuizuHandel = () => {
  Taro.showModal({
    title: "提示",
    content: "确定要退租吗？",
    success: (res) => {
      if (res.confirm) {
        request
          .post({
            url: `contract/${unref(detailData).id}/breakFast`,
          })
          .then((res) => {
            if (res.code == 200) {
              // 删除钥匙
              // 获取钥匙
              if (curItem.value?.lockId && !keyId.value) {
                getKeyHandel({ lockId: curItem.value?.lockId }).then((res) => {
                  keyId.value = res?.list[0]?.keyId;
                  if (keyId.value) {
                    delKeyHandel({
                      keyId: keyId.value,
                    });
                  }
                });
              } else {
                if (keyId.value) {
                  delKeyHandel({
                    keyId: keyId.value,
                  });
                }
              }
              Taro.showToast({
                icon: "success",
                title: "退租成功",
              });
              getFastDetail();
              setTimeout(() => {
                Taro.navigateBack({
                  delta: 1,
                });
              }, 300);
            }
          });
      }
    },
  });
};

// 审核公安采集
const shenheHandel = (item, type) => {
  getFastDetail();
  // type:2不通过  1通过
  console.log(item);
  Taro.showModal({
    title: "提示",
    content: type == 2 ? "确定不通过吗？" : "确定通过吗？",
    success: (res) => {
      console.log(res);
      if (res.confirm) {
        request
          .get({
            url: `contract/checkTenant`,
            data: {
              contract_id: item.contract_id,
              status: type,
            },
          })
          .then(() => {
            Taro.showToast({
              icon: "success",
              title: "操作成功",
            });
            setTimeout(() => {
              getFastDetail();
            }, 800);
          });
      }
    },
  });
};

const getDate = (type, ap = false) => {
  const date = new Date();
  let year = date.getFullYear();
  let month = date.getMonth() + 1;
  let day = date.getDate();

  if (type === "start") {
    year = year - 60;
  } else if (type === "end") {
    year = year + 2;
  }
  month = month > 9 ? month : "0" + month;
  day = day > 9 ? day : "0" + day;
  if (ap) {
    return `${year}-${month}-${day}`;
  } else {
    return `${year}${month}${day}`;
  }
};

const onShare = () => {
  Taro.showShareMenu({
    menus: ["shareAppMessage"],
    success: () => {
      console.log("success");
    },
    fail: (err) => {
      console.log(err);
    },
  });
};

useShareAppMessage(() => {
  return {
    title: "" + curItem.value.estate_name + curItem.value.name,
    path:
      "/pages/tenant/deviceDetail/deviceDetail?sn=" +
      curItem.value.sn +
      "&share=1",
  };
});
</script>

<style lang="scss">
.yuqi-box {
  font-size: 25rpx;
  text-align: right;
  margin-top: 10rpx;
  margin-right: 15rpx;
  position: absolute;
  bottom: 5rpx;
}
.zuke {
  .title {
    font-weight: 500;
    font-size: 38px;
    color: #000000;
    padding: 20px;
    padding-bottom: 20px;
    background-color: #fff;
  }
}
.shenbao-box {
  display: flex;
  align-items: center;
  justify-content: space-around;
  box-sizing: border-box;
}
.shenbao-btn {
  width: 300px;
  height: 80px;
  background: #1352fd;
  border-radius: 20px;
  font-family: OPPOSans;
  font-weight: 500;
  font-size: 30px;
  color: #ffffff;
  line-height: 80px;
  text-align: center;
}
page {
  background-color: #f1f3f7;
  position: relative;
  .edit {
    position: absolute;
    right: 20px;
    top: 20px;
    font-weight: 500;
    font-size: 35px !important;
    box-sizing: border-box;
    padding: 10px 60px;
    color: #fff;

    background: #1352fd;
    border-radius: 20px;
    transition: all 0.3s;
    &:active {
      opacity: 0.7;
    }
  }
  .scroll-box2 {
    padding-bottom: 154px;
    .box {
      // height: 76vh;
      // overflow: scroll;
    }
  }
  .top-card {
    width: 750px;
    max-height: 626px;
    background: #ffffff;
    padding: 75px 69px 76px 91px;
    box-sizing: border-box;
    > view {
      display: flex;
      justify-content: space-between;
      align-items: center;
      &:nth-of-type(2) {
        margin-top: 89px;
      }
    }
    .right {
      position: relative;
      .demo-mask {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.1);
        display: flex;
        justify-content: center;
        flex-direction: column;
        align-items: center;
        color: red;
        font-size: 32px;
      }
    }
    .iconfont {
      position: absolute;
      top: 10px;
      right: 10px;
      font-size: 30px;
      background-color: #fff;
      color: #333;
      border-radius: 50%;
      padding: 5px;
    }
    image {
      width: 294px;
      height: 193px;
    }
    .left {
      display: flex;
      flex-direction: column;
      text {
        font-family: OPPOSans;
        font-weight: 500;
        font-size: 24px;
        color: #7a839d;
        margin-top: 10px;
      }
      .b {
        font-family: OPPOSans;
        font-weight: 500;
        font-size: 30px;
        color: #000000;
      }
    }
  }

  .bot-box {
    margin-top: 35px;
    background-color: #fff;
    .select-box {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      > view {
        padding: 5px 15px;
        font-size: 25px;
        background-color: #ccc;
        border-radius: 5px;
        margin-left: 15px;
        &.active {
          background-color: #1352fd;
          color: #fff;
        }
      }
    }
  }

  .ri-up {
    .iconfont {
      color: #b6b6b6;
      font-size: 26px;
    }
  }

  .footer-fixed {
    width: 750px;
    height: 154px;
    background: #ffffff;
    box-shadow: 0px -4px 32px 0px rgba(54, 69, 193, 0.24);
    > .box {
      display: flex;
      align-items: center;
      padding-top: 20px;
      .btn-primary {
        width: 40%;
      }
    }
    .btn-primary,
    .share-btn {
      width: 700px;
      height: 88px;
      background: #1352fd;
      border-radius: 20px;
      transition: all 0.3s;
      margin-left: 5px;
      margin-right: 5px;
      &:active {
        opacity: 0.7;
      }
    }
    .share-btn {
      position: absolute;
      left: 20px;
      z-index: 99;
      top: 20px;
      opacity: 0;
    }
  }

  .select-custom-com-mask {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    overflow: hidden;
    z-index: 999999999;

    .select-custom-com {
      background-color: #fff;
      width: 100%;
      height: 300rpx;
      position: fixed;
      bottom: 0;
      left: 0;
      z-index: 99999;

      .li {
        border-bottom: 1px solid #ccc;
        text-align: center;
        height: 100rpx;
        line-height: 100rpx;

        &:active {
          background-color: #e3e3e3;
        }
      }
    }
  }
}
.bind-check-form {
  background: #ffffff;
}
.tabs {
  background-color: #1352fd;

  .tabs-scr {
    .content {
      display: flex;
      align-items: center;
      height: 120px;
      // overflow-x: auto;
    }
  }

  .tab-item {
    position: relative;
    width: 200px;
    flex-shrink: 0;
    position: relative;
    color: rgba(255, 255, 255, 0.6);
    text-align: center;
    .iconfont {
      position: absolute;
      top: 0;
      right: -6px;
      font-size: 20px;
      background-color: #fff;
      color: #333;
      border-radius: 50%;
      padding: 5px;
    }

    &.active {
      color: #fff;

      &::before {
        position: absolute;
        left: 50%;
        bottom: -17rpx;
        background-color: #fff;
        width: 40rpx;
        height: 10rpx;
        display: block;
        content: "";
        transform: translateX(-50%);
        border-radius: 5rpx;
      }
    }
  }
}

.info-box {
  // padding-bottom: calc(env(safe-area-inset-bottom) + 204rpx);
  .top-box {
    background-color: #fff;

    .title {
      font-weight: 500;
      font-size: 38px;
      color: #000000;
      margin-bottom: 40px;
      padding: 20px;
      padding-bottom: 0;
    }
    text,
    input {
      font-size: 32px;
    }
    .content {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      text {
        margin-left: 8px;
        flex-shrink: 0;
      }
    }
    .yingImg {
      width: 50px;
      height: 50px;
    }
  }
  .look-box {
    position: relative;
    // margin: 30px auto;
    // width: 704px;
    min-height: 356px;
    background: #ffffff;
    // box-shadow: 0px 4px 32px 0px rgba(182, 190, 201, 0.38);
    border-radius: 20px;
    box-sizing: border-box;
    .title {
      font-weight: 500;
      font-size: 38px;
      color: #000000;
      margin-bottom: 40px;
      padding: 20px;
      padding-bottom: 0;
    }
    .item {
      margin-top: 15px;
      height: 60px;
      border-bottom: 1px solid #dfdfdf;
      padding: 15px 20px;
      display: flex;
      justify-content: space-between;

      text {
        font-size: 30px;
      }
      .label {
        color: #000000;
        display: inline-block;
        width: 200px;
        // text-align: right;
      }
      .text {
        color: #000000;
        margin-left: 15px;
      }
    }
  }
  .bot {
    background-color: #fff;
    margin-top: 37px;
  }
  .form-item {
    border-bottom: 1px solid #dfdfdf;
    height: 120rpx;
    justify-content: center;
    background-color: #fff;
    line-height: 119rpx;
    box-sizing: border-box;

    .u-form-item__body__right__message {
      transform: translateY(-15rpx);
    }
  }

  .item-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #fff;
    padding-left: 30px;
    position: relative;
    padding-right: 30px;

    .select-date {
      font-size: 32rpx;
      color: #ccc;
    }

    .ri {
      color: #b6bec5;
      font-size: 32rpx;
    }

    input {
      text-align: right !important;
      font-size: 32rpx !important;
      z-index: 0;
    }

    .phone {
      color: #b6bec5;
      position: absolute;
      right: 40rpx;
      font-size: 30rpx;
    }

    .label {
      margin-right: 40rpx;
      font-size: 32rpx;

      .b {
        color: red;
        margin-right: 10rpx;
      }
    }

    .wrap {
      position: absolute;
      right: 40rpx;
      font-size: 32rpx;
    }
  }

  .top-card {
    padding: 25rpx;

    .title {
      font-size: 40rpx;
      font-weight: 700;
    }

    .label {
      color: #999;
      font-size: 30rpx;

      .label-t {
        margin-top: 10rpx;
      }
    }
  }

  .upload-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 25rpx;
    width: 100%;
    box-sizing: border-box;

    .card {
      width: 327rpx;
      height: 200rpx;
      border-radius: 15rpx;
      overflow: hidden;
      position: relative;

      image {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }

      .close {
        position: absolute;
        top: 15rpx;
        right: 15rpx;
        color: #fff;
        width: 50rpx;
        height: 50rpx;
        background-color: rgba(0, 0, 0, 0.6);
        border-radius: 50%;
        text-align: center;
        line-height: 50rpx;
        font-size: 32rpx;
      }
    }
  }

  .yingye-box {
    padding: 25rpx;

    .top {
      .title {
        font-size: 40rpx;
        font-weight: 700;
      }
    }

    .upload-box-2 {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .left {
        width: 150rpx;
        height: 200rpx;
        flex-shrink: 0;
        background-color: #f5f8ff;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        color: #999;
        margin: 15rpx 0;
        margin-right: 20rpx;
        position: relative;

        .close {
          position: absolute;
          top: 15rpx;
          right: 15rpx;
          color: #fff;
          width: 50rpx;
          height: 50rpx;
          background-color: rgba(0, 0, 0, 0.6);
          border-radius: 50%;
          text-align: center;
          line-height: 50rpx;

          .iconfont {
            font-size: 32rpx !important;
          }
        }

        image {
          width: 100%;
          height: 100%;
        }

        text {
          font-size: 25rpx;
        }

        .iconfont {
          font-size: 60rpx;
        }
      }

      .right {
        font-size: 26rpx;

        .top {
          color: #999;
        }

        .bottom {
          color: red;
          height: auto;
        }
      }
    }
  }
}
</style>
