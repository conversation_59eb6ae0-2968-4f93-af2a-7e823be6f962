<template>
  <view class="list">
    <view
      class="list-item p20"
      v-for="item in items.filter(o => !o.log.includes('手动清零或'))"
      :key="item.id"
      @tap="onDetail(item.id)"
    >
      <template v-if="item.action === 3">
        <view class="flex flex-space-between">
          <view>{{ item.log }}</view>
          <view>{{item.amount}}</view>
        </view>
        <view class="color-low">充值日期：{{item.created_at}}</view>
      </template>
      <template v-else>
        <view>{{ item.log.split(';')[0] ? item.log.split(';')[0] : item.log }}</view>
        <view class="color-low">操作人：{{item.user_name}} </view>
        <view class="color-low">操作日期：{{item.created_at}}</view>
        <view class="color-low">清零前度数：{{ item.before }}度</view>
        <view class="color-low" v-if="item.log.split(';')[1]">{{ item.log.split(';')[1] }}</view>
      </template>
    </view>
    
  </view>
</template>

<script setup>
import { ref } from "vue";
import request from "@/utils/request";
import Taro, {
  useDidShow,
  useDidHide,
  useReady,
  useLoad,
  usePullDownRefresh,
  useReachBottom,
} from "@tarojs/taro";
definePageConfig({
  navigationBarTitleText: "设备操作日志",
  navigationBarBackgroundColor: "#1352FD",
  navigationBarTextStyle: "white",
});

const id = ref(0)

useLoad((options) => {
  console.log(options, 'log options')
  params.value.action = options.action || 1
  id.value = options.id
  fetch();
})

useDidShow(() => {
  
});

useReachBottom(() => {
  if (total.value > items.value.length) {
    params.value.page += 1;
    getList();
  }
});

const items = ref([]);
const total = ref(0);
const params = ref({
  page: 1,
  action: 1
});

const fetch = () => {
  items.value = [];
  params.value.page = 1;
  getList();
};
const getList = () => {
  request
    .get({
      url: "device/" + id.value + "/log",
      data: {
        ...params.value,
      },
    })
    .then((res) => {
      items.value = [...items.value, ...res.data.items];
      total.value = res.data.total;
    });
};
</script>

<style lang="scss">
page {
  background: #f1f3f7;
}
.list {
  padding-bottom: 170rpx;
}

.list-item {
  width: 700rpx;
  background: #ffffff;
  border-radius: 14px;
  margin: 20rpx auto;
  padding-bottom: 38px;
  font-size: 28px;
}
</style>
