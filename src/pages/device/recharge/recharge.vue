<template>
  <view class="container">
    <view class="tip" v-if="device?.net_type == 2"><text>温馨提示: 缴费时, 需要与设备保持3米内</text></view>
    <view class="du">
      <view class="du-lab"><text class="text-v-center">{{ device?.agent?.type == 2 && globalStore.who != 'business' || device?.type == 5 || device?.type == 6 || conf?.elec_show_type == 2 ? '剩余金额(元)' : device?.type == 2 ? '剩余水量(m³)' : '剩余电量(度)' }}</text><MyIcon v-if="device?.agent?.type == 2 && globalStore.who != 'business' || device?.type == 2 || device?.type == 5 || device?.type == 6 ? false　: true" icon="icon-dl" width="19rpx" height="25rpx"></MyIcon></view>
      <view class="du-num">{{ (device?.agent?.type == 2 && globalStore.who != 'business') || conf?.elec_show_type == 2 ? (device.du * (Number(device.price) + Number(device.agent?.service))).toFixed(2)  : device.du }}</view>
    </view>
    <view class="form">
      <view class="flex flex-space-between">
        <view class="form-title">充值金额</view>
        <view class="log-link" @tap="onRechargeLog" v-if="globalStore.who == 'business'">充值记录</view>
      </view>
      <view class="form-amount flex flex-row">
        <view class="fa-item" v-for="item in rechargePriceLis" :key="item.price" :class="formState.amount == item.price ? 'fa-checked' : ''" @tap="onChangeAmount(item.price)"><text class="rmb">￥</text>{{ item.price }}</view>
        <!-- <view class="fa-item" :class="formState.amount == 30 ? 'fa-checked' : ''" @tap="onChangeAmount(30)"><text class="rmb">￥</text>30</view>
        <view class="fa-item" :class="formState.amount == 50 ? 'fa-checked' : ''" @tap="onChangeAmount(50)"><text class="rmb">￥</text>50</view>
        <view class="fa-item" :class="formState.amount == 100 ? 'fa-checked' : ''" @tap="onChangeAmount(100)"><text class="rmb">￥</text>100</view>
        <view class="fa-item" :class="formState.amount == 200 ? 'fa-checked' : ''" @tap="onChangeAmount(200)"><text class="rmb">￥</text>200</view>
        <view class="fa-item" :class="formState.amount == 300 ? 'fa-checked' : ''" @tap="onChangeAmount(300)"><text class="rmb">￥</text>300</view> -->
      </view>
      <view class="mt10">
        <MyInput2 suffix="元" v-if="!showRechargeTip && !showFail">
            <template #content>
              <input class="my-input2-m" v-if="mode == 1 ? false : true" v-model="formState.amount" type="digit" :disabled="mode == 1 ? true : false" :placeholder="globalStore.who == 'tenant' ? `最低充值金额:${device?.type == 2 ? conf.water_recharge_min : conf.elec_recharge_min}元` : '自定义金额'" />
              <view class="my-input2-m" v-else>{{ device.price }}</view>
            </template>
        </MyInput2>
      </view>
      <view class="d-bar p20">
        <view class="d-cell" v-if="mode == 1 ? false : true"><view class="d-lab">充值金额</view><view class="d-val">{{formState.amount}}元</view></view>
        <view class="d-cell" v-if="(device?.agent?.type == 2 && globalStore.who != 'business') || (mode == 2 && device?.agent?.type == 2) || (device?.type == 5 || device?.type == 6 || conf?.elec_show_type == 2) ? false : true"><view class="d-lab">{{ device?.type == 2 ? '到账水量' : '到账电量' }}</view><view class="d-val">{{du}}{{ device?.type == 2 ? 'm³' : '度' }} <text class="d-val-txt" v-if="mode == 1 || !conf.show_price ? false : false">({{ device?.type == 2 ? '水量单价：' : '电量单价：' }} {{device.price}} {{ device?.type == 2 ? '元/m³' : '元/度' }})</text></view></view>
        <view class="d-cell" v-if="globalStore.who === 'business' && device?.agent?.type == 2"><view class="d-lab">实际付款</view><view class="d-val">{{ mode == 2 ? payAmt + ServiceCharge : 0 }} 元 <text class="d-val-txt">{{ mode == 1 ? '(临时用电充值，无需付款)' : '房东代客充值' }}</text></view></view>
        <view class="d-cell" v-if="globalStore.who === 'business'  && device?.agent?.type != 2"><view class="d-lab">实际付款</view><view class="d-val">0 元 <text class="d-val-txt">{{ mode == 1 ? '(临时用电充值，无需付款)' : mode == 2 ? '房东代客充值' : '(管理员充值，无需付款)' }}</text></view></view>
        <view class="d-cell" v-if="globalStore.who === 'tenant'"><view class="d-lab">实际付款</view><view class="d-val">{{payAmt + fee}} 元 <text class="d-val-txt" v-if="globalStore.who === 'tenant' && device?.agent?.type != 2">(含{{fee}}元手续费)</text></view></view>
        <!-- <view class="d-cell" v-if="globalStore.who === 'business'"><view class="d-lab">当月剩余充值度数</view><view class="d-val">{{ ((Number(device?.business_amount) -  Number(bussinessDeviceAmount)).toFixed(2)) || 0 }} 度 </view></view> -->
      </view>

      <view class="mt33">
        <button class="btn-add" @tap="onSubmit" :disabled="isButtonDisabled">立即支付</button>
      </view>
      <view class="mt33" v-if="globalStore.who === 'tenant'">
        <image @tap="onChangePrivacy(1)" v-if="!privacy" :src="iconRadio" class="icon-radio"></image>
            <image @tap="onChangePrivacy(0)" v-if="privacy" :src="iconRadioChecked" class="icon-radio"></image>
          <text @tap="onChangePrivacy(1)">我已阅读并同意</text> <text class="link" @tap="onJump('/pages/article/article?key=recharge')">《充值协议》</text>
      </view>
    </view>
    <MyPopup id="showRechargeTip" :show="showRechargeTip" :isScrlloAnimation="true"  :titleStyle="{ color: 'red', fontSize: '36rpx', fontWeight: 'bold' }" title="本小程序仅限水电费充值，引导其他付款均为诈骗行为，请仔细确认后付款！" @close="onChangeShowTip">
      <template #content>
        <view class="recharge-confirm-box">
          <view class="recharge-tip flex">
            <view style="width: 100rpx; padding-top: 120rpx">
              <!-- <MyIcon icon="icon-warning" width="55rpx" height="55rpx"></MyIcon> -->
              <image
                src="https://yimits.oss-cn-beijing.aliyuncs.com/images/jinggao.png"
                mode="scaleToFill"
                style="width: 55rpx;height: 55rpx;"
              />
            </view>
            <view style="flex: 1" class="text-box">
              <view> 1、请检查，是否为您需要充值的设备</view>
              <view>  2、支付完成后，<text>请点击“完成”按钮</text></view>
              <view> 3、您充值金额将结算至房东绑定的账户， 充值成功后无法撤回</view>
              <view> 4、若发现充错设备或需退款，请联系房 东处理</view>
            </view>
          </view>
          <view class="recharge-detail">
            <view class="mt20 flex flex-space-between">
              <view class="rd-left">房间名称</view>
              <view class="rd-right">{{device.house && (device.house.estate_name + device.house.name)}}</view>
            </view>
            <view class="mt20 flex flex-space-between">
              <view class="rd-left">充值金额</view>
              <view class="rd-right">{{formState.amount}}元</view>
            </view>
            <view class="mt20 flex flex-space-between" v-if="(device?.agent?.type == 2 && globalStore.who != 'business') || (mode == 2 && device?.agent?.type == 2) ? false : true">
              <view class="rd-left">{{ device?.type == 2 ? '到账水量' : '到账电量' }}</view>
              <view class="rd-right">{{du}}度</view>
            </view>
            <view class="mt20 flex flex-space-between">
              <view class="rd-left">支付金额</view>
              <view class="rd-right ">
                <text class="ico" @tap="isMingXi = !isMingXi">
                  <text>查看明细</text>
                  <text class="iconfont " :class="{'icon-shangjiantou':isMingXi, 'icon-xiajiantou1':!isMingXi}"></text>
                </text>
                <text class="red" v-if="device?.agent?.type == 2 && globalStore.who === 'business'">{{payAmt + ServiceCharge}}元</text>
                <text class="red" v-else>{{payAmt + fee}}元</text>
              </view>
            </view>
            <view class="mt20 rd-fee" v-show="isMingXi">
              平台服务费：{{ device?.agent?.type == 2 && globalStore.who === 'business' ? ServiceCharge : fee }}元
            </view>
            <!-- <view class="mt20 rd-fee" v-show="isMingXi" v-if="device?.agent?.type == 2 && globalStore.who === 'business'">
              充值手续费：{{ ServiceCharge }}元
            </view> -->
          </view>
        </view>
        <view class="rd-btn-box">
          <button class="btn-add" @tap="handleTenantRecharge" :disabled="isButtonDisabled">确认支付</button>
        </view>
      </template>
    </MyPopup>

    <MyPopup id="showBusinessRechargeTip" :show="showBusinessRechargeTip" :isScrlloAnimation="true"  :titleStyle="{ color: 'red', fontSize: '36rpx', fontWeight: 'bold' }" title="注意：房东充值免费充值，无需付费！！！" @close="showBusinessRechargeTip = false">
      <template #content>
        <view class="recharge-confirm-box-business">
          <text>实际支付, </text>
          <text class="text">0</text>
          <text>元</text>
        </view>
        <view class="rd-btn-box">
          <button class="btn-add" style="margin-bottom: 20rpx;" @tap="businessRechargeTipYesHandel">确认充值</button>
        </view>
      </template>
    </MyPopup>

    <MyPopup :show="showRechareXY" :isScrlloAnimation="false"  :titleStyle="{ color: '#000', fontSize: '36rpx' }" title="同意协议" @close="showRechareXY = false">
      <template #content>
        <view class="recharge-confirm-xy">
          <text>同意 </text>
          <text class="link" @tap="onJump('/pages/article/article?key=recharge')">《充值协议》</text>
        </view>
        <view class="rd-btn-box" style="border:none;">
          <button class="btn-add" style="margin-bottom: 20rpx;border-radius: 50rpx;" @tap="onSubmitBefore">同意并支付</button>
        </view>
      </template>
    </MyPopup>

    <YModal
      title="no"
      confirmText="no"
      :show="showNetModal"
      @close="showNetModal = false"
      @confirm="confirmNetHandel"
      :maskClose="false"
      :bodyStyle="{width:'680rpx',paddingTop:'100rpx',borderRadius:'30rpx',height: '625rpx'}"
    >
      <template #content>
        <view class=" content-modal2" style="text-align:center">
          <image :src="netErroImg" style="width: 218rpx;height:155rpx"></image>
          <view :style="{
            color: '#000',
            fontWeight: 700,
            marginTop: '79rpx',
            marginBottom: '38rpx',
            fontSize:'40rpx'}">远程网络<text style="color:#db2b2b ;">异常</text></view>
          <view>
            <text style="font-size: 35rpx;font-weight: 500;">与电表保持3米以内距离打开蓝牙进行操作</text>
          </view>
          <view class="btn-box" @tap="showNetModal = false">
            <view @tap="clickYesHandel" style="color: #000;">我知道了</view>
          </view>
        </view>
      </template>
    </YModal>
  <GlobDialog/>
  </view>
  <YToast :show="showToast" width="310" height="100" padding="35rpx 35rpx" :text="yToastText" @close="showToast = false"/>
  <contantModal :show="isContact2" :phone="globalStore.userInfo.service_te" @close="isContact2 = false" :isInlineServe="false" title="提示" content="本月房东充值额度已用完，如有疑问请联系客服" :isShowClose="false" :isShowCancel="false" :isShowConfirm="true" confirmText="我知道了" @confirm="isContact2 = false" />

</template>
<script setup>
  import { ref, computed, unref } from 'vue'
  import './recharge.scss'
  import Taro, {
    useDidShow,
    useDidHide,
    useReady,
    useLoad,
    usePullDownRefresh,
    useReachBottom,
    useUnload
  } from '@tarojs/taro'
  const {throttle} = require('@/utils/loadshMy')
  import request from '@/utils/request'
  import deviceConnect from '@/utils/deviceConnect'

  import MyIcon from '@/components/MyIcon'
  import MyInput2 from '@/components/MyInput2'
  import MyPopup from '@/components/MyPopup'
  import contantModal from '../../my/components/contantModal/index.vue'


  const iconRadio = 'https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-radio.png'
  const iconRadioChecked = 'https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-radio-checked.png'

  import YModal from '@/components/YModal/index.vue'
  import YToast from '@/components/YToast/index.vue'

  import GlobDialog from '@/components/globDialog/index.vue'

  const netErroImg = 'https://yimits.oss-cn-beijing.aliyuncs.com/images/net-erro.png'

  const showToast = ref(false)

  const yToastText = ref('')

  const isContact2 = ref(false)

  function getCurrentPageInstance() {
  const pages = Taro.getCurrentPages(); // 获取页面栈
  const currentPage = pages[pages.length - 1]; // 获取当前页面的实例
  return currentPage; // 返回当前页面的实例
}

const isCurentPage = ref(true)

const timerSet = ref(null)
const timerStateSet = ref(null)
const timerRechargeSet = ref(null)
const timerRechargeBleSet = ref(null)

const showRechareXY = ref(false)

const rechargePriceLis = ref([
  {price: 20},
  {price: 30},
  {price: 50},
  {price: 100},
  {price: 200},
  {price: 300},
])

let countdown = null;
let coutTimes = 20

useDidHide(() => {
  isCurentPage.value = false
  clearInterval(timerSet.value)
  clearInterval(timerStateSet.value)
  clearInterval(timerRechargeSet.value)
  clearInterval(timerRechargeBleSet.value)
  clearInterval(countdown)
  // globalStore.setDeviceRefTime(30)
  // globalStore.setGlobDialog({show:false,type:''})
})

useUnload(() => {
  isCurentPage.value = false
  clearInterval(timerSet.value)
  clearInterval(timerStateSet.value)
  clearInterval(timerRechargeSet.value)
  clearInterval(timerRechargeBleSet.value)
  clearInterval(countdown)
  globalStore.setDeviceRefTime(30)
  globalStore.setGlobDialog({show:false,type:''})
})


  import { useGlobalStore } from '@/stores'
  import { resolve } from 'path';
  import { reject } from 'lodash';
  import { checkIsConnectRssi, checkNetInfo } from '@/utils'
  const globalStore = useGlobalStore()

  const showNetModal = ref(false)

  const isMingXi = ref(true)

  const mode = ref(null)//1:临时用电（需要加次数每日限制5次） 2：房东代充

  const id = ref(0)
  const sn = ref('')
  const who = ref('business')
  const isButtonDisabled = ref(false);
  const bussinessDeviceAmount = ref(0)//当月设备房东充值的金额
  const show_order = ref('')//是否是可退费订单
  useLoad((option) => {
    // 保持屏幕常亮
    Taro.setKeepScreenOn({
        keepScreenOn: true
    })
    updateOpenId()
    id.value = option.id
    sn.value = option.sn
    mode.value = option?.mode || null
    console.log(option,"option");
    show_order.value = option?.show_order 
    if (option.who === 'tenant') {
      globalStore.who = 'tenant'
    }
    getDetail(sn.value)
    // 获取当月设备房东充值的金额
    request.get({
      url: 'device/getBusinessAmount',
      data: {
        device_id: id.value,
      },
      showToast:false
    }).then(res => {
      bussinessDeviceAmount.value = res.data?.current || 0
      console.log(res,"getBusinessAmount");
    })
  })

  useDidShow(() => {
    isCurentPage.value = true
    // globalStore.setGlobDialog({show:false,type:''})
  })

  // 充值失败，存储失败的度数
  const rechargeFailSetDuInfo = (info) => {
    Taro.setStorageSync('rechargeFailDuInfo', JSON.stringify(info))
  }

  const privacy = ref(false)
  const onChangePrivacy = (val) => {
    privacy.value = val
  }

  const device = ref({})

  const formState = ref({
    amount: '',
    du: 0,
    pay_amt: 20,
    fee: 0,
    rate: 0
  })

  const payAmt = computed(() => {
    return (formState.value.amount * 1) + (formState.value.fee * 1)
  })
  const fee = computed(() => {
    if (device.value?.agent?.type == 2) {
      return 0
    }
    return ((formState.value.amount * formState.value.rate).toFixed(2)) * 1
  })
  const du = computed(() => {
    // return ((formState.value.amount / device.value.price).toFixed(2)) * 1
    return (device.value?.agent?.type == 2 ? (formState.value.amount / (Number(device.value.price) + Number(device.value?.agent?.service))).toFixed(2) : (formState.value.amount / device.value.price).toFixed(2)) * 1
  })

  // 定制用户的房东充值手续费
  const ServiceCharge = computed(() => {
    // return ((formState.value.amount * (device.value.rate ? device.value.rate : 0.006)).toFixed(2)) * 1
    return 0
  })

  const conf = ref({})

  const confirmNetHandel = () => {
    showNetModal.value = false
    // device.value.net_type = 2
    // deviceConnect.init(device.value.mac, device.value.net_type)
    if (device.value.net_type !== 1) {
      globalStore.listenClient = device.value.mac + '-2'
      setTimeout(() => {
        deviceConnect.getConnection()
      },300)
    }
  }

  // 拿到最新蓝牙设备的du
  const listenDeviceReadBle = () => {
  let s = 0
  s = 0
  return new Promise((resolve, reject) => {
    timerSet.value = setInterval(() => {
      s ++

      if (globalStore.device.message.length > 0) {
        let msg = globalStore.device.message.shift()
        console.log(msg, device.value.mac)
        if (msg.mac === device.value.mac) {
          if (msg.event === '0C') {
            resolve(msg.du)
            s = 0
            Taro.hideLoading()
            clearInterval(timerSet.value)
          }

          if (s >= 3) {
            s = 0
            Taro.hideLoading()
            clearInterval(timerSet.value)
            Taro.showToast({
              title: '获取电量超时',
              icon: 'warning',
              duration: 2000
            })
            reject()
          }
        }
      }

    }, 1000)
  })
  }


  // 获取设备信息
  const getDevInfo = (sn) => {
    return new Promise((resolve, reject) => {
      let opt = {
        url: 'device/' + id.value
      }
      if (sn) {
        opt = {
          url: 'device/search',
          data: {
            sn
          }
        }
      }
      request.get(opt).then(res => {
        // device.value = res.data
        resolve(res.data)
      })
    })
  }


  const getDetail = (sn) => {
    let opt = {
      url: 'device/' + id.value
    }
    if (sn) {
      opt = {
        url: 'device/search',
        data: {
          sn
        }
      }
    }
    request.get(opt).then(res => {
      id.value = res.data.id
      device.value = res.data
      // 如果是峰谷表
      if (res.data?.type == 5 || res.data?.type == 6) { 
        // 默认一元一度
        device.value.price = 1
        Taro.setNavigationBarTitle({
          title:'充值余额'
        })
      }
      if (res.data?.type == 2) {
        // 修改标题
        Taro.setNavigationBarTitle({
          title:'水量充值'
        }
        )
      }
      console.log(res.data,"res.datares.data");
      // TODO 第一次读取的数据存本地 防止被更新
      Taro.setStorageSync('rechargeBeforeDu', res.data?.du)
      rechargeFailSetDuInfo({
        du: res.data?.du,
        net_type: device.value?.net_type,
        sn: device.value?.sn,
        id: device.value?.id,
        rechargeDu: du.value
      })
      globalStore.getUserInfo().then(() => {
        if(globalStore.who === 'business') {
          if (mode.value == 1) {
            formState.value.amount = Number(device.value?.price) + Number(device.value?.agent?.service)
          }
        }
      })
      if(device.value.signal == 0){
        // isButtonDisabled.value = true
        // Taro.showToast({
        //   title: '设备暂无信号',
        //   icon: 'none'
        // })
      //   if (globalStore.who === 'tenant') {
      //     setTimeout(() => {
            // Taro.showModal({
            //   title: '提示',
            //   content: '设备不在线,请联系房东!',
            //   showCancel:false,
            //   success: function (res) {
            //     if (res.confirm) {
            //       console.log('用户点击确定')
            //       Taro.navigateBack()
            //     } else if (res.cancel) {
            //       console.log('用户点击取消')
            //     }
            //   }
            // })
      //     },0)
      // }
      }
      if (res.data.net_type == 1 && res.data.signal <= 0) {
          // 为4G设备且没有信号 走蓝牙操作表的逻辑
          // TODO
          if (globalStore.who === 'tenant') {
            //  showNetModal.value = true
             device.value.net_type = 2
             deviceConnect.init(device.value.mac, device.value.net_type)
          } else {
            device.value.net_type = 2
            deviceConnect.init(device.value.mac, device.value.net_type)
            if (device.value.net_type !== 1) {
              globalStore.listenClient = device.value.mac + '-2'
              deviceConnect.getConnection()
            }
          }
      } else {
        // 非4G设备扫描蓝牙
        deviceConnect.init(device.value.mac, device.value.net_type)
      }
      if (device.value.net_type !== 1) {
        globalStore.listenClient = device.value.mac + '-2'
        //listenDeviceMessage()
        deviceConnect.getConnection()
      }
      if (globalStore.who === 'tenant') {
        request.get({
          url: 'business/conf',
          data: {
            businessId: res.data.business_id
          },
          showToast:false
        }).then(res => {
          conf.value = res.data
          if (device.value?.type == 2) {
            rechargePriceLis.value = rechargePriceLis.value.filter(item => Number(item.price) >= Number(res.data.water_recharge_min))
          } else {
            rechargePriceLis.value = rechargePriceLis.value.filter(item => Number(item.price) >= Number(res.data.elec_recharge_min))
          }
            // formState.value.amount = rechargePriceLis.value[0].price
          console.log(res.data, 'conf')
          if (res.data.fee_payer === 2) {
            formState.value.rate = device.value.rate ? device.value.rate : 0.006;
          }
        })
      } else {
        formState.value.amount = rechargePriceLis.value[0].price
      }
    })
  }

  const onChangeAmount = (val) => {
    if (mode.value == 1) {
      Taro.showToast({
        title: '临时用电充值每次只能充值1度!',
        icon: 'none'
      })
      return
    }
    formState.value.amount = val
  }

  // const onChangeInputAmount = (e) => {
  //   formState.value.amount = e.detail.value
  // }

  const isSubmit = ref(false)
  const onSubmit = async () => {
    // 需要检测网络信号合蓝牙连接强度信号不好不让充值
    await checkNetInfo()
    if (device.value?.net_type == 2) {
      await checkIsConnectRssi(device.value?.type || 2)
    }
    if ((process.env.TARO_ENV === 'weapp' && globalStore.who === 'tenant' && device.value?.type != 2 && (payAmt.value >= conf.value?.elec_recharge_min)) || (process.env.TARO_ENV === 'weapp' && globalStore.who === 'tenant' && device.value?.type == 2 && (payAmt.value >= conf.value?.water_recharge_min))) {
      // 获取订阅权限
      wx.login({
        success(res) {
          if (res.code) {
            request.post({
              url: 'user/updateOpenId',
              data: {
                code: res.code
              },
              showLoading:false,
              Headers: {
                'Token': globalStore.token
              }
            }).then(_ => {
            })
          }
        },
        fail() {
          Taro.showToast({
            title: '获取openid出错',
            icon: 'error'
          })
        }
      })
      wx.getSetting({
        withSubscriptions: true,
        success (res) {
          console.log(res.subscriptionsSetting,"subscriptionsSetting")
          if (res.subscriptionsSetting.mainSwitch) { 
            Taro.requestSubscribeMessage({
              tmplIds: ['veACA0Y1QHf-YBqZzJVEYIA5E4cnjd2o7wj1V8JxebQ'],
              success (res) {
                console.log(res, "requestSubscribeMessageSucess");
                if (res['veACA0Y1QHf-YBqZzJVEYIA5E4cnjd2o7wj1V8JxebQ'] == 'reject') {
                  Taro.showModal({
                  title: '提示',
                  content: '请开启订阅消息，以便及时收到用电信息',
                  showCancel: false,
                    success (res) {
                      if (res.confirm) {
                        Taro.openSetting()
                      }
                    }
                  })
                return
                }
                // 同步授权状态
                request.get({
                  url: 'user/updateSub',
                  data: {
                    sub_1: globalStore.userInfo?.sub_1,
                    sub_2: 1
                  },
                  showLoading:false
                }).then(_ => {
                })
              },
              fail (erro) {
                console.log(erro,"requestSubscribeMessageErro");
              }
            })
          } else {
            Taro.showModal({
              title: '提示',
              content: '请开启订阅消息，以便及时收到用电信息',
              showCancel: false,
              success (res) {
                if (res.confirm) {
                  Taro.openSetting()
                }
              }
            })
          }
          // res.subscriptionsSetting = {
          //   mainSwitch: true, // 订阅消息总开关
          //   itemSettings: {   // 每一项开关
          //     SYS_MSG_TYPE_INTERACTIVE: 'accept', // 小游戏系统订阅消息
          //     SYS_MSG_TYPE_RANK: 'accept'
          //     zun-LzcQyW-edafCVvzPkK4de2Rllr1fFpw2A_x0oXE: 'reject', // 普通一次性订阅消息
          //     ke_OZC_66gZxALLcsuI7ilCJSP2OJ2vWo2ooUPpkWrw: 'ban',
          //   }
          // }
        }
      })
    }
    console.log(device.value?.agent?.type,"device.value.agent?.type");
    console.log("is_limit_recharge_days:"+conf.value?.is_limit_recharge_days);
    console.log("payAmt:" + payAmt.value);

    // 更新用户信息
    try {
      Taro.showLoading()
      await globalStore.getUserInfo()
      Taro.hideLoading()
      if(globalStore.who === 'business') {
        if (mode.value == 1) {
          formState.value.amount = Number(device.value?.price) + Number(device.value?.agent?.service)
        }
      }
    }catch(_) {}

     // 判段是否是定制用户并且是房东
    if (globalStore.who === 'business' && (mode.value == 1 || mode.value == 2)) {
      // 房东每次只能充一du
      // console.log('定制用户房东充值');
      if (unref(mode) == 1) {
        if (du.value > 1) {
          Taro.showToast({
            title: '临时用电只能充值一度！',
            icon: 'none'
          })
          return
        }
        if (device.value?.today_recharge_limit >= 5) {
          Taro.showToast({
            title: '临时用电当日限制5次！',
            icon: 'none'
          })
          return
        }
        // TAG 临时用电 免费每次一度 当日限制5次
          // 继续免费充值
          if (device.value.net_type !== 1) {
            deviceConnect.getConnection().then(_=> {
              handleRecharge()
            })
          } else {
            handleRecharge()
          }
      }else if (unref(mode) == 2) {
        // TAG 房东代充 需要收费
        showRechargeTip.value = true
        if (device.value.net_type === 2) {
          playAudio()
        }
      }
      return
    }

    if (globalStore.who === 'business') {
      const duT = Number(device.value.business_amount) - Number(bussinessDeviceAmount.value)
      console.log(duT,"duT");
      // 表单判断-水表不判断
      if (du.value > duT && duT > 0 && device.value.type != 2) {
         Taro.showToast({
           title: '充值度数不能超过剩余额度',
           icon: 'none'
         })
        if (device.value?.agent?.type == 2) {
          formState.value.amount  = ((Number(device.value.price) + Number(device.value?.agent?.service)) * duT).toFixed(2)
        } else {
          formState.value.amount  = (Number(device.value.price) * duT).toFixed(2)
        }
         return
      }

      // 限额判断
      if (duT <= 0) {
        isContact2.value = true
        return 
      }
    }

   

    if (isSubmit.value) {
      Taro.showToast({
        title: '操作过于频繁，请稍后再试',
        icon: 'none'
      })
      return
    }

    // 充值限流
    isSubmit.value = false

    setTimeout(() => {
      isSubmit.value = false
    }, 5000)

    if (device.value?.type != 2 && payAmt.value < conf.value?.elec_recharge_min) {
      return Taro.showModal({
        title: '提示',
        content: `电表单次最低充值金额${conf.value?.elec_recharge_min}元！`,
        showCancel:false,
        success: function (res) {
          if (res.confirm) {
            console.log('用户点击确定')
          } else if (res.cancel) {
            console.log('用户点击取消')
          }
        }
      })
    }
    if (device.value?.type == 2 && payAmt.value < conf.value?.water_recharge_min) {
      return Taro.showModal({
        title: '提示',
        content: `水表单次最低充值金额${conf.value?.water_recharge_min}元！`,
        showCancel:false,
        success: function (res) {
          if (res.confirm) {
            console.log('用户点击确定')
          } else if (res.cancel) {
            console.log('用户点击取消')
          }
        }
      })
    }
    if (globalStore.who === 'tenant') {
      if (!privacy.value) {
        showRechareXY.value = true
        // Taro.showToast({
        //   title: '请阅读并同意充值协议',
        //   icon: 'none'
        // })
        return
      }
    }

    if (device.value.net_type !== 1) {
      deviceConnect.getConnection().then(_ => {
        // 如果是房东先弹起弹框提示用户是0元充值，点击确定再去真实充值操作
        if (globalStore.who === 'business') {
          showBusinessRechargeTip.value = true
          return
        }
        handleRecharge()
      })
    } else {
      if (globalStore.who === 'business') {
          showBusinessRechargeTip.value = true
          return
      }
      // 如果是房东先弹起弹框提示用户是0元充值，点击确定再去真实充值操作
      handleRecharge()
    }
  }

  const onSubmitBefore = () => {
    privacy.value = true
    showRechareXY.value = false
    onSubmit()
  }

  const handleRecharge = () => {
  console.log(device.value?.business?.user.mobile,"device.value?.business?.user.mobile");
  console.log(globalStore.userInfo.mobile,"globalStore.user.mobile");

    if (globalStore.who === 'business') {
      if (formState.value.amount > 10000) {
        return Taro.showToast({
          title: '单次充值电量不可超过1万',
          icon:'none'
        })
      }
      // TODO 查看表房东和当前用户手机号是否一致，不一致则提示
      // if (device.value?.business?.user.mobile && globalStore.userInfo?.mobile && device.value?.business?.user.mobile != globalStore.userInfo?.mobile) {
      //   return Taro.showToast({
      //     title: '当前设备不属于您，无法进行充值!',
      //     icon:'none'
      //   })
      // }
      globalStore.setGlobDialog({show:true,type:'TimeReFLoading'})
      countdown = setInterval(function() {
        globalStore.setDeviceRefTime(globalStore.deviceRefTime-1)
        if (globalStore.deviceRefTime <= 0) {
          clearInterval(countdown);
         globalStore.setGlobDialog({show:false,type:'TimeReFLoading'})
          globalStore.setDeviceRefTime(30)
          globalStore.setGlobDialog({show:true,type:'RefFailed'})
        } 
      }, 1000);
      deviceConnect.recharge( du.value, formState.value.amount, 0, (mode.value == 1) ? 0 : 1)
      if (device.value.net_type === 1) {
        listenDeviceRecharge()
      } else {
        listenDeviceRechargeBle()
      }
    } else {
      // todo 弹框
      showRechargeTip.value = true
      if (device.value.net_type === 2) {
        playAudio()
      }
    }
  }

  const playAudio = () => {
    const innerAudioContext = Taro.createInnerAudioContext()
    innerAudioContext.autoplay = true
    innerAudioContext.src = 'https://yimidb.oss-cn-hangzhou.aliyuncs.com/mp/audio/cztx.wav'
    innerAudioContext.onPlay(() => {
      console.log('开始播放')
    })
  }

  const showRechargeTip = ref(false)
  const showBusinessRechargeTip = ref(false)
  const onChangeShowTip = () => {
    showRechargeTip.value = !showRechargeTip.value
  }

  const businessRechargeTipYesHandel = () => {
    showBusinessRechargeTip.value = false
    handleRecharge()
  }

 const handleTenantRecharge = () => {
 console.log('***tenant recharge***',Date.now() - globalStore.rechargeTime,(Date.now() - globalStore.rechargeTime) < 1000 * globalStore.rechargeTimeSecens);
 
    if(globalStore.rechargeTime && (Date.now() - globalStore.rechargeTime) < 1000 * globalStore.rechargeTimeSecens) {
      console.log('***请勿频繁充值***');
        Taro.showToast({
          title: '充值频繁,请稍后再试！',
          icon:'none'
        })
      return 
    }
    request.post({
      url: show_order.value == 1 ? 'payment/rechargeTemp' : 'payment/recharge',
      data: {
        deviceId: id.value,
        amount: formState.value.amount,
        fee: (device.value?.agent?.type == 2 && globalStore.who === 'business') ? ServiceCharge.value : fee.value,
        du: du.value,
        pay_channel: process.env.TARO_ENV === 'alipay' ? 'alipay_lite' : 'wx_lite',
        service_fee: (device.value?.agent?.type == 2 && globalStore.who === 'business') ? device.value?.agent?.service : 0,
        identity: show_order.value == 1 ? undefined : (device.value?.agent?.type == 2 && globalStore.who === 'business') ? 2 : 1,
      },
      showToast:false
    }).then(res => {
      console.log(res, 'payment params')
      if (device.value?.agent?.type == 2 && globalStore.who === 'business') {
        showRechargeTip.value = false
      }
      let payConfig = JSON.parse(res.data.expend.pay_info)
      if (process.env.TARO_ENV === 'weapp') {
        wx.requestPayment({
          timeStamp: payConfig.timeStamp,
          nonceStr: payConfig.nonceStr,
          package: payConfig.package,
          signType: payConfig.signType,
          paySign: payConfig.paySign,
          success: () => {
            console.log('success')
            // todo 查询充值订单状态
            Taro.showLoading({
              title: '支付确认中...'
            })
            queryOrderStatus(res.data.order_no).then(_ => {
              // 请求充值
              // Taro.showLoading({
              //   title: '正在充值中...',
              //   mask: true
              // })
              globalStore.setGlobDialog({show:true,type:'TimeReFLoading'})
              countdown = setInterval(function() {
                // coutTimes--
              globalStore.setDeviceRefTime(globalStore.deviceRefTime-1)
                if (globalStore.deviceRefTime <= 0) {
                  clearInterval(countdown);
                  // coutTimes=20
                  globalStore.setGlobDialog({show:false,type:'TimeReFLoading'})
                  globalStore.setDeviceRefTime(30)
                  globalStore.setGlobDialog({show:true,type:'RefFailed'})
                } else {
                  // Taro.showLoading({
                  //   title: `设备充值剩余${(coutTimes).toFixed(0)}秒`,
                  //   mask: true
                  // });
                }
              }, 1000);
              deviceConnect.recharge2( du.value, res.data.order_no).then(_=> {
                if (device.value.net_type === 1) {
                  listenDeviceRecharge(res.data.order_no).then(() => {
                    clearInterval(countdown);
                    globalStore.setGlobDialog({show:false,type:'TimeReFLoading'})
                    // coutTimes=20
                    globalStore.setDeviceRefTime(30)
                  }).catch(() => {
                    handleRechargeFail(res.data.order_no)
                    clearInterval(countdown);
                    globalStore.setGlobDialog({show:false,type:'TimeReFLoading'})
                    // coutTimes=20
                    globalStore.setDeviceRefTime(30)
                  })
                } else {
                  listenDeviceRechargeBle(res.data.order_no).then(() => {
                    clearInterval(countdown);
                    globalStore.setGlobDialog({show:false,type:'TimeReFLoading'})
                    // coutTimes=20
                    globalStore.setDeviceRefTime(30)
                  }).catch(() => {
                    handleRechargeFail(res.data.order_no)
                    clearInterval(countdown);
                    globalStore.setGlobDialog({show:false,type:'TimeReFLoading'})
                    // coutTimes=20
                    globalStore.setDeviceRefTime(30)
                  })
                }
              }).catch(_=> {
                Taro.hideLoading()
                // Taro.showToast({
                //   title: '充值失败',
                //   icon: 'error'
                // })
                if(!isCurentPage.value) return
                globalStore.setGlobDialog({show:true,type:'RefFailed'})
                // Taro.showModal({
                //   title: '充值失败',
                //   content: '电量充值失败，请退出小程序重新操作（补充电量不再扣费，联系客服:400-926-2566)',
                //   showCancel: false,
                //   success: function (res) {
                //     if (res.confirm) {
                //       Taro.hideLoading()
                //       setTimeout(() => {
                //         Taro.reLaunch({
                //           url: '/pages/index/index'
                //         })
                //       },300)
                //     }
                //   }
                // })
                handleRechargeFail(res.data.order_no)
              })
            })
          },
          fail: () => {
            console.log('fail')
            Taro.hideLoading()
            Taro.showToast({
              title: '支付失败',
              icon: 'error'
            })
          }
        })
      }
      if (process.env.TARO_ENV === 'alipay') {
        my.tradePay ({
          // 调用统一收单交易创建接口（alipay.trade.create），获得返回字段支付宝交易号 trade_no
          tradeNO: payConfig.tradeNO,
          success: res2 => {
            if (res2.resultCode == 6004 || res2.resultCode == 8000 || res2.resultCode == 9000) {
              // todo 查询充值订单状态
              Taro.showLoading({
                title: '支付确认中...',
                mask: true
              })
              queryOrderStatus(res.data.order_no).then(_ => {
                // 请求充值
                // Taro.showLoading({
                //   title: '正在充值中...',
                //   mask: true
                // })
                globalStore.setGlobDialog({show:true,type:'TimeReFLoading'})
                countdown = setInterval(function() {
                  // coutTimes--
                  globalStore.setDeviceRefTime(globalStore.deviceRefTime-1)
                  if (globalStore.deviceRefTime <= 0) {
                    clearInterval(countdown);
                    globalStore.setGlobDialog({show:false,type:'TimeReFLoading'})
                    // coutTimes=20
                    globalStore.setDeviceRefTime(30)
                    globalStore.setGlobDialog({show:true,type:'RefFailed'})
                  } else {
                    // Taro.showLoading({
                    //   title: `设备充值剩余${(coutTimes).toFixed(0)}秒`,
                    //   mask: true
                    // });
                  }
                }, 1000);
                deviceConnect.recharge2( du.value, res.data.order_no).then(_=> {
                  if (device.value.net_type === 1) {
                    listenDeviceRecharge(res.data.order_no).then(() => {
                      clearInterval(countdown);
                      globalStore.setGlobDialog({show:false,type:'TimeReFLoading'})
                      // coutTimes=20
                      globalStore.setDeviceRefTime(30)
                    }).catch(() => {
                      handleRechargeFail(res.data.order_no)
                      clearInterval(countdown);
                      globalStore.setGlobDialog({show:false,type:'TimeReFLoading'})
                      // coutTimes=20
                      globalStore.setDeviceRefTime(30)
                    })
                  } else {
                    listenDeviceRechargeBle(res.data.order_no).then(() => {
                      clearInterval(countdown);
                      globalStore.setGlobDialog({show:false,type:'TimeReFLoading'})
                      // coutTimes=20
                      globalStore.setDeviceRefTime(30)
                    }).catch(() => {
                      handleRechargeFail(res.data.order_no)
                      clearInterval(countdown);
                      globalStore.setGlobDialog({show:false,type:'TimeReFLoading'})
                      // coutTimes=20
                      globalStore.setDeviceRefTime(30)
                    })
                  }
                }).catch(_=> {
                  Taro.hideLoading()
                  // Taro.showToast({
                  //   title: '充值失败',
                  //   icon: 'error'
                  // })
                  if(!isCurentPage.value) return
                  globalStore.setGlobDialog({show:true,type:'RefFailed'})
                  // Taro.showModal({
                  //   title: '充值失败',
                  //   content: '电量充值失败，请退出小程序重新操作（补充电量不再扣费，联系客服:400-926-2566)',
                  //   showCancel: false,
                  //   success: function (res) {
                  //     if (res.confirm) {
                  //       Taro.hideLoading()
                  //       setTimeout(() => {
                  //         Taro.reLaunch({
                  //           url: '/pages/index/index'
                  //         })
                  //       },300)
                  //     }
                  //   }
                  // })
                  handleRechargeFail(res.data.order_no)
                })

              })
            } else {
              Taro.showToast({
                title: '支付失败',
                icon: 'error'
              })
            }
          },
          fail: res => {
            console.log('fail', res)
            Taro.hideLoading()
            Taro.showToast({
              title: '支付失败',
              icon: 'error'
            })
          },
        });
      }
    }).catch(error => {
      yToastText.value = error.data.message
      showToast.value = true
      // Taro.showToast({
      //   title: error.data.message,
      //   icon: 'error'
      // })
    })
  }

  /**
   * 房东 临时用电充值成功 次数加1 增加充值度数
   */
  const handleRechargeBusinessSuccessModeOne = () => {
    if (globalStore.who === 'business') {
      // 增加充值成功的度数 注意传的是度数 不是金额
      request.get({
        url: 'device/incrBusinessAmount',
        data: {
          device_id: id.value,
          amount: du.value,
        },
        showToast:false
      })
    }
    if (globalStore.who === 'business' && mode.value == 1) {
      // TODO
      request.get({
        url: 'order/today_count',
        data: {
          sn: device.value?.sn,
        },
        showLoading: false,
        showToast:false
      })
    }
  }

  const handleRechargeSuccess = (orderNo) => {
    return new Promise((resolve, reject) => {
      request.post({
        url: 'order/success',
        data: {
          orderNo,
          identity: (device.value?.agent?.type == 2 && globalStore.who === 'business') ? 2 : 1,
          description: device.value?.net_type == 2 ? '蓝牙表充值成功' : undefined,
          // mode: (device.value?.agent?.type == 2 && globalStore.who === 'business' && mode.value == 1) ? 1 : undefined, //1:临时用电（需要加次数每日限制5次） 2：房东代充
        },
        showLoading: false,
        showToast:false
      }).then(res=> {
        //TODO如果有差值返回需要计算并更新当前总度数，当前总度数-差值=现在总度数-蓝牙表执行
        if (device.value?.net_type != 1 && (Number(res?.data?.decr) || 0) > 0) {
          const nowTotalDu = parseFloat(device.value.total) - parseFloat(res?.data?.decr || 0)
          deviceConnect.setTotalDu(nowTotalDu || Number(device.value.total))
        }
        resolve()
      }).catch(_=> {
        reject()
      })
    })
  }


  const handleRechargeFail = async(orderNo) => {
    const info = await getDevInfo(sn.value)//获取最新的du
    if (info?.net_type == 1) {
      // 最新度数发生变化了 说明充值成功了 不上报错误
          // parseFloat(res.data.du) > parseFloat(device.value.du)
      if(parseFloat(info?.du) > parseFloat(device.value.du)) return
      // 4g
      request.post({
        url: 'order/fail',
        data: {
          orderNo,
          before: device.value.du,
          after:info?.du
        },
        showLoading: false,
        showToast:false
      })
      console.log('4G表充值失败：');
      console.log("orderNo:"+orderNo);
      console.log("before:"+device.value.du);
      console.log("after:" + info?.du);
      rechargeFailSetDuInfo({
        du: info?.du,
        net_type: device.value?.net_type,
        sn: device.value?.sn,
        id: device.value?.id,
        rechargeDu: du.value
      })
    } else {
      const souDu = du.value
      // 如果是断开状态直接失败
      if (globalStore.device.isConnected == false) {
        if (Number(device.value.du) > Number(Taro.getStorageSync('rechargeBeforeDu'))) {
          // 认为成功上报成功
          request.post({
            url: 'order/success',
            data: {
              orderNo,
              description:`蓝牙表充值成功`,
            },
            showLoading: false
          })
          request.post({
            url: 'device/' + id.value + '/read',
            showLoading: false,
            showToast:false,
            data: {
              du: device.value.du,
              info: globalStore.who,
              log: 1
            }
          })
          return
        }
          request.post({
            url: 'order/fail',
            data: {
              orderNo,
              before: Number(Taro.getStorageSync('rechargeBeforeDu')),
              after:device.value.du
            },
            showLoading: false,
            showToast:false
          })
        return
      }
      // 获取蓝牙设备最新度
      deviceConnect.getConnection()
      .then(async() => {
        deviceConnect.queryStatus()
          Taro.showLoading({
            title: '正在读表中...',
            icon: 'none'
          })
          listenDeviceReadBle()
            .then((du) => {
              // if(du > device.value.du) return
              if (Number(du) > Number(Taro.getStorageSync('rechargeBeforeDu'))) {
                // 认为成功上报成功
                request.post({
                  url: 'order/success',
                  data: {
                    orderNo,
                    description:`蓝牙表充值成功`,
                  },
                  showLoading: false
                })
                request.post({
                  url: 'device/' + id.value + '/read',
                  showLoading: false,
                  showToast:false,
                  data: {
                    du: du,
                    info: globalStore.who,
                    log: 1
                  }
                })
                return
              }
              console.log('蓝牙表充值失败：');
              console.log("orderNo:"+orderNo);
              console.log("before:"+device.value.du);
              console.log("after:" + du);
              rechargeFailSetDuInfo({
                du,
                net_type: device.value?.net_type,
                sn: device.value?.sn,
                id: device.value?.id,
                rechargeDu: souDu
              })
              request.post({
                url: 'order/fail',
                data: {
                  orderNo,
                  before: Number(Taro.getStorageSync('rechargeBeforeDu')),
                  after:du
                },
                showLoading: false,
                showToast:false
              })
                // request.post({
                //   url: 'order/success',
                //   data: {
                //     orderNo,
                //     description:`蓝牙表充值失败`,
                //   },
                //   showLoading: false
                // })
          }).catch(() => {
             rechargeFailSetDuInfo({
                du: device.value.du,
                net_type: device.value?.net_type,
                sn: device.value?.sn,
                id: device.value?.id,
                rechargeDu: du.value
              })
            if (Number(device.value.du) > Number(Taro.getStorageSync('rechargeBeforeDu'))) {
              // 认为成功上报成功
              request.post({
                url: 'order/success',
                data: {
                  orderNo,
                  description:`蓝牙表充值成功`,
                },
                showLoading: false
              })
              request.post({
                url: 'device/' + id.value + '/read',
                showLoading: false,
                showToast:false,
                data: {
                  du: device.value.du,
                  info: globalStore.who,
                  log: 1
                }
              })
              return
            }
            request.post({
                url: 'order/fail',
                data: {
                  orderNo,
                  before: Number(Taro.getStorageSync('rechargeBeforeDu')),
                  after:device.value.du
                },
                showLoading: false,
                showToast:false
              })
              // request.post({
              //   url: 'order/success',
              //   data: {
              //     orderNo,
              //     description:`蓝牙表充值失败`,
              //   },
              //   showLoading: false
              // })
          })
      }).catch(() => {
          rechargeFailSetDuInfo({
            du: device.value.du,
            net_type: device.value?.net_type,
            sn: device.value?.sn,
            id: device.value?.id,
            rechargeDu: du.value
          })
        if (Number(device.value.du) > Number(Taro.getStorageSync('rechargeBeforeDu'))) {
          // 认为成功上报成功
          request.post({
            url: 'order/success',
            data: {
              orderNo,
              description:`蓝牙表充值成功`,
            },
            showLoading: false
          })
          request.post({
            url: 'device/' + id.value + '/read',
            showLoading: false,
            showToast:false,
            data: {
              du: device.value.du,
              info: globalStore.who,
              log: 1
            }
          })
          return
        }
        request.post({
            url: 'order/fail',
            data: {
              orderNo,
              before: Number(Taro.getStorageSync('rechargeBeforeDu')),
              after:device.value.du
            },
            showLoading: false,
            showToast:false
          })
          // request.post({
          //   url: 'order/success',
          //   data: {
          //     orderNo,
          //     description:`蓝牙表充值失败`,
          //   },
          //   showLoading: false
              // })
      })
    }

  }


  // 查询订单支付状态，10秒未返回显示失败
  const queryOrderStatus = (orderNo) => {
    let s = 0
    return new Promise((resolve, reject) => {

      timerStateSet.value = setInterval(() => {
        console.log(orderNo, 'orderStatus')
        s ++
        request.get({
          url: 'payment/orderStatus',
          showLoading: false,
          showToast:false,
          data: {
            orderNo
          }
        }).then(res => {
          console.log(res)
          if (res.data.is_paid) {
            Taro.hideLoading()
            clearInterval(timerStateSet.value)
            resolve()
          }
          if (s >= 10) {
            Taro.hideLoading()
            clearInterval(timerStateSet.value)
            Taro.showToast({
              title: '支付失败',
              icon: 'warning',
              duration: 2000
            })
            reject()
          }
        })

      }, 1000)
    })
  }

  // 临时订单充值成功提示
  const rechargeTempSucess = () => {
    if (globalStore.who == 'tenant' && show_order.value == 1) {
      Taro.showModal({
        title: '提示',
        content: '用电结束后需点击结束订单,剩余费用才可退回!',
        showCancel:false,
        success: function (res) {
          if (res.confirm) {
            // 点击确认
            Taro.redirectTo({
              url: "/pages/tenant/tempOrder/tempOrder",
            });
          } else {
            // 点击取消
          }
        }
      })
    }
  }

  const listenDeviceRecharge = (orderNo) => {
    let s = 0
    return new Promise((resolve, reject) => {
      timerRechargeSet.value = setInterval(() => {
        s ++

        request.get({
          url: 'device/' + id.value,
          showLoading: false,
          showToast:false
        }).then(res => {
          if (parseFloat(res.data.du) > parseFloat(device.value.du)) {

            if (orderNo) {
              handleRechargeSuccess(orderNo).then(_=> {
                device.value.du = res.data.du
                Taro.hideLoading()
                clearInterval(timerRechargeSet.value)
                clearInterval(countdown);
                    // coutTimes=20
                    globalStore.setGlobDialog({show:false,type:'TimeReFLoading'})
                    globalStore.setDeviceRefTime(30)
                Taro.showToast({
                  title: '充值成功',
                  duration: 3000
                })
                globalStore.homeHasChange = true
                setTimeout(() => {
                  if (show_order.value == 1) {
                    rechargeTempSucess()
                  } else {
                    Taro.navigateBack()
                  }
                }, 1000)
                resolve()

              })
            } else {
              device.value.du = res.data.du
              Taro.hideLoading()
              clearInterval(timerRechargeSet.value)
              clearInterval(countdown);
              globalStore.setGlobDialog({show:false,type:'TimeReFLoading'})
              // coutTimes=20
              globalStore.setDeviceRefTime(30)
              Taro.showToast({
                title: '充值成功',
                duration: 3000
              })
              handleRechargeBusinessSuccessModeOne()
              globalStore.homeHasChange = true
              setTimeout(() => {
                Taro.navigateBack()
              }, 1000)
              resolve()
            }

          }
          if (s >= 30) {
            Taro.hideLoading()
            clearInterval(countdown);
            globalStore.setGlobDialog({show:false,type:'TimeReFLoading'})
            // coutTimes=20
            globalStore.setDeviceRefTime(30)
            clearInterval(timerRechargeSet.value)
            // Taro.showToast({
            //   title: '充值失败',
            //   icon: 'error',
            //   duration: 2000
            // })
            if(!isCurentPage.value) return
            globalStore.setGlobDialog({show:true,type:'RefFailed'})
            // Taro.showModal({
            //   title: '充值失败',
            //   content: '电量充值失败，请退出小程序重新操作（补充电量不再扣费，联系客服:400-926-2566)',
            //   showCancel: false,
            //   success: function (res) {
            //     if (res.confirm) {
            //       Taro.hideLoading()
            //       clearInterval(countdown);
            //       // coutTimes=20
            //       globalStore.setGlobDialog({show:false,type:'TimeReFLoading'})
            //       globalStore.setDeviceRefTime(30)
            //       setTimeout(() => {
            //         Taro.reLaunch({
            //           url: '/pages/index/index'
            //         })
            //       },300)
            //     }
            //   }
            // })
            reject()
          }
        })

      }, 1000)
    })
  }

  const listenDeviceRechargeBle = (orderNo) => {
    let s = 0
    return new Promise((resolve, reject) => {
      timerRechargeBleSet.value = setInterval(() => {
        s ++

        if (globalStore.device.message.length > 0) {
          let msg = globalStore.device.message.shift()
          console.log(msg, device.value.mac)
          if (msg.mac === device.value.mac) {
            if (msg.event === '0A' && Number(msg.du) > Number(device.value.du)) {
              device.value.du = msg.du
              let postData = {
                du: msg.du,
                info: globalStore.who,
                log: 1
              }
              request.post({
                url: 'device/' + id.value + '/read',
                showLoading: false,
                showToast:false,
                data: postData
              }).then(_=> {

                if (orderNo) {
                  handleRechargeSuccess(orderNo).then(_=> {
                    Taro.hideLoading()
                    clearInterval(countdown);
                    // coutTimes=20
                    globalStore.setGlobDialog({show:false,type:'TimeReFLoading'})
                    globalStore.setDeviceRefTime(30)
                    clearInterval(timerRechargeBleSet.value)
                    Taro.showToast({
                      title: '充值成功',
                      duration: 3000
                    })
                    globalStore.homeHasChange = true
                    setTimeout(() => {
                      if (show_order.value == 1) {
                        rechargeTempSucess()
                      } else {
                        Taro.navigateBack()
                      }
                    }, 1000)
                    resolve()

                  })
                } else {
                  Taro.hideLoading()
                  clearInterval(countdown);
                    // coutTimes=20
                    globalStore.setGlobDialog({show:false,type:'TimeReFLoading'})
                    globalStore.setDeviceRefTime(30)
                  clearInterval(timerRechargeBleSet.value)
                  Taro.showToast({
                    title: '充值成功',
                    duration: 3000
                  })
                  handleRechargeBusinessSuccessModeOne()
                  globalStore.homeHasChange = true
                  setTimeout(() => {
                    Taro.navigateBack()
                  }, 1000)
                  resolve()
                }

              })
            }

            // if (s >= 30) {
            //   Taro.hideLoading()
            //   clearInterval(timer)
            //   // Taro.showToast({
            //   //   title: '操作超时',
            //   //   icon: 'warning',
            //   //   duration: 2000
            //   // })
            //   Taro.showModal({
            //     title: '充值失败',
            //     content: '电量充值失败，请退出小程序重新操作（补充电量不再扣费，联系客服:400-926-2566)',
            //     showCancel: false,
            //     success: function (res) {
            //       if (res.confirm) {

            //       }
            //     }
            //   })
            //   reject()
            // }
          }
        }

        if (s >= 30) {
          Taro.hideLoading()
          clearInterval(countdown);
          //  coutTimes=20
          globalStore.setGlobDialog({show:false,type:'TimeReFLoading'})
          globalStore.setDeviceRefTime(30)
          clearInterval(timerRechargeBleSet.value)
          if(!isCurentPage.value) return
          globalStore.setGlobDialog({show:true,type:'RefFailed'})
            // Taro.showModal({
            //   title: '充值超时',
            //   content: '电量充值失败，请退出小程序重新操作（补充电量不再扣费，联系客服:400-926-2566)',
            //   showCancel:false,
            //   success: function (res) {
            //     if (res.confirm) {
            //       console.log('用户点击确定')
            //       // Taro.navigateBack()
            //       Taro.hideLoading()
            //       setTimeout(() => {
            //         Taro.reLaunch({
            //           url: '/pages/index/index'
            //         })
            //       },300)
            //     } else if (res.cancel) {
            //       console.log('用户点击取消')
            //     }
            //   }
            // })
            reject()
            // request.post({
            //   url: 'order/fail',
            //   data: {
            //     orderNo,
            //     before: device.value.du,
            //     after:device.value.du
            //   },
            //   showLoading: false,
            //   showToast:false
            // })
        }

      }, 1000)
    })
  }

  const onRechargeLog = () => {
    Taro.navigateTo({
      url: '/pages/device/log/log?action=3&id=' + id.value
    })
  }

  const updateOpenId = () => {
    if (process.env.TARO_ENV === 'weapp') {
      wx.login({
        success(res) {
          if (res.code) {
            request.post({
              url: 'user/updateOpenId',
              showLoading: false,
              showToast:false,
              data: {
                code: res.code
              }
            }).then(_ => {

            })
          }
        },
        fail() {
          Taro.showToast({
            title: '获取openid出错',
            icon: 'error'
          })
        }
      })
    } else {
      my.getAuthCode({
        scopes: 'auth_base',
        success: res => {
          // 在服务端获取用户信息
          if (res.authCode) {
            request.post({
              url: 'user/updateAlipayId',
              showLoading: false,
              showToast:false,
              data: {
                code: res.authCode
              }
            }).then(_ => {

            })
          }
        },
        fail: err => {
          console.log('my.getAuthCode 调用失败', err)
        }
      });
    }
  }

  const onJump = (path) => {
    Taro.navigateTo({
      url: path
    })
  }



  </script>
