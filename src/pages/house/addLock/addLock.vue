<template>
  <view class="add-lock-container" v-if="!isSearch">
    <view class="title">模亮触摸屏</view>
    <view>
      <image :src="locakBg" mode="aspectFill" class="img" />
    </view>
    <view class="desc">
      模亮触摸屏, 锁进入可添加状态, 点击下一步 搜索门锁
    </view>
    <view class="btn" @tap="toHandel"> 下一步 </view>
  </view>
  <view class="add-lock-container2" v-else>
    <view class="title">搜索可用设备</view>
    <view class="list">
      <view class="li" v-for="(item, index) in lockLists" :key="index">
        <view class="le">
          <image :src="ble" mode="aspectFill" class="img" />
          <view>{{ item.deviceName || item.MAC }}</view>
        </view>
        <view style="display: flex; align-items: center">
          <view class="ri" @tap="init(index)"> 添加 </view>
          <view style="margin-left: 8rpx">{{
            !item.isSettingMode ? "(已被初始化)" : ""
          }}</view>
        </view>
      </view>
    </view>
  </view>
  <YModal
    title="no"
    confirmText="no"
    :show="showAddTip"
    @close="showAddTip = false"
    showCancel
    maskClose
    :bodyStyle="{ backgroundColor: 'transparent' }"
  >
    <template #content>
      <view class="tip-container">
        <image
          :src="addType == 0 ? addE : addS"
          class="img"
          mode="aspectFit"
        ></image>
      </view>
    </template>
  </YModal>
</template>

<script setup>
import request from "@/utils/request";
import YModal from "@/components/YModal/index.vue";


const locakBg = 'https://yimits.oss-cn-beijing.aliyuncs.com/picss/lock-bg.png'
const ble = 'https://yimits.oss-cn-beijing.aliyuncs.com/picss/ble.png'
const addE = 'https://yimits.oss-cn-beijing.aliyuncs.com/picss/add-erro.png'
const addS = 'https://yimits.oss-cn-beijing.aliyuncs.com/picss/add-succ.png'


import {
  initLockSdk,
  stopScanBleDevice,
  startScanBleDevice,
sendKey,
} from "@/utils/lock";
import Taro, {
  useDidShow,
  useLoad,
  useUnload,
  usePullDownRefresh,
} from "@tarojs/taro";
import { ref } from "vue";
import { useGlobalStore } from "@/stores";
import { AES_Encrypt, AES_Decrypt, MD5_Encrypt } from "@/utils/crypto";
import { clientId, clientSecret } from "@/config";

const showAddTip = ref(false);

const addType = ref(0); // 0:添加失败 1:添加成功

const globalStore = useGlobalStore();

const isSearch = ref(false);

const lockLists = ref([]);

const detailItem = ref();

const isInitLock = ref(false);

const ttlockAccount = ref(); //锁账户

const tokenVal = ref();

usePullDownRefresh(() => {
  console.log("resdfd");
  // console.log(MD5_Encrypt('***********'));
  if (!isSearch.value) return Taro.stopPullDownRefresh();
  lockLists.value = [];
  searchBle();
  // 关闭刷新
});

const toHandel = () => {
  isSearch.value = true;
  Taro.setNavigationBarTitle({
    title: "搜索可用设备",
  });
  searchBle();
};

// 搜索附近蓝牙设备
const searchBle = () => {
  Taro.showLoading({
    title: "蓝牙设备扫描中",
  });
  startScanBleDevice().then((lockList) => {
    // TODO 成功扫描到设备
    lockLists.value = lockList;
    Taro.hideLoading();
    Taro.stopPullDownRefresh();
  });
};

// 初始化蓝牙设备(click item)
const init = (index) => {
  Taro.showModal({
    title: "提示",
    content: "是否添加该设备",
    success: (res) => {
      if (res.confirm) {
        console.log("用户点击确定");
        isInitLock.value = true;
        const lockItem = lockLists.value[index];
        handleInitLock(lockItem);
      } else {
        console.log("用户点击取消");
      }
    },
  });
};

const handleInitLock = (deviceFromScan) => {
  console.log(deviceFromScan, "deviceFromScan");
  if (!deviceFromScan.isSettingMode) {
    isInitLock.value = false;
    Taro.showToast({
      title: `智能锁${
        deviceFromScan.deviceName || deviceFromScan.MAC
      }已被初始化，当前不可添加`,
      icon: "none",
    });
    console.log(
      `智能锁${
        deviceFromScan.deviceName || deviceFromScan.MAC
      }已被初始化，当前不可添加`
    );
    return;
  }
  lockLists.value = [];
  console.log(
    `正在初始化蓝牙智能锁${deviceFromScan.deviceName}, MAC地址：${deviceFromScan.MAC}`
  );
  Taro.showLoading({
    title: "添加中...",
  });
  initLockSdk({ deviceFromScan, accessToken: tokenVal.value,lockAlias:detailItem.value.house_name })
    .then((res) => {
      console.log(res, "resApi");
      showAddTip.value = true;
      addType.value = 1;
      // 绑定
      request
        .post({
          url: "ttlock/bind",
          data: {
            lockId: res.lockId,
            keyId: res.keyId,
            house_id: detailItem.value.house_id,
            type: "1",
          },
        })
        .then(() => {
          const item = {
            lockId: res.lockId,
            house_name: detailItem.value.house_name,
            status: detailItem.value.status,
            house_id: detailItem.value.house_id,
          };
          // 如果有快速入住给租客发钥匙
          if (detailItem.value.tenantMobile) {
            sendKey({
              lockId: res.lockId,
              phone: detailItem.value.tenantMobile,
              house_id: detailItem.value.house_id
            })
          }
          setTimeout(() => {
            Taro.navigateTo({
              url:
                "/pages/house/lockDetail/lockDetail?item=" +
                JSON.stringify(item),
            });
          }, 1500);
        });
    })
    .catch(() => {
      showAddTip.value = true;
      addType.value = 0;
    })
    .finally(() => {
      Taro.hideLoading();
      setTimeout(() => {
        showAddTip.value = false;
      }, 3000);
    });
};

// 获取账户
const getAccount = () => {
  request
    .get({
      url: "ttlock/userDetail",
    })
    .then((res) => {
      console.log(res);
      if (!res.data.length || !res.data) {
        regsion();
      } else {
        ttlockAccount.value = res.data.filter((item) => item.type == "1")[0];
        getToken({
          username: ttlockAccount.value.username,
          password: ttlockAccount.value.password_hash,
        });
      }
    });
};

// 添加账户
const addAccount = ({ username, password, password_hash }) => {
  request
    .post({
      url: "ttlock/saveUser",
      data: {
        username,
        password,
        password_hash,
        type: "1", //1=房东 2=租客
      },
    })
    .then((res) => {
      console.log(res);
      // 更新
      request
        .get({
          url: "ttlock/userDetail",
        })
        .then((res) => {
          console.log(res);
          if (!res.data.length || !res.data) {
            console.log("没有数据");
          } else {
            ttlockAccount.value = res.data.filter((item) => item.type == "1")[0];
            getToken({
              username: ttlockAccount.value.username,
              password: ttlockAccount.value.password_hash,
            });
          }
        });
    });
};

/**
 * 获得token
 */
const getToken = ({ username, password }) => {
  return new Promise((resolve, reject) => {
    request
      .post({
        url: "/oauth2/token",
        LOCKYAPI: true,
        data: {
          username,
          password,
          client_id: clientId,
          client_secret: clientSecret,
          grant_type: "password",
          redirect_uri: "http://www.sciener.cn",
        },
      })
      .then((res) => {
        console.log(res);
        resolve();
        Taro.setStorageSync("ttlaccessToken", res.access_token);
        tokenVal.value = res.access_token;
        globalStore.setTtlaccessToken(res.access_token);
      });
  });
};

// 删除账户
const delAccount = () => {
  request
    .post({
      url: "ttlock/deleteUser",
      data: {
        username: ttlockAccount.value.username,
      },
    })
    .then((res) => {
      console.log(res);
    });
};

// 注册锁账户
const regsion = () => {
  return new Promise(async (resolve, reject) => {
    const userInfo = await globalStore.getUserInfo();
    console.log(userInfo);
    const option = {
      username: `${userInfo.mobile}shanzupo`,
      password: MD5_Encrypt(userInfo.mobile),
      date: new Date().getTime(),
    };
    request
      .post({
        url: "/v3/user/register",
        LOCKYAPI: true,
        data: {
          clientId,
          clientSecret,
          ...option,
        },
      })
      .then((res) => {
        console.log(res);
        // addAccount({
        //     username: 'didfj_***********shanzupo',
        //     password: '***********',
        //     password_hash: MD5_Encrypt('***********'),
        //   })
        if (res.username) {
          // 注册成功 同步账户
          resolve();
          addAccount({
            username: res.username,
            password: userInfo.mobile,
            password_hash: MD5_Encrypt(userInfo.mobile),
          });
        }
      });
  });
};

useUnload(() => {
  stopScanBleDevice();
});

useLoad((query) => {
  console.log(query.item);
  if (!query.item) {
    Taro.showModal({
      title: "提示",
      content: "参数错误请退出小程序重新进入重试！",
      showCancel: false,
      success: (res) => {
        Taro.reLaunch({
          url: "/pages/index/index",
        });
      }
    })
    return;
  }
  const item = JSON.parse(query.item);
  detailItem.value = item;
  // request
  //   .post({
  //     url: "ttlock/bind",
  //     data: {
  //       lockId: '********',
  //       keyId: '*********',
  //       house_id: detailItem.value.house_id,
  //       type: "1",
  //     },
  //   })
  //   addAccount({
  //       username: 'didfj_***********shanzupo',
  //       password: '***********',
  //       password_hash: MD5_Encrypt('***********'),
  //     })
  console.log(item);
  getAccount();
  // request.post({
  //   url: "ttlock/bind",
  //   data: {
  //     lockId: "********",
  //     keyId: "*********",
  //     house_id: detailItem.value.house_id,
  //     type: "1",
  //   },
  // });
});
</script>

<style lang="scss">
page {
  background-color: #f1f2f3;
}
.tip-container {
  text-align: center;
  .img {
    width: 381px;
    height: 281px;
  }
}
.add-lock-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 60px;
  .title {
    font-weight: 400;
    font-size: 34px;
    color: #000000;
  }
  .img {
    width: 409px;
    height: 407px;
    margin-top: 170px;
    margin-bottom: 195px;
  }
  .desc {
    font-weight: 400;
    font-size: 28px;
    color: #878787;
  }
  .btn {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 660px;
    height: 77px;
    background: #3678f3;
    border-radius: 39px;
    color: #fff;
    margin-top: 142px;
  }
}
.add-lock-container2 {
  padding: 10px;
  .title {
    font-weight: 400;
    font-size: 28px;
    color: #525252;
    margin-bottom: 10px;
  }
  .li {
    width: 100%;
    height: 100px;
    background: #ffffff;
    border-bottom: 1px solid #f1f2f3;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 30px;
    box-sizing: border-box;
    .le {
      display: flex;
      align-items: center;
      .img {
        width: 24px;
        height: 40px;
        margin-right: 20px;
      }
    }
    .ri {
      border-radius: 25px;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #4365f5;
      width: 79px;
      height: 39px;
      background: #dfe5ff;
      border-radius: 20px;
      font-size: 22px;
      padding: 5px;
    }
  }
}
</style>
