<template>
  <View class="container">
    <MySearch placeholder="关健词搜索" @search="onSearch" style="background-color: #1352fd;"></MySearch>
    <MyFilter :options="options" @change="onChangeFilter"></MyFilter>
    <view class="status-filter">
      <text
        @tap="onChangeStatus('all')"
        class="tag"
        :class="params.status === 'all' ? 'tag-selected' : 'tag-default'"
        >全部</text
      >
      <text
        @tap="onChangeStatus('vacant')"
        class="tag"
        :class="params.status === 'vacant' ? 'tag-selected' : 'tag-default'"
        >未出租</text
      >
      <text
        @tap="onChangeStatus('hired')"
        class="tag"
        :class="params.status === 'hired' ? 'tag-selected' : 'tag-default'"
        >已出租</text
      >
      <text
        @tap="onChangeStatus('signing')"
        class="tag"
        :class="params.status === 'signing' ? 'tag-selected' : 'tag-default'"
        >签定中</text
      >
      <text
        @tap="onChangeStatus('fail')"
        class="tag"
        :class="params.status === 'fail' ? 'tag-selected' : 'tag-default'"
        >房租已逾期</text
      >
      <text
        @tap="onChangeStatus('expire')"
        class="tag"
        :class="params.status === 'expire' ? 'tag-selected' : 'tag-default'"
        >合同快到期</text
      >
    </view>
    <view class="house-stat flex flex-space-between flex-v-center">
      <view
        >总房源<text class="stat-num">{{ total }}</text
        >套</view
      >
      <view
        ><image :src="IconAboutStatus" class="icon-about-status"></image>
        <text class="link">关于房态图</text></view
      >
    </view>
    <view class="house-list">
      <!-- <view class="no-data">暂无信息</view> -->
      <view v-for="(item, index) in items" :key="item.id" class="item-box-q">
        <AtSwipeAction
           autoClose
           :disabled="item.device.length >0 ? true : false"
           @click="onClickDelHandel"
           @opened="openedHandel(index)"
           @closed="closedHandel(index)"
           :isOpened="item.show"
          :options="[
            {
              text: '删除',
              style: {
                backgroundColor: '#FF4949',
              },
              item
            },
          ]"
        >
          <view
            class="house-item"
            :class="'house-status-' + item.status_key"
            @tap="onDetail(item.id)"
          >
            <view class="house-title">
              <image
                :src="IconHouseMini"
                class="icon-house-mini"
                mode="widthFix"
              ></image
              >{{ item.name }}
            </view>
            <view
              ><text class="house-rent">￥{{ item.rent }}/月</text></view
            >
            <view class="mt36 flex flex-v-center flex-space-between">
              <view
                ><text class="house-tenant">{{ item.tenant_name }}</text></view
              >
              <view class="house-day">
                <image
                  :src="IconQuan"
                  style="width: 26rpx; height: 26rpx; vertical-align: middle"
                ></image>
                <text
                  v-if="item.status_key === 'vacant'"
                  style="vertical-align: middle"
                >
                  空置 {{ item.create_days }} 天</text
                >
                <text
                  v-if="item.status_key === 'hired'"
                  style="vertical-align: middle"
                >
                  租期还剩 {{ item.days }} 天</text
                >
                <text
                  v-if="item.status_key === 'expire'"
                  style="vertical-align: middle"
                >
                  合同还有 {{ item.days }} 天到期</text
                >
                <text
                  v-if="item.status_key === 'expired'"
                  style="vertical-align: middle"
                >
                  租期已过 {{ item.days }} 天</text
                >
                <text
                  v-if="item.status_key === 'fail'"
                  style="vertical-align: middle"
                >
                  账单逾期 {{ item.fail_days }} 天</text
                >
                <text
                  v-if="item.status_key === 'fast'"
                  style="vertical-align: middle"
                  >{{
                    item.contracts?.expire_status == 2
                      ? `租期已过${item.days}天`
                      : `租期还剩${item.days}天`
                  }}</text
                >
              </view>
            </view>
            <view class="house-status">{{ item.status }}</view>
            <view class="house-left-border"></view>
          </view>
        </AtSwipeAction>
      </view>
    </view>

    <view class="footer-fixed" style="z-index: 999999999;">
      <button class="btn-add m33" @tap="onAdd">创建房源</button>
    </view>
  </View>
</template>
<script setup>
  import { ref } from 'vue'
  import './house.scss'
  import Taro, { useDidShow, useDidHide, useReady, useLoad, usePullDownRefresh, useReachBottom} from '@tarojs/taro'
  import request from '@/utils/request'
  import { AtSwipeAction } from 'taro-ui-vue3/lib'


  import MySearch from '@/components/MySearch'
  import MyFilter from '@/components/MyFilter'

  const IconAboutStatus = 'https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-about-status.png'
  const IconHouseMini = 'https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-house-mini.png'
  const IconQuan = 'https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-quan.png'

  import { useGlobalStore } from '@/stores'
  const globalStore = useGlobalStore()

  const options = ref([])


  usePullDownRefresh(() => {
    console.log('onPullDownRefresh')
  })

  const onChangeFilter = (child, key) => {
    console.log(child, key)
    params.value[key] = child.value
    fetch()
  }
  const onSearch = (keyword) => {
    params.value.keyword = keyword
    fetch()
  }

  const onDetail = (id) => {
    Taro.navigateTo({
      url: '/pages/house/detail/detail?id=' + id
    })
  }

  // 删除房源
  const onClickDelHandel = ({item}) => {
    console.log(item);
    Taro.showModal({
      title: '温馨提示',
      content: '是否确认删除房源？',
      confirmText: '确认',
      success: res => {
        if (res.confirm) {
          Taro.showLoading({
            title: '删除中...'
          })
          request.delete({
            url: 'house/'+item.id,
            showLoading:false,
          }).then((res) => {
            if (res.code == 200) {
              Taro.showToast({
                title: '删除成功！',
                icon: "success",
                duration: 2000,
                success: () => {
                  setTimeout(() => {
                    fetch()
                  },1500)
                }
              })
            }
          })
        }
      }
    })
  }

  const openedHandel = (idx) => {
    console.log(idx);
    items.value.forEach(item=> {
      item.show = false
    })
    items.value[idx].show = true
  }

  const closedHandel = (idx) => {
    console.log('closed');
    items.value[idx].show = false
  }

  /** ----------------------接口数据-begin----------------------------------- */
  const items = ref([])
  const total = ref(0)

  const params = ref({
    page: 1,
    status: 'all',
    keyword: '',
    sort: 'created_at',
    order: 'desc'
  })

  const onChangeStatus = (val) => {
    params.value.status = val
    fetch()
  }

  const fetch = () => {
    items.value = []
    params.value.page = 1
    getList()
  }

  const getList = () => {
    request.get({
      url: 'house',
      data: {
        ... params.value
      }
    }).then(res => {
      if (items.value.length > 0) {
        items.value = [...items.value, ... res.data.items]
      } else {
        items.value = res.data.items
      }
      total.value = res.data.total
      if (res.data.currentPage >= res.data.lastPage) {
        isLastPage.value = true
      }
    })
  }
  /** ----------------------接口数据-end----------------------------------- */

  /** ----------------------配置数据-begin----------------------------------- */
  const getFilterConfig = () => {
    request.get({
      url: 'house/filter/config'
    }).then(res => {
      options.value = res.data
    })
  }
  /** ----------------------配置数据-end----------------------------------- */
  useLoad(() => {
    getFilterConfig()
  })

  useDidShow(() => {
    fetch()
  })

  usePullDownRefresh(() => {
    console.log('onPullDownRefresh')
    isLastPage.value = false
    Taro.stopPullDownRefresh()
    fetch()
  })


  const isLastPage = ref(false)

  useReachBottom(() => {
    console.log("bottom");
    if (!isLastPage.value) {
      params.value.page += 1;
      getList();
    }
  })

  const onAdd = () => {
    Taro.navigateTo({
      url: '/pages/house/add/add'
    })
  }


</script>
