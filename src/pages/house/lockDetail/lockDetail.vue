<script setup>
import MyIcon from "@/components/MyIcon";
import request from "@/utils/request";
import Taro, { useDidShow, useLoad } from "@tarojs/taro";
import { onMounted, ref, unref } from "vue";
import { useGlobalStore } from "@/stores";
const globalStore = useGlobalStore();
import {
  createPwdHandel,
  toReadRecord,
  getLockPwdList,
  getLockList,
  modifyPwdHandel,
  handleResetLock,
  sendKeyHandel,
} from "@/utils/lock";
import { clientId, clientSecret } from "@/config";
import { AES_Encrypt, AES_Decrypt, MD5_Encrypt } from "@/utils/crypto";

const eysopen = "https://yimits.oss-cn-beijing.aliyuncs.com/picss/eys-open.png";
const tip = "https://yimits.oss-cn-beijing.aliyuncs.com/picss/tip.png";
const tip2 = "https://yimits.oss-cn-beijing.aliyuncs.com/picss/tip-b.png";
const dian = "https://yimits.oss-cn-beijing.aliyuncs.com/picss/dian-bg.png";
const routeimg = "https://yimits.oss-cn-beijing.aliyuncs.com/picss/route.png";
const yuanimg = "https://yimits.oss-cn-beijing.aliyuncs.com/picss/yuan-bg.png";
const suoimg = "https://yimits.oss-cn-beijing.aliyuncs.com/picss/key-2.png";
const keyAdmin =
  "https://yimits.oss-cn-beijing.aliyuncs.com/picss/key-admin.png";
const rizhi = "https://yimits.oss-cn-beijing.aliyuncs.com/picss/rizhi.png";
const unbind = "https://yimits.oss-cn-beijing.aliyuncs.com/picss/unbind.png";

const isOPenLockShow = ref(false); //是否开锁

const isOpenLock = ref(false); //是否正在开锁

const keyInfo = ref(null); //锁信息

const lockId = ref(); //暂时模拟

const fastMobile = ref();

const house_id = ref();

const keyList = ref([]);

const updateDianTime = ref(Date.now()); //电量更新时间

const itemDetail = ref();

useLoad((q) => {
  console.log(q);
  const item = JSON.parse(q.item);
  itemDetail.value = item;
  lockId.value = item.lockId; //锁id
  fastMobile.value = item.fastMobile;

  // request.post({
  //     url: "/v3/lock/delete",
  //     LOCKYAPI: true,
  //     data: {
  //       lockId:18188435,
  //       clientId,
  //       accessToken: globalStore.ttlaccessToken,
  //       date: new Date().getTime(),
  //     },
  //   })

  console.log("load");
  // 获取钥匙列表
  console.log("获取钥匙列表");
  getLockList().then((res) => {
    console.log(res);
    keyList.value = res.list;
    // 查找id
    const index = res.list.findIndex((item) => item.lockId == lockId.value);
    keyInfo.value = res.list[index];

    globalStore.setkeyInfo(keyInfo.value);
    console.log(keyInfo.value, "keyInfo.value ");
  });
});

function timestampToDate(timestamp) {
  const date = new Date(timestamp);
  return `${date.getFullYear()}-${("0" + (date.getMonth() + 1)).slice(-2)}-${(
    "0" + date.getDate()
  ).slice(-2)} ${("0" + date.getHours()).slice(-2)}:${(
    "0" + date.getMinutes()
  ).slice(-2)}:${("0" + date.getSeconds()).slice(-2)}`;
}

/**
 * 开锁
 */
const openLockHandel = () => {
  isOpenLock.value = true;
  Taro.showLoading({ title: "正在开锁" });
  requirePlugin("myPlugin", ({ controlLock }) => {
    const start = Date.now();
    // 控制智能锁
    controlLock({
      /* 控制智能锁方式 3 -开锁, 6 -闭锁 */
      controlAction: 3,
      lockData: unref(keyInfo).lockData,
      serverTime: Date.now(),
    }).then((res) => {
      console.log(res);
      if (res.errorCode == 0) {
        Taro.showToast({ icon: "success", title: "已开锁" });
        isOPenLockShow.value = true;
        setTimeout(() => {
          isOPenLockShow.value = false;
        }, 4000);
        // toReadRecord({ keyInfo: keyInfo.value }).then(() => {
        //   updateDianTime.value = new Date().getTime();
        //   setTimeout(() => {
        //     isOPenLockShow.value = false;
        //   }, 4000);
        // });
        isOpenLock.value = false;
      } else {
        Taro.hideLoading();
        isOpenLock.value = false;
        isOPenLockShow.value = false;
        Taro.showToast({
          title: `开锁失败：${res.errorMsg}`,
          icon: "none",
          duration: 5000,
        });
      }
    });
  });
};

const unQbindHandel = () => {
  Taro.showModal({
    title: "提示",
    content: "确定要强制解绑吗？强制解绑后云端数据将丢失需要手动重置锁后重新添加!",
    success: (res) => {
      if (res.confirm) {
        console.log("用户点击确定");
        Taro.showLoading({ title: "正在强制解绑" });
        console.log(lockId.value, "lockId.value");
        console.log(itemDetail.value.house_id, "itemDetail.value.house_id");
        request
          .post({
            url: "ttlock/unbind",
            data: {
              lockId: lockId.value,
              house_id: itemDetail.value.house_id,
            },
            showLoading: true,
          })
          .then(() => {
            request.post({
              url: "/v3/lock/delete",
              LOCKYAPI: true,
              data: {
                lockId: lockId.value,
                clientId,
                accessToken: globalStore.ttlaccessToken,
                date: new Date().getTime(),
              },
            });
            Taro.hideLoading;
            Taro.showToast({ title: "解绑成功", icon: "success" });
          })
          .catch(() => {
            Taro.hideLoading;
          });
        setTimeout(() => {
          Taro.showToast({ title: "解绑成功", icon: "success" });
          setTimeout(() => {
            Taro.navigateBack();
          }, 800);
        }, 1200);
      } else {
        console.log("用户点击取消");
      }
    },
  });
};

const unbindHandel = () => {
  Taro.showModal({
    title: "提示",
    content: "确定要解绑吗？",
    success: (res) => {
      if (res.confirm) {
        console.log("用户点击确定");
        Taro.showLoading({ title: "正在解绑" });
        handleResetLock({
          lockId: lockId.value,
          lockData: keyInfo.value.lockData,
        }).then(() => {
          request
            .post({
              url: "ttlock/unbind",
              data: {
                lockId: lockId.value,
                house_id: itemDetail.value.house_id,
              },
              showLoading: true,
            })
            .then(() => {
              Taro.hideLoading;
              Taro.showToast({ title: "解绑成功", icon: "success" });
            })
            .catch(() => {
              Taro.hideLoading;
            });
          setTimeout(() => {
            Taro.showToast({ title: "解绑成功", icon: "success" });
            setTimeout(() => {
              Taro.navigateBack();
            }, 800);
          }, 1200);
        });
      } else {
        console.log("用户点击取消");
      }
    },
  });
};

const rizhiHandel = () => {
  Taro.navigateTo({
    url: "/pages/house/lockRizhi/lockRizhi?lockId=" + lockId.value,
  });
};

const pwdAdminHandel = () => {
  Taro.navigateTo({
    url: `/pages/house/passwordAdmin/passwordAdmin?lockId=${lockId.value}&fastMobile=${fastMobile.value}&house_id=${itemDetail.value.house_id}`,
  });
};
</script>

<template>
  <view class="lock-detail-container">
    <view class="title">
      <view class="tit"> {{ itemDetail?.house_name }} </view>
      <view class="tag"> {{ itemDetail?.status || "未知" }} </view>
    </view>
    <!-- lock -->
    <view class="lock-box">
      <view class="dianb-box">
        <view class="box">
          <view
            :style="{
              backgroundColor:
                keyInfo?.electricQuantity <= 50 ? '#f64d19' : '#02b159',
              width: `${(keyInfo?.electricQuantity / 100) * 85}%`,
            }"
            class="con"
          ></view>
        </view>
        <view class="txt">{{ keyInfo?.electricQuantity }}%</view>
      </view>
      <view class="_name">
        {{ keyInfo?.lockAlias || keyInfo?.lockName || keyInfo?.lockMac }}
      </view>
      <view class="content">
        <image
          :src="routeimg"
          class="routeimg"
          :class="isOpenLock ? 'route' : 'route paused'"
          mode="aspectFill"
        ></image>
        <image
          :src="yuanimg"
          class="yuanimg"
          mode="aspectFill"
          @tap.stop="openLockHandel"
        ></image>
        <view class="suo" @tap.stop="openLockHandel">
          <image
            :src="isOPenLockShow ? suoimgOpen : suoimg"
            class="suoimg"
            mode="aspectFill"
          ></image>
          <text>{{ isOPenLockShow ? "已开锁" : "点击开锁" }}</text>
        </view>
        <view class="bottom">
          <view>门锁</view>
          <view class="desc">更新于{{ timestampToDate(updateDianTime) }}</view>
        </view>
      </view>
    </view>
    <!-- utils -->
    <view class="utils-box">
      <view class="li" @tap="pwdAdminHandel">
        <view class="img">
          <image :src="keyAdmin" class="suoimg" mode="aspectFill"></image>
        </view>
        <view class="label">密码管理</view>
      </view>
      <view class="li" @tap="rizhiHandel">
        <view class="img">
          <image :src="rizhi" class="suoimg" mode="aspectFill"></image>
        </view>
        <view class="label">操作日志</view>
      </view>
      <view class="li" @tap="unbindHandel">
        <view class="img">
          <image :src="unbind" class="suoimg" mode="aspectFill"></image>
        </view>
        <view class="label">解绑门锁</view>
      </view>
      <view
        class="li"
        @tap="unQbindHandel"
        v-if="!keyInfo || !keyInfo?.electricQuantity"
      >
        <view class="img">
          <image :src="unbind" class="suoimg" mode="aspectFill"></image>
        </view>
        <view class="label">强制解绑</view>
      </view>
    </view>
  </view>
</template>

<style lang="less">
page {
  background-color: #f7f9ff;
}
.lock-detail-container {
  > .title {
    display: flex;
    align-items: center;
    padding: 0 32px;
    height: 88px;
    > .tit {
      font-size: 32px;
      font-weight: 500;
      color: #333333;
    }
    > .tag {
      font-size: 24px;
      font-weight: 400;
      color: #fff;
      padding: 4px 16px;
      background: #f5f5f5;
      background-image: url("https://yimits.oss-cn-beijing.aliyuncs.com/picss/tag.png");
      background-repeat: no-repeat;
      background-size: 100% 100%;
      margin-left: 10px;
    }
  }
  ._name {
    color: #bdbdbd;
    font-size: 30px;
    text-align: center;
    transform: translateY(20px);
    // margin-top: 20px;
  }
  .lock-box {
    position: relative;
    width: 675px;
    height: 772px;
    background: #ffffff;
    box-shadow: 0px 2px 51px 0px rgba(25, 69, 169, 0.08);
    border-radius: 30px;
    margin: 0 auto;
    margin-top: 20px;
    .dianb-box {
      position: absolute;
      right: 40px;
      top: 30px;
      display: flex;
      width: 80px;
      align-items: center;
      .con {
        position: absolute;
        top: 3px;
        left: 1px;
        width: 85%;
        height: 19px;
      }
      .box {
        position: relative;
        width: 38px;
        height: 21px;
        background-image: url("https://yimits.oss-cn-beijing.aliyuncs.com/picss/dian-box.png");
        background-repeat: no-repeat;
        background-size: 100% 100%;
        margin: 0 auto;
        flex-shrink: 0;
      }
      .txt {
        font-size: 24px;
        font-weight: 500;
      }
    }
  }
  .content {
    position: relative;
    background: url("https://yimits.oss-cn-beijing.aliyuncs.com/picss/bottom-bg.png")
      no-repeat;
    background-size: 538px 538px;
    background-position: center 100px;
    height: 100%;
    margin-top: -20px;
    padding-top: 30px;
    overflow: hidden;
    .bottom {
      position: absolute;
      left: 50%;
      bottom: -30px;
      transform: translateX(-50%);
      text-align: center;
      .dian {
        font-weight: 500;
        font-size: 24px;
        color: #000000;
        text {
          color: #196ffc;
        }
      }
      .desc {
        font-weight: 500;
        font-size: 24px;
        color: #9a9a9a;
        margin-top: 10px;
      }
    }
    .dianimg {
      width: 671px;
      height: 188px;
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
    }
    .routeimg {
      width: 538px;
      height: 538px;
      position: absolute;
      left: 50%;
      top: 100px;
      transform: translateX(-50%);
      &.route {
        animation: route 5s linear infinite;
      }
      &.paused {
        animation-play-state: paused;
      }
      @keyframes route {
        0% {
          transform: translateX(-50%) rotate(0deg);
        }
        100% {
          transform: translateX(-50%) rotate(360deg);
        }
      }
    }
    .yuanimg {
      width: 308px;
      height: 308px;
      position: absolute;
      left: 50%;
      top: 215px;
      transform: translateX(-50%);
    }
    .suo {
      display: flex;
      flex-direction: column;
      align-items: center;
      position: absolute;
      left: 50%;
      top: 320px;
      transform: translateX(-50%);
      font-size: 18px;
      .suoimg {
        width: 48px;
        height: 59px;
        margin-bottom: 10px;
      }
    }
  }
  .utils-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 45px;
    padding: 0 45px;
    box-sizing: border-box;
    .li {
      text-align: center;
      .img {
        width: 133px;
        height: 133px;
        background: #ffffff;
        box-shadow: 0px 2px 10px 0px rgba(25, 69, 169, 0.08);
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        image {
          width: 50px;
          height: 50px;
        }
      }
      .label {
        margin-top: 10px;
      }
    }
  }
}
</style>
