<script setup>
import MyIcon from "@/components/MyIcon";
import request from "@/utils/request";
import Taro, { useDidShow, useLoad } from "@tarojs/taro";
import { onMounted, ref, unref } from "vue";

const query = ref();
const keyboardPwd = ref();

const eysclose = "https://yimits.oss-cn-beijing.aliyuncs.com/picss/eys-close.png";
const eysopen = "https://yimits.oss-cn-beijing.aliyuncs.com/picss/eys-open.png";

useLoad((q) => {
  console.log("load");
  query.value = JSON.parse(q.item);
  console.log(query.value);
  keyboardPwd.value = query.value.keyboardPwd;
});

const isOpen = ref(false);

const clickEysHandel = () => {
  console.log("clickEysHandel");
  isOpen.value = !isOpen.value;
};

const clickEditHandel = () => {
  console.log("clickEditHandel");
  Taro.navigateTo({
    url: `/pages/house/lockPwdEdit/lockPwdEdit?item=${JSON.stringify(query.value)}`,
  });
};
</script>

<template>
  <view class="pwd-admin-lock">
    <view class="cell">
      <view class="tit">租客密码</view>
      <view class="ipt">
        <input :key="isOpen" :type="isOpen ? 'text' : 'password'" v-model="keyboardPwd" />
        <image
          :src="isOpen ? eysclose : eysopen"
          mode="aspectFit"
          @tap="clickEysHandel"
        ></image>
      </view>
    </view>

    <view class="btn">
      <button @tap="clickEditHandel">密码修改</button>
    </view>
  </view>
</template>

<style lang="less">
page {
  background-color: #f1f3f7;
}
.pwd-admin-lock {
  .cell {
    height: 200px;
    width: 100%;
    background-color: #fff;
    box-sizing: border-box;
    padding: 30px 40px;
    .tit {
      font-size: 30px;
      color: #939393;
      margin-bottom: 50px;
    }
    .ipt {
      display: flex;
      justify-content: space-between;
      align-items: center;
      input {
        font-size: 30px;
      }
    }
    image {
      width: 41px;
      height: 28px;
    }
  }
  .btn {
    width: 100%;
    min-height: 150px;
    background-color: #fff;
    position: absolute;
    bottom: 0;
    left: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    // 设置ios刘海屏底部横线安全区域
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
    button {
      width: 700px;
      height: 88px;
      background: #ffffff;
      border-radius: 20px;
      border: 2px solid #1352fd;
      color: #1352fd;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0px -4px 32px 0px rgba(54, 69, 193, 0.24);
    }
  }
}
</style>
