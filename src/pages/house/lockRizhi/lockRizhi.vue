<script setup>
import MyIcon from "@/components/MyIcon";
import request from "@/utils/request";
import Taro, {
  useDidShow,
  useLoad,
  useReachBottom,
  usePullDownRefresh,
} from "@tarojs/taro";
import { onMounted, reactive, ref, unref } from "vue";
import {
  getCloudLog,
  getUnlockStatusMessage,
  modifyPwdHandel,
  toReadRecord,
} from "@/utils/lock";
import { useGlobalStore } from "@/stores";
import AtCalendar from "taro-ui-vue3/lib/calendar/index";
import YModal from "@/components/YModal/index.vue";

const globalStore = useGlobalStore();

const query = ref();
const keyboardPwd = ref("");
const keyboardPwdCofirm = ref("");

const showRiLiModal = ref(false);

const eysclose = "https://yimits.oss-cn-beijing.aliyuncs.com/picss/eys-close.png";
const eysopen = "https://yimits.oss-cn-beijing.aliyuncs.com/picss/eys-open.png";
const tip = "https://yimits.oss-cn-beijing.aliyuncs.com/picss/tip.png";
const tip2 = "https://yimits.oss-cn-beijing.aliyuncs.com/picss/tip-b.png";

const rili = "https://yimits.oss-cn-beijing.aliyuncs.com/picss/rili.png";
const xia = "https://yimits.oss-cn-beijing.aliyuncs.com/picss/xia.png";
import { getLockList } from "@/utils/lock";

function getCurrentDate(flag, dateNum = 0) {
  const date = new Date();
  if (dateNum) {
    date.setDate(date.getDate() + dateNum);
  }
  const year = date.getFullYear();
  const month = ("0" + (date.getMonth() + 1)).slice(-2); // 月份从 0 开始，需要加 1，并补零
  const day = ("0" + date.getDate()).slice(-2); // 日期补零
  if (flag) {
    return `${year}-${month}-${day}`;
  } else {
    return `${year}/${month}/${day}`;
  }
}

function formatTimestamp(timestamp) {
  const date = new Date(timestamp);

  const year = date.getFullYear();
  const month = ("0" + (date.getMonth() + 1)).slice(-2);
  const day = ("0" + date.getDate()).slice(-2);
  const hours = ("0" + date.getHours()).slice(-2);
  const minutes = ("0" + date.getMinutes()).slice(-2);
  const seconds = ("0" + date.getSeconds()).slice(-2);

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

const curentDate = reactive({
  start: getCurrentDate(true, -30),
  end: getCurrentDate(true, 1),
});

const keyValue = ref(new Date().getTime());

const keyInfo = ref(null); //锁信息

const lockId = ref(); //暂时模拟

const logList = ref([]);

const pageSize = ref(20);

const pageNo = ref(1);

const total = ref(0);

const date = ref(new Date(curentDate.end));

useLoad((q) => {
  console.log("load");
  lockId.value = q.lockId;
  init();
});

usePullDownRefresh(() => {
  Taro.showLoading();
  toReadRecord({ keyInfo: keyInfo.value });
  init().then(() => {
    Taro.stopPullDownRefresh();
    Taro.hideLoading();
  });
});

useReachBottom(() => {
  console.log("reachBottom");

  if (logList.value.length >= total.value) {
    Taro.showToast({
      title: "没有更多数据了",
      icon: "none",
    });
    return;
  }

  pageNo.value += 1;
  getLogLs(true);
});

const getLogLs = (isPush) => {
  getCloudLog({
    keyInfo: keyInfo.value,
    pageNo: pageNo.value,
    pageSize: pageSize.value,
    startDate: new Date(curentDate.start.replaceAll("/", "-")).getTime(),
    endDate: new Date(curentDate.end.replaceAll("/", "-")).getTime(),
  }).then((res) => {
    console.log(res,"reslog");
    if (isPush) {
      logList.value = [...logList.value, ...res.list];
    } else {
      logList.value = res.list;
      total.value = res.total;
    }
  });
};

const init = () => {
  return new Promise((resolve, reject) => {
    getLockList().then(async (res) => {
      console.log(res);
      // 查找id
      const index = res.list.findIndex((item) => item.lockId == lockId.value);
      keyInfo.value = res.list[index];
      console.log("keyInfo.value",keyInfo.value);
      resolve();
      getLogLs(false);
    });
  });
};

const onSelectDate = (value) => {
  console.log(value.value);
  if (value.value.start) {
    curentDate.start = value.value.start.replaceAll("-", "/");
  }
  if (value.value.end) {
    keyValue.value = new Date().getTime();
    curentDate.end = value.value.end.replaceAll("-", "/");
    date.value = new Date(curentDate.end);
    date.value.setDate(date.value.getDate() + 1);
  }
};

const onDayClick = (value) => {
  console.log(value);
};

date.value.setDate(date.value.getDate() + 1);
const xiaDateHandel = () => {
  date.value.setDate(date.value.getDate() + 1);

  curentDate.end = date.value.toISOString().split("T")[0].replaceAll("-", "/");

  keyValue.value = new Date().getTime();

  console.log(date.value.toISOString().split("T")[0].replaceAll("-", "/"));
  pageNo.value = 1;
  getLogLs(false);
};

const yesHandel = () => {
  console.log("yesHandel");
  showRiLiModal.value = false;
  pageNo.value = 1;
  getLogLs(false);
};

const transName = (name, item) => {
  if(!name) return
  const arr = name.split("-");
  const arr2 = name.split("_");
  // console.log(arr2);
  if (arr[0] == "undefined") {
    if (item.success == 1) {
      return "管理员密码开锁";
    } else {
      return "未知";
    }
  } else if (arr2.length && arr2[1] && arr2[1].includes("shanzupo")) {
    if (arr2[1].includes("tenant")) {
      return `${arr2[1].split("shanzupo")[0]}-租客`;
    } else {
      return `${arr2[1].split("shanzupo")[0]}-房东`;
    }
  } else if (name.includes("***")) {
    // return `管理员密码开锁-初始密码`;
    if (item.success == 1) {
      return "管理员密码开锁";
    } else {
      return "未知";
    }
  } else {
    return name;
  }
};

// 密码加密三位数
const transPwd = (pwd) => {
  if (!pwd) return;
  if (pwd.length) return `密码: (${pwd})`;
  // return `(${pwd.substring(0, 3) + "***"})`;
  return "";
};
</script>

<template>
  <view class="rizhi-container">
    <view class="title-box">
      <image :src="rili" mode="aspectFit" @tap="showRiLiModal = true"></image>
      <view class="box" @tap="showRiLiModal = true">
        <view class="date">{{ curentDate.start.replaceAll("/", "-") }}</view>
        <view class="txt">至</view>
        <view class="date">{{ curentDate.end.replaceAll("/", "-") }}</view>
      </view>
      <image :src="xia" mode="aspectFit" @tap="xiaDateHandel"></image>
    </view>
    <!-- l;ist -->
    <view v-if="!logList.length" class="no-data">暂无数据~</view>
    <view class="list">
      <view class="item" v-for="(item, idx) in logList" :key="idx">
        <view class="left">
          <view class="name"
            >{{ transName(item.username, item) || "未知" }}
          </view>
          <view class="time">{{ formatTimestamp(item.lockDate) }}</view>
        </view>
        <view class="right">
          <view
            class="txt"
            :style="{ color: item.success ? '#1b9c35' : '#ff0000' }"
          >
            <view>
              {{ getUnlockStatusMessage(item.recordTypeFromLock) }}
            </view>
            <view class="pwd"> {{ transPwd(item.keyboardPwd) }}</view>
          </view>
        </view>
      </view>
    </view>
    <!-- rili -->

    <YModal
      title="no"
      confirmText="no"
      :show="showRiLiModal"
      @close="showRiLiModal = false"
      showCancel
      maskClose
    >
      <template #content>
        <view class="content-rili-modal">
          <at-calendar
            isMultiSelect
            :currentDate="curentDate"
            :onSelectDate="onSelectDate"
            @onDayClick="onDayClick"
            :key="keyValue"
          />
          <view class="utils-box">
            <view class="left">
              <view class="da">开始时间日期 {{ curentDate.start }}</view>
              <view class="da">结束时间日期 {{ curentDate.end }}</view>
            </view>
            <view class="right">
              <view class="cancel" @tap="showRiLiModal = false">取消</view>
              <view @tap="yesHandel">确定</view>
            </view>
          </view>
        </view>
      </template>
    </YModal>
  </view>
</template>

<style lang="scss">
@import "taro-ui-vue3/dist/style/components/calendar.scss";
.at-calendar__list.flex {
  .flex__item--selected-head {
    &.flex__item--selected-tail {
      .flex__item-container {
        background-color: #1f86fd !important;
      }
    }
  }
}

.at-calendar__list.flex {
  .flex__item--selected {
    background-color: #1f86fd !important;
  }
}
page {
  background-color: #f1f3f7;
}
.content-rili-modal {
  padding-top: 20px;
  .utils-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #fff;
    margin-top: 30px;
    border-radius: 10rpx;
    .left {
      display: flex;
      align-items: center;
      flex-direction: column;
      flex-shrink: 0;
      width: 60%;
      .da {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 22rpx;
        font-weight: 500;
        color: #333333;
        border-radius: 22px;
        margin-right: 20rpx;
        margin-top: 10px;
      }
    }
    .right {
      display: flex;
      align-items: center;
      view {
        font-size: 28rpx;
        font-weight: 500;
        color: #4365f5;
        margin-right: 20rpx;
      }
      .cancel {
        color: #999;
      }
    }
  }
}

.rizhi-container {
  .no-data {
    text-align: center;
    margin-top: 30px;
    color: #333;
  }
  .title-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20rpx 30rpx;
    background-color: #fff;
    border-radius: 10rpx;
    height: 101px;
    .box {
      display: flex;
      align-items: center;
    }
    image {
      width: 40rpx;
      height: 40rpx;
    }
    .date {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 28rpx;
      font-weight: 500;
      color: #333333;
      width: 191px;
      height: 44px;
      background: #f1f2f7;
      border-radius: 22px;
    }
    .txt {
      font-size: 24rpx;
      font-weight: 400;
      color: #999999;
      margin: 0 20rpx;
    }
  }

  .list {
    margin-top: 20rpx;
    .item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 30rpx;
      background-color: #fff;
      border-radius: 10rpx;
      margin-bottom: 5rpx;
      .left {
        .name {
          font-size: 30rpx;
          font-weight: 500;
          color: #333333;
        }
        .time {
          font-size: 26rpx;
          font-weight: 400;
          color: #999999;
          margin-top: 10rpx;
        }
      }
      .right {
        .txt {
          text-align: right;
          font-size: 28rpx;
          font-weight: 500;
          color: #4365f5;
          display: flex;
          justify-content: flex-end;
          flex-direction: column;
          .pwd {
            color: #999;
            font-size: 24rpx;
          }
        }
      }
    }
  }
}
</style>
