page {
  background-color: #fff;
}
.bind-container {
  padding: 40px;
}
.water-bind-container {
  padding: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  .water-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #f0f4ff;
    border-radius: 10px;
    width: 150px;
    padding: 0 10px;
    font-size: 30px;
    image {
      width: calc(43px * 1.5);
      height: calc(76px * 1.5);
    }
  }
}
.home-business {
  // padding-bottom: 50px;
  // height: 87vh;
  // overflow: scroll;
  .scrollview {
    /* 滚动条样式 */
    ::-webkit-scrollbar {
      width: 0px; /* 滚动条宽度 */
    }

    /* 滚动条轨道颜色 */
    ::-webkit-scrollbar-track {
      background-color: transparent; /* 透明背景 */
    }

    /* 滚动条滑块颜色 */
    ::-webkit-scrollbar-thumb {
      background-color: rgba(0, 0, 0, 0); /* 半透明黑色滑块 */
      // border-radius: 10px; /* 圆角滑块 */
    }
  }
  .my-search-input {
    position: relative;
    z-index: 0;
  }
  padding-bottom: 120px;
  .header {
    // background: url('../../assets/images/bg-house.png') no-repeat bottom right;
    // background-size: 476px 363px;
    background-color: #1452fd;
    height: 490px;

    .message-ico {
      position: absolute;
      left: 20px;
      color: #fff;
      font-size: 45px;
      transform: translateY(-20px);
      &.active {
        &::after {
          content: "";
          position: absolute;
          top: 0;
          right: 0;
          background-color: red;
          width: 10px;
          height: 10px;
          border-radius: 50%;
        }
      }
    }

    .logo {
      position: relative;
      padding-top: 100px;
      text-align: center;

      .img {
        width: 174px;
        height: 67px;
      }
    }
    .stat {
      margin-top: 64px;
      font-size: 26px;
      color: #ffffff;

      .num {
        font-size: 48px;
        font-weight: 400;
        font-family: "Bahnschrift-Regular";
      }
      .digit {
        font-family: "Bahnschrift-Regular";
        font-weight: 400;
      }
    }

    // ----------
    .uls {
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      margin-top: 20px;
      .border-b {
        width: 1px;
        height: 60px;
        opacity: 0.8;
        margin: 0 32px;
        background-color: #fff;
      }
      > view {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;

        .num {
          font-size: 44px;
          color: #ffffff;
        }
        .label {
          font-size: 24px;
          margin-top: 4px;
        }
      }
    }
  }
  .deveice-box {
    padding-bottom: 200px;
    .emty {
      text-align: center;
      line-height: 50px;
      color: #b6bec5;
      margin-top: 100px;
    }
    .status-filter {
      display: flex;
      justify-content: space-between;
      align-items: center;
      // padding: 0 20px;
      margin-top: 10px;

      .tag {
        height: 43px;
        background: #ebefff;
        border-radius: 21px;
        border: 1px solid #aebbef;
        text-align: center;
        line-height: 43px;
        opacity: 0.9;
        font-size: 24px;
        padding: 0 15px;
        flex-shrink: 0;
        margin-right: 25px;
      }
    }
  }
  .header-bg {
    background-color: #1452fd;
    height: 100px;
  }
  .main {
    position: relative;
    padding-top: 20px;
    border-top-left-radius: 35px;
    border-top-right-radius: 35px;
    margin-top: -260px;
    background-color: #fff;
  }
  .box {
    position: absolute;
    top: -100px;
    width: 100%;
    box-sizing: border-box;
    .box-inner {
      border-radius: 40px 40px 0 0;
      background-color: #ffffff;
      padding: 31px 31px 0 31px;
      .house-stat {
        background: #1f5bfd;
        box-shadow: 0 4px 32px 0 rgba(54, 69, 193, 0.24);
        border-radius: 30px;
        padding: 30px;

        .house-stat-item {
          width: 25%;
        }
        .house-stat-num {
          font-size: 48px;
          color: #ffffff;
        }
        .house-stat-txt {
          color: #afbcff;
          font-size: 24px;
        }
      }
    }
  }
  .box2 {
    padding: 7px 31px 8px 31px;
    .enter-chart {
      background-color: #ffe9e7;
      border-radius: 52px;
      padding: 16px 17px;
      width: 228px;
      font-size: 26px;
    }
    .enter-help {
      background-color: #ebefff;
      border-radius: 52px;
      padding: 8px 23px 6px 10px;
      width: 100%;
      font-size: 34px;
      margin-left: 12px;
      font-weight: 700;
    }
    .icon-chart {
      width: 72px;
      height: 72px;
      margin-right: 11px;
    }
    .icon-help {
      width: 90px;
      height: 90px;
    }
    .icon-arrow1 {
      width: 44px;
      height: 44px;
    }
  }
  .menu {
    padding: 0 31px 31px 31px;
    gap: 35px;
    .menu-item {
      text-align: center;
      width: 200px;

      .menu-icon {
        padding: 29px 0 26px 0;
        border-radius: 15px;
        margin-bottom: 19px;
      }
      .menu-img {
        width: 72px;
        height: 72px;
      }
    }
    .menu1 {
      background-color: #ebefff;
    }
    .menu2 {
      background-color: #ffe9e7;
    }
    .menu3 {
      background-color: #fff4ea;
    }
    .menu4 {
      background-color: #f8efff;
    }
    .menu5 {
      background-color: #fff4ea;
    }
    .menu6 {
      background-color: #f8efff;
    }
  }
  .btn-device {
    position: fixed;
    bottom: calc(env(safe-area-inset-bottom) + 120px);
    left: 0;
    width: 100%;
    height: 102px;
    background: #1f5bfd;
    border: 2px solid #1f5bfd;
    border-radius: 0px;
    // margin: 0 31px;
    text-align: center;
    color: #ffffff;
    font-size: 28px;
    line-height: 102px;
    z-index: 2;
  }
  .icon-scan {
    width: 68px;
    height: 68px;
    vertical-align: middle;
    margin-right: 10px;
  }
}

.home-tenant {
  .btn-device {
    position: fixed;
    bottom: calc(env(safe-area-inset-bottom) + 120px);
    left: 0;
    width: 100%;
    height: 102px;
    background: #1f5bfd;
    border: 2px solid #1f5bfd;
    border-radius: 0px;
    // margin: 0 31px;
    text-align: center;
    color: #ffffff;
    font-size: 28px;
    line-height: 102px;
    z-index: 2;
  }
  .device-list2.tenant-item {
    padding: 0;
      .device-item {
        margin-top: 0;
      }
  }
  // padding-bottom: 50px;
  .header {
    height: 685px;
    background: linear-gradient(180deg, #1452fd 63%, #ffffff 100%);
    position: relative;

    .bg-fangjian {
      right: 62px;
      top: 207px;
      position: absolute;
    }
    .bg-house {
      position: absolute;
      left: 0;
      top: 110px;
    }

    .header-title {
      position: absolute;
      top: 240px;
      left: 58px;
      color: #ffffff;

      .tt1 {
        font-size: 42px;
        font-family: OPPOSans;
        font-weight: 500;
        color: #ffffff;
        margin-bottom: 21px;
      }
      .tt2 {
        font-size: 30px;
        font-family: OPPOSans;
        font-weight: 400;
        color: #ffffff;
      }
    }

    .more-house {
      width: 127px;
      height: 63px;
      background: #f0f5fe;
      border-radius: 32px 0 0 32px;
      font-size: 30px;
      font-weight: 500;
      color: #1452fd;
      line-height: 63px;
      padding: 0 51px 0 28px;
      position: absolute;
      right: 0;
      top: 240px;
    }
  }
  .main {
    position: relative;
    padding-top: 201px;
    background-color: #ffffff;
  }
  .my-house {
    position: absolute;
    top: -261px;
    left: 30px;
    width: 690px;
    padding-top: 33px;

    background: url("https://yimits.oss-cn-beijing.aliyuncs.com/images/bg-tenant-top.png") no-repeat 31px top;
    background-size: contain;

    .my-house-box {
      background-color: #ffffff;
      box-shadow: 0 7px 29px 0 rgba(112, 145, 178, 0.2);
      border-radius: 25px;
      // height: 384px;
      min-height: 342px;

      .my-house-empty {
        text-align: center;
        padding-top: 143px;
      }
    }
    .my-house-full {
      padding-top: 40px;
    }
    .my-house-name {
      font-size: 36px;
      font-family: OPPOSans;
      font-weight: bold;
      color: #000000;
      padding-left: 44px;
    }
    .my-house-days {
      font-size: 28px;
      font-family: OPPOSans;
      font-weight: 500;
      color: #000000;
      margin-top: 17px;
      padding-left: 44px;
    }

    .my-house-stat {
      margin: 20px 27px;
      border-top: 1px solid #dfdfdf;
      padding: 20px 0 20px 0;
      position: relative;
    }
    .more-device {
      position: absolute;
      top: -70px;
      right: 0;
    }
    .stat-num {
      font-size: 100px;
      font-family: Bahnschrift;
      font-weight: 400;
      color: #000000;
    }
    .stat-txt {
      font-size: 26px;
      font-family: OPPOSans;
      font-weight: 400;
      color: #a8b1ca;
    }
  }

  .menu {
    padding: 0 31px 31px 31px;
    gap: 35px;
    .menu-item {
      text-align: center;
      width: 200px;
      font-size: 30px;
      position: relative;

      .menu-icon {
        padding: 21px 0 21px 0;
        border-radius: 15px;
        margin-bottom: 19px;
      }
      .menu-img {
        width: 69px;
        height: 69px;
      }
      .badge {
        position: absolute;
        top: -10px;
        right: -10px;
        width: 40px;
        height: 40px;
        font-size: 32px;
        text-align: center;
        line-height: 40px;
        background-color: #ff0000;
        color: #ffffff;
        border-radius: 100px;
        z-index: 1;
      }
    }
    .menu1 {
      background-color: #ebefff;
    }
    .menu2 {
      background-color: #ffe9e7;
    }
    .menu3 {
      background-color: #fff4ea;
    }
    .menu4 {
      background-color: #f8efff;
    }
    .menu5 {
      background-color: #fff4ea;
    }
    .menu6 {
      background-color: #f8efff;
    }
  }

  .my-bill {
    background-color: #f1f2f3;
    padding: 31px;
    padding-bottom: 120px;

    .bill-title {
      font-size: 36rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #000000;
      padding: 10px 0 23px 0;
    }
    .bill-item {
      background-color: #ffffff;
      border-radius: 8px;
      padding: 32px 27px;
      position: relative;
      line-height: 180%;
      margin-bottom: 21px;
      font-size: 16px;
    }
    .bill-name {
      font-size: 36rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #000000;
    }
    .bill-house {
      font-size: 26rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #a8b1ca;
    }
    .bill-amount {
      font-size: 48rpx;
      font-family: Bahnschrift;
      font-weight: normal;
      color: #000000;
      .cny {
        font-size: 30px;
      }
    }
    .bill-pay-date {
      font-size: 26rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #a8b1ca;
    }
    .bill-con {
      padding: 37px 0;
      border-bottom: 1px solid #dfdfdf;
    }
    .bill-btm {
      padding: 18px 0 21px 0;
    }
  }
}
.b-pay-amount {
  font-size: 72px;
  font-weight: 400;
  color: #000000;
  .cny {
    font-size: 30px;
  }
}
.b-pay-tips {
  height: 79px;
  background: #e9f1fe;
  text-align: center;
  line-height: 79px;
  font-size: 26px;
  font-weight: 500;
  color: #1352fd;
}
.b-pay-fee {
  text-align: center;
  font-size: 24px;
  font-weight: 500;
  color: #b6bec5;
  line-height: 31px;
}

.emty-message-container {
  text-align: center;
  .qrcode {
    text-align: center;
    margin: 30px 0;
    margin-top: 45px;
    image {
      width: 180px;
      height: 180px;
    }
  }
  .text-b {
    font-size: 30px;
    margin-bottom: 80px;
  }
}
.input-custom {
  padding: 15px;
  border-radius: 10px;
  font-size: 27px;
  height: 100px;
}
