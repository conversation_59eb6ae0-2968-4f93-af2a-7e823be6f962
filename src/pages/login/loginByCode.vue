<template>
  <view class="container">
    <view class="login-title">
      <view class="login-t1">请输入手机号</view>
      <view class="login-t2">为方便取得联系，请输入您的常用手机号码</view>
    </view>
    <view class="login-item">
      <view class="li-t">请输入手机号</view>
      <view class="li-c">
        <view class="li-c-prefix">+86</view>
        <view><input :cursor="posCur" @input="bindReplaceInput" type="number" v-model="formState.mobile" class="login-input" :style="isAp ? 'height:96%' : ''" /></view>
      </view>
    </view>
    <view class="login-item">
      <view class="li-t">请输入验证码</view>
      <view class="li-c">
        <view class="li-c-prefix">验证码</view>
        <view class="li-c-ipt"><input :cursor="posCur" @input="bindReplaceInput" type="number" v-model="formState.code" class="login-input" :style="isAp ? 'height:96%' : ''" /></view>
        <view class="li-c-suffix">
          <text class="btn-text-small" v-if="!isSend" @tap="getSmsCode">获取验证码</text>
          <text class="color-low" v-if="isSend">{{timeout}}秒后重发</text>
        </view>
      </view>
    </view>
    <view class="login-btn">
      <button class="btn-primary" @tap="handleLogin">登录</button>
    </view>
    <view class="text-right link-other" @tap="onChangeLogin">
      密码登录
    </view>
  </view>
</template>
<script setup>
import { ref } from "vue";
import request from "@/utils/request";
import Taro, {
  useDidShow,
  useDidHide,
  useReady,
  useLoad,
  usePullDownRefresh,
  useReachBottom,
} from "@tarojs/taro";

import MyCell from '@/components/MyCell'
import {useGlobalStore} from "@/stores";
import {validation} from "@/utils/validation"

definePageConfig({
  navigationBarTitleText: "验证码登录",
  navigationBarBackgroundColor: "#FFFFFF",
  //navigationBarTextStyle: "white",
});
const globalStore = useGlobalStore()

const isAp = process.env.TARO_ENV === "alipay";

const posCur = ref(-1)

const bindReplaceInput = (e) => {
  let pos = e.detail.cursor
  posCur.value = pos
}


const formState = ref({
  mobile: '',
  code: '',
  who: globalStore.who
})

const isSend = ref(false)
const timeout = ref(60)

const getSmsCode = () => {
  if (!formState.value.mobile) {
    Taro.showToast({
      title: '请输入手机号码',
      icon: 'none'
    })
    return
  }
  request.post({
    url: 'sendSmsCode',
    data: {
      mobile: formState.value.mobile
    }
  }).then(res => {
    isSend.value = true
    timeoutCountdown()
  })
}

const timeoutCountdown = () => {
  setTimeout(() => {
    timeout.value = timeout.value - 1

    if (timeout.value > 0) {
      timeoutCountdown()
    } else {
      isSend.value = false
      timeout.value = 60
    }
  }, 1000)
}

const handleLogin = () => {

  validation(formState.value, {
    mobile: {
      type: 'required',
      message: '请输入手机号码',
    },
    code: {
      type: 'required',
      message: '请输入短信验证码'
    }
  }).then(() => {
    //发起网络请求
    request.post({
      url: 'user/login',
      data: formState.value,
    }).then((res) => {
      Taro.setStorageSync('token', res.data.token)
      globalStore.setUserInfo(res.data)
      globalStore.homeHasChange = true
      // 判断缓存是否有设备 有设备去绑定
        // 接口存进登录态
        if(!globalStore.tempDeviceList[globalStore.tempDeviceList.length - 1]?.sn) return updateOpenId()
        request.get({
            url: 'user/bindDevice',
            data: {
              sn:globalStore.tempDeviceList[globalStore.tempDeviceList.length - 1]?.sn
            }
          }).then((_res) => {
          }).finally(() => {
            updateOpenId()
          })
    })
  }).catch((err) => {
    Taro.showToast({
      title: err,
      icon: 'error'
    })
  })

}

const updateOpenId = () => {
  if (process.env.TARO_ENV === 'weapp') {
    wx.login({
      success(res) {
        if (res.code) {
          request.post({
            url: 'user/updateOpenId',
            data: {
              code: res.code
            }
          }).then(_ => {
            Taro.switchTab({
              url: '/pages/index/index'
            })
          })
        }
      },
      fail() {
        Taro.showToast({
          title: '获取openid出错',
          icon: 'error'
        })
      }
    })
  } else {
    my.getAuthCode({
      scopes: 'auth_base',
      success: res => {
        // 在服务端获取用户信息
        if (res.authCode) {
          request.post({
            url: 'user/updateAlipayId',
            data: {
              code: res.authCode
            }
          }).then(_ => {
            Taro.switchTab({
              url: '/pages/index/index'
            })
          })
        }
      },
      fail: err => {
        console.log('my.getAuthCode 调用失败', err)
      }
    });
  }
}

const onChangeLogin = () => {
  Taro.redirectTo({
    url: '/pages/login/login'
  })
}

</script>
<style lang="scss">
.login-title {
  margin: 68px 37px 127px 37px;
}
.login-t1 {
  font-size: 38px;
  font-weight: 500;
  color: #000000;
}
.login-t2 {
  font-size: 26px;
  font-weight: 500;
  color: #989898;
}
.login-item {
  margin: 43px 37px;
  .li-t {
    font-size: 22px;
    font-weight: 500;
    color: #989898;
  }
  .li-c {
    display: flex;
    border-bottom: 1px solid #B9B9B9;
    padding-bottom: 5px;
    padding-top: 45px;
  }
  .li-c-prefix {
    width: 120px;
    height: 53px;
    background: #EBEBEB;
    border-radius: 6px;
    text-align: center;
  }
  .li-c-ipt {
    flex: 1;
  }
  .li-c-suffix {
    width: 200px;
    text-align: right;
  }
  .login-input {
    border: none;
    font-size: 30px;
    font-weight: 500;
    color: #000000;
    height: 25px;
    padding-left: 10px;
  }
}
.login-btn {
  margin: 33px 37px ;
  text-align: center;
}
.link-other {
  font-size: 30px;
  font-weight: 500;
  color: #989898;
  margin: 0 37px;
}
</style>
