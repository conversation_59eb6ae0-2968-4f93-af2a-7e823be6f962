<template>
  <View class="container">
    <view class="message" :style="'padding-top: ' + (40 + globalStore.windowInfo.statusBarHeight) + 'px'">
      <view class="no-data" v-if="total === 0">
        <view class="mt20">暂无消息</view>
      </view>
      <view class="msg-item" v-for="item in items" :key="item.id">

        <view class="msg-title" :class="item.is_read === 0 ? 'is-new-msg' : ''">
          {{item.title}}
        </view>

        <view class="mt10">
        <rich-text :nodes="item.content"></rich-text>
        </view>
        <view class="mt20">{{item.created_at}}</view>
      </view>

    </view>
  </View>
</template>

<script setup>
  import { ref } from 'vue'
  import Taro, { useDidShow, useDidHide, useReady, useLoad, usePullDownRefresh, useReachBottom } from '@tarojs/taro'
  import request from '../../utils/request'
  import './message.scss'

  import { useGlobalStore } from '@/stores'
  const globalStore = useGlobalStore()

  const onChangeFilter = (child, key) => {
    console.log(child, key)
    params.value[key] = child.value
    fetch()
  }
  const onSearch = (keyword) => {
    params.value.keyword = keyword
    fetch()
  }


  /** ----------------------接口数据-begin----------------------------------- */
  const items = ref([])
  const total = ref(0)

  const params = ref({
    page: 1
  })

  const onChangeStatus = (val) => {
    params.value.status = val
    fetch()
  }

  const fetch = () => {
    items.value = []
    params.value.page = 1
    getList()
  }

  const getList = () => {
    request.get({
      url: 'message',
      data: {
        ... params.value
      }
    }).then(res => {
      if (items.value.length > 0) {
        items.value = [...items.value, ... res.data.items]
      } else {
        items.value = res.data.items
      }
      total.value = res.data.total
      if (res.data.currentPage >= res.data.lastPage) {
        isLastPage.value = true
      }
      if (params.value.page === 1) {
        handleRead()
      }
    })
  }

  const handleRead = () => {
    request.post({
      url: 'message/read',
      showLoading: false
    }).then(res => {
      Taro.removeTabBarBadge({
        index: 1
      })
    })
  }

  /** ----------------------接口数据-end----------------------------------- */

  useLoad((options) => {

  })

  useDidShow(() => {
    fetch()
  })

  usePullDownRefresh(() => {
    Taro.stopPullDownRefresh()
    fetch()
  })

  const isLastPage = ref(false)

  useReachBottom(() => {
    if (!isLastPage.value) {
      params.value.page += 1
      getList()
    }
  })


</script>
