<template>
  <view class="card" @tap.stop="toHandel">
    <view class="card-amount">
      <view
        ><text class="cny">￥</text
        ><text class="amount">{{ data.user.amount }}</text></view
      >
      <view class="amount-s">
        总金额 (含未到账
        <text class="pending-amount">¥{{ data.user.pending_amount }}</text> )
      </view>
    </view>
    <view
      class="card-bank flex flex-space-between"
      v-if="
        !data.account ||
        !data.account.account_info ||
        !data.account.account_info.card_id
      "
    >
      <view class="t1" v-if="accountInfo && accountInfo?.status != 2">{{ accountInfo?.reason }}(两个工作日内完成)</view>
      <view class="t1" v-else>暂无银行卡</view>
      <view v-if="!data?.account?.account_info?.card_id" class="btn-add-bank" @tap="addCardHandel"
        ><text style="margin-right: 8rpx;" class="text-v-center">{{ !accountInfo ? '去添加' : accountInfo?.status == 3 ? '去编辑' : '去添加' }}</text>
        <MyIcon
          icon="icon-arrow-right-mini"
          width="9rpx"
          height="15rpx"
        ></MyIcon
      ></view>
    </view>
    <view class="card-bank flex flex-space-between" style="align-items: center;" v-else>
      <view class="t1" v-if="accountInfo && accountInfo?.status == 2 && data.account?.account_info?.bank_acct_type == 1">{{ data.account.account_info?.card_name }}(企业)-{{ accountInfo?.bank_name }}</view>
      <view class="t1">{{ data.account.account_info.bank_name }}</view>
      <view class="btn-add-bank t1" @tap="onJump('/pages/money/cardInfo')">{{
        data.account.account_info.card_id
      }}</view>
    </view>
    <view class="btn-cash-log" @tap="onJump('/pages/money/cashLog')"
      >提现记录</view
    >
    <view class="btn-cash" @tap="onJumpJiaoyan('/pages/money/draw')">提现</view>
  </view>
  <!-- 企业 -->
  <view class="bank-card"  @click="toDetailHandel($event,1)" v-if="false">
			<view class="content" v-if="bankInfo?.bus_account?.card_no ">
				<!-- 有银行卡显示 -->
				<view class="tixian-btn" data-id="tixian" v-if="bankInfo?.bus_account.status == 2" @click="tixianHandel(1)">
					提现
				</view>
				<!-- 有银行卡显示 -->
				<view class="bottom">
					<view class="le">
						{{  bankInfo?.bus_account?.card_name  }}(企业)
					</view>
					<!-- 有银行卡显示 -->
					<view class="ri" >
						<text>{{ bankInfo?.bus_account.card_no   }}</text>
					</view>
				</view>
					<view class="reson">
						{{ bankInfo?.bus_account.reason  }}
					</view>
			</view>
			<view class="content2" data-id="tixian" v-else @tap="bingBinkHandel(1)">
				<text class="iconfont icon-jiahao1"></text>
				绑定企业账户
			</view>
		</view>
  <view class="footer-fixed">
    <view class="p20">
      <button class="btn-primary" @tap="onJumpJiaoyan('/pages/money/draw')">
        提现
      </button>
    </view>
    <MyPopup :show="showTip" title="绑卡提示" @close="onChangeShowTip">
      <template #content>
        <view class="p40 text-center">您还未绑卡,是否去绑定银行卡? </view>
        <view
          class="p20 flex flex-space-between flex-v-center"
          style="border-top: 1px solid #dfdfdf"
        >
          <view style="width: 28%" class="text-center" @tap="onChangeShowTip"
            >取消</view
          >
          <view style="width: 70%"
            ><button class="btn-primary" @tap="onBindCard">去绑卡</button></view
          >
        </view>
      </template>
    </MyPopup>
  </view>
</template>

<script setup>
import { ref } from "vue";
import request from "@/utils/request";
import myBottomBtn from "@/components/MyBottomBtn/MyBottomBtn";
import MyIcon from "@/components/MyIcon";
import { getApiRoot } from "@/config";
import MyPopup from "@/components/MyPopup";

import Taro, {
  useDidShow,
  useDidHide,
  useReady,
  useLoad,
  usePullDownRefresh,
  useReachBottom,
} from "@tarojs/taro";
definePageConfig({
  navigationBarTitleText: "银行卡管理",
  navigationBarBackgroundColor: "#F7F9FF",
});

if (process.env.TARO_ENV === 'alipay') {
  my.setNavigationBar({
    frontColor: '#ffffff',
    backgroundColor: '#1452fd',
  })
}

const data = ref({
  user: {},
  account: {},
});

usePullDownRefresh(() => {
  fetch();
  Taro.stopPullDownRefresh();
});

useDidShow(() => {
  fetch();
  getAcountHandel()
});

const fetch = () => {
  request
    .get({
      url: "account",
    })
    .then((res) => {
      data.value = res.data;
    });
};

const onJump = (url) => {
  Taro.navigateTo({
    url: url,
  });
};

const toHandel = (e) => {
  // if (accountInfo.value && accountInfo.value.status == 2) {
  //   onJump('/pages/money/cardInfo')
  // }
}

const onJumpJiaoyan = (url) => {
  if (
    data.value.account.account_info &&
    data.value.account.account_info.card_id
  ) {
    Taro.navigateTo({
      url: url,
    });
  } else {
    if (!data.value?.account?.account_info?.card_id) {
      // 没有银行卡信息
      onChangeShowTip();
    } else if(accountInfo.value && accountInfo.value.status != 2 && !data.value.account.account_info.card_id) {
      // 有对公信息 提示状态
      Taro.showToast({
        icon: 'none',
        title: accountInfo.value.reason,
        duration: 2000
      })

    }
  }
};

const onBindCard = () => {
  onChangeShowTip();
  onJump("/pages/money/bindCard");
};

const accountInfo = ref(null)

  // 获取对公信息
const getAcountHandel = async() => {
 const {data} =await request
    .get({
      url: "account/bankDetail",
    })
    if (data && data?.address) {
      accountInfo.value = data
    }
  }

  // 添加银行卡
const addCardHandel = () => {
    if (!accountInfo.value) {
      onJump('/pages/money/bindCard')
    } else {
      // onJump('/pages/money/bindCard?type=1')
      onJump('/pages/money/bindCard')
    }
  }


const showTip = ref(false);
const onChangeShowTip = () => {
  showTip.value = !showTip.value;
};

const bingBinkHandel = (type) => {
  onJump('/pages/money/bindCard?type='+type)
}
</script>

<style lang="scss">
page {
  background-color: #f7f9ff;
}
.card {
  position: relative;
  margin: 26px;
  border-radius: 25px;
  background: url("https://yimidb.oss-cn-hangzhou.aliyuncs.com/mp/images/bg-card.png")
    top right no-repeat;
  background-size: 455px;
  background-color: #1352fd;
  padding: 15px;

  .card-bank {
    border-top: 1px solid #3a6fff;
    color: #8eacff;
    font-size: 24px;
    padding: 28px 19px;
    min-width: 100px;
    flex-shrink: 0;
    .t1 {
      font-size: 30px;
    }
    .btn-add-bank {
      font-size: 25px;
      color: #ffffff;
    min-width: 100px;
    flex-shrink: 0;
    }
  }
  .card-amount {
    padding: 64px 0 40px 44px;
    color: #ffffff;
    .amount {
      font-size: 60px;
    }
    .cny {
      font-size: 24px;
    }
    .amount-s {
      color: #8eacff;
      font-size: 20px;
    }
  }

  .btn-cash-log {
    width: 137px;
    height: 67px;
    background: url("https://yimidb.oss-cn-hangzhou.aliyuncs.com/mp/images/bg-cash.png")
      center no-repeat;
    background-size: contain;
    font-size: 24px;
    color: #1352fd;
    line-height: 60px;
    text-align: center;
    position: absolute;
    top: 28px;
    right: -10px;
  }
  .btn-cash {
    font-size: 24px;
    color: #8eacff;
    border: 1px solid #8eacff;
    padding: 6px 19px;
    position: absolute;
    top: 121px;
    right: 35px;
  }
}
.bank-card {
			position: relative;
			margin: 0 auto;
			box-sizing: border-box;
			width: 698rpx;
			height: 279rpx;
			background: #1352FD;
			border-radius: 25rpx;
			margin-bottom: 30rpx;

			image {
				position: absolute;
				right: 0;
				top: 0;
				width: 455rpx;
				height: 187rpx;
			}
			.content2 {
				display: flex;
				justify-content: center;
				align-items: center;
				color: #FFFFFF;
				font-weight: 700;
				font-size: 40rpx;
				height: 100%;
			}

			.content {
				position: relative;
				color: #FFFFFF;
				.reson {
					text-align: right;
					margin-top: 100rpx;
					padding-right: 30rpx;
					font-weight: 700;
					font-size: 40rpx;
				}

				.tixian-btn {
					position: absolute;
					right: 30rpx;
					top: 130rpx;
					width: 79rpx;
					height: 39rpx;
					border: 1px solid #8EACFF;
					border-radius: 4rpx;
					text-align: center;
					line-height: 39rpx;
					font-size: 24rpx;
					color: #8EACFF;

					&:active {
						background-color: #0f43c9;
					}
				}

				.tag-box {
					position: absolute;
					right: -10rpx;
					top: 30rpx;

					.tag {
						width: 137rpx;
						height: 67rpx;

					}

					text {
						display: block;
						width: 150rpx;
						position: absolute;
						left: -110rpx;
						color: #1352FD;
						top: 15rpx;
						font-size: 24rpx;
						z-index: 9;
					}
				}


				.top {
					padding: 50rpx 30rpx;

					.price {
						font-size: 23rpx;

						.p {
							font-size: 60rpx;
							margin-left: 5rpx;
						}

					}

					.label {
						font-size: 20rpx;
						color: #8EACFF;

						text {
							color: #FFFFFF;
						}
					}
				}

				.bottom {
					display: flex;
					justify-content: space-between;
					border-top: 1px solid #3A6FFF;
					margin: 0 15rpx;
					padding: 15rpx;
          height: auto;

					.le {
						font-size: 24rpx;
						color: #8EACFF;
					}

					.ri {
						font-size: 28rpx;
						font-weight: 700;
						height: 50rpx;
						// width: 135rpx;
						text-align: center;
						line-height: 50rpx;

						.iconfont {
							font-size: 28rpx;
							margin-left: 5rpx;
						}
					}
				}
			}
		}
</style>
