<template>
  <view class="list">
    <view
      class="list-item p20"
      v-for="item in items"
      :key="item.id"
    >
<!--      <view class="">-->
<!--&lt;!&ndash;        <view>{{item.bank_name}} {{item.card_id}}</view>&ndash;&gt;-->
<!--        -->
<!--      </view>-->
      <view class="flex flex-space-between flex-v-center">
        <view>
          <view class="cash-log-amt">{{item.cash_amt}}</view>
          <view class="color-low">提现日期：{{item.created_at}}</view>
          <view class="color-low">户名：{{item.card_name || '暂无'}}</view>
        </view>
        <view>
          <text v-if="item.status === 'P'" class="cl-s-p">处理中</text>
          <text v-if="item.status === 'S'" class="cl-s-s">提现成功</text>
          <text v-if="item.status === 'F'" class="cl-s-f">提现失败</text>
        </view>
      </view>
    </view>

    <view v-if="total === 0" class="p20 text-center color-low">暂无记录</view>

  </view>
</template>

<script setup>
import { ref } from "vue";
import request from "@/utils/request";
import Taro, {
  useDidShow,
  useDidHide,
  useReady,
  useLoad,
  usePullDownRefresh,
  useReachBottom,
} from "@tarojs/taro";
definePageConfig({
  navigationBarTitleText: "提现记录",
  navigationBarBackgroundColor: "#1352FD",
  navigationBarTextStyle: "white",
});

const id = ref(0)

useLoad((options) => {
  console.log(options, 'log options')
  params.value.action = options.action || 1
  id.value = options.id
  fetch();
})

useDidShow(() => {

});

useReachBottom(() => {
  if (total.value > items.value.length) {
    params.value.page += 1;
    getList();
  }
});

const items = ref([]);
const total = ref(-1);
const isEmpty = ref(false)
const params = ref({
  page: 1
});

const fetch = () => {
  items.value = [];
  params.value.page = 1;
  getList();
};
const getList = () => {
  request
    .get({
      url: "account/cashLog",
      data: {
        ...params.value,
      },
    })
    .then((res) => {
      items.value = [...items.value, ...res.data.items];
      total.value = res.data.total;
    });
};
</script>

<style lang="scss">
page {
  background: #f1f3f7;
}
.list {
  padding-bottom: 170px;
}

.list-item {
  background: #ffffff;
  border-radius: 14px;
  margin: 20px auto;
  padding-bottom: 38px;
  font-size: 28px;

  .cash-log-amt {
    font-size: 32px;
    color: #1352FD;
    padding-bottom: 20px;
  }
  .cl-s-p {
    color: #1352FD;
    font-size: 26px;
    font-weight: 500;
  }
  .cl-s-s {
    color: #000000;
    font-size: 26px;
    font-weight: 500;
  }
  .cl-s-f {
    color: #FF4200;
    font-size: 26px;
    font-weight: 500;
  }
}

</style>
