<template>
  <view class="card-info flex flex-v-center flex-space-between">
    <view>
      <view class="ci-bn">{{data.account.account_info && data.account.account_info.bank_name}}</view>
      <view class="ci-id">{{data.account.account_info && data.account.account_info.card_id}}</view>
    </view>
    <view><MyIcon icon="icon-arrow-right" width="15rpx" height="28rpx"></MyIcon></view>
  </view>
  <view class="dc-form">
    <view class="dc-top"><text class="dc-t">提现金额</text><text class="dc-tip">（两小时内到账，只可提现已到账金额）</text></view>
    <view class="dc-ipt flex flex-space-between flex-v-center">
      <view style="width: 60rpx">￥</view>
      <view ><input class="dc-input" v-model="formState.amount" type="digit" placeholder="0.00" /></view>
      <view><text class="btn-text" @tap="onDrawAll">全部提现</text></view>
    </view>
    <view class="dc-amt">
      <text class="dc-amt-t">可提现金额</text>
      <text class="dc-cny">￥</text>
      <text class="dc-amt-n">{{ data.user.amount }}</text>
    </view>
  </view>

  <view class="draw-cash-rule">
    <view class="draw-cash-rule-t">提现手续费规则: </view>
    <view class="draw-cash-rule-c">
      <view>受理时间及金额限制:</view>
      <view>
        <view>【提现时间】</view>
        <view>08:00 - 22:00</view>
        <view>【金额限制】</view>
        <view>提现笔数：3笔/日</view>
        <view>单笔限额：2万元</view>
        <view>当日限额：5万元</view>
      </view>
    </view>
  </view>


  <view class="footer-fixed">
    <view class="p20">
      <button class="btn-primary" @tap="onSubmit">确认提现</button>
    </view>
  </view>

  <YModal
    title="提示"
    confirmText="我知道了"
    :show="showTip"
    @close="showTip = false"
    @confirm="showTip = false"
    :showCancel="false"
    :maskClose="false"
    :showClose="false"
    >
      <template #content>
        <view class="content-toast-modal">
          {{ msgErro }}
        </view>
      </template>
    </YModal>

</template>

<script setup>
import { ref } from "vue";
import request from "@/utils/request";
import myBottomBtn from "@/components/MyBottomBtn/MyBottomBtn";
import MyIcon from "@/components/MyIcon"
import YModal from '@/components/YModal/index.vue'


import { getApiRoot } from "@/config";
import Taro, {
  useDidShow,
  useDidHide,
  useReady,
  useLoad,
  usePullDownRefresh,
  useReachBottom,
} from "@tarojs/taro";
definePageConfig({
  navigationBarTitleText: "提现",
  navigationBarBackgroundColor: "#F7F9FF",
});

if (process.env.TARO_ENV === 'alipay') {
  my.setNavigationBar({
    frontColor: '#ffffff',
    backgroundColor: '#1452fd',
  })
}

const showTip = ref(false)

const msgErro = ref('')

const data = ref({
  user: {},
  account: {}
})

const formState = ref({
  amount: ''
})

useDidShow(() => {
  fetch()
})

const fetch = () => {
  request.get({
    url: 'account'
  }).then(res => {
    data.value = res.data
  })
}

const onJump = (url) => {
  Taro.navigateTo({
    url: url
  })
}

const onSubmit = () => {
  if (formState.value.amount <= 0) {
    Taro.showToast({
      title: '请输入正确的提现金额',
      icon: 'none'
    })
    return
  }
  if (parseFloat(formState.value.amount) > parseFloat(data.value.user.amount)) {
    Taro.showToast({
      title: '可提现金额不足',
      icon: 'none'
    })
    return
  }
  request.post({
    url: 'account/drawCash',
    data: formState.value,
    showToast:false
  }).then(res => {
    Taro.showToast({
      title: '提现提交成功，请等待处理',
      icon: 'none',
      duration: 3000
    })
    setTimeout(() => {
      Taro.navigateBack()
    }, 3000)
  }).catch(err => {
    console.log(err);
    msgErro.value = err.data.message || '提现失败！'
    showTip.value = true
    // setTimeout(() => {
    //   showTip.value = false
    // },4500)
  })
}

const onDrawAll = () => {
  formState.value.amount = data.value.user.amount
}

</script>

<style lang="scss">
page {
  background-color: #f7f9ff;
}
.content-toast-modal {
  text-align: center;
  color: #000;
  line-height: 50px;
  font-size: 34px;
}
.card-info {
  padding: 42px 39px;
  background: #FFFFFF;

  .ci-bn {
    font-size: 32px;
  }
  .ci-id {
    font-size: 24px;
  }
}
.dc-form {
  margin-top: 40px;
  background: #FFFFFF;
  padding: 44px 37px;

  .dc-top {
    padding-bottom: 44px;
    .dc-t {
      font-size: 32px;
      font-weight: 500;
      color: #000000;
    }
    .dc-tip {
      font-size: 24px;
      font-weight: 500;
      color: #B3B3B3;
    }
  }
  .dc-ipt {
    border-bottom: 1px solid #DCDCDC;
    padding: 44px 0;
  }
  .dc-input {
    font-size: 60px;
    width: 460px;
    height: 66px;
  }
  .dc-amt {
    padding: 49px 0;

    .dc-amt-t {
      font-size: 26px;
      font-weight: 500;
      color: #000000;
      display: inline-block;
      padding-right: 20px;
    }
    .dc-cny {
      font-size: 24px;
      font-weight: 500;
      color: #1352FD;
    }
    .dc-amt-n {
      font-size: 36px;
      font-weight: 400;
      color: #1352FD;
    }
  }

}
.draw-cash-rule {
  padding: 52px 37px;

  .draw-cash-rule-t {
    font-size: 26px;
    font-weight: 500;
    color: #000000;
    padding-bottom: 10px;
  }
  .draw-cash-rule-c {
    font-size: 26px;
    font-weight: 500;
    color: #B3B3B3;
    line-height: 32px;
  }
}

</style>
