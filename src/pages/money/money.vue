<!--
 * @Autor: lisong
 * @Date: 2023-08-16 15:11:02
 * @LastEditors: lisong
 * @LastEditTime: 2023-08-16 15:58:43
-->
<template>
  <view class="top">
    <view class="top-left">
      <view class="top-left_title">
        <text>总金额(元)</text>
        <image
          class="top-left_img"
          src="https://yimidb.oss-cn-hangzhou.aliyuncs.com/mp/images/money/zjbz.png"
        ></image>
      </view>
      <view class="amount">{{data.user?.amount}}</view>
    </view>
    <view class="top-right" @tap="onJump('/pages/money/cashLog')">
      <view class="top-right_title">已提现金额(元)</view>
      <view class="top-right_amount">{{data.user?.draw_amt}}</view>
    </view>
  </view>
  <view class="content">
    <image
      src="https://yimidb.oss-cn-hangzhou.aliyuncs.com/mp/images/money/dian.png"
      class="top-dian"
    ></image>
    <view class="list" @tap="onJump('/pages/money/card')">
      <image
        src="https://yimidb.oss-cn-hangzhou.aliyuncs.com/mp/images/money/cards.png"
        class="list-img"
      ></image>
      <view>银行账户管理</view>
    </view>
  </view>
  <view class="list" @tap="onJump('/pages/money/bill')">
    <image
      src="https://yimidb.oss-cn-hangzhou.aliyuncs.com/mp/images/money/order.png"
      class="list-img"
    ></image>
    <view>我的账单</view>
  </view>

  <view class="bottom">
    <view class="bottom-btn" @tap="onDraw">去提现</view>
    <view class="bottom-sub">所有资金在您本人的银行电子账户中 </view>
  </view>

  <MyPopup :show="showTip" title="绑卡提示" @close="onChangeShowTip">
    <template #content>
      <view class="p40 text-center">您还未绑卡,是否去绑定银行卡?  </view>
      <view class="p20 flex flex-space-between flex-v-center" style="border-top: 1px solid #DFDFDF">
        <view style="width: 28%" class="text-center" @tap="onChangeShowTip">取消</view>
        <view style="width: 70%"><button class="btn-primary" @tap="onBindCard">去绑卡</button></view>
      </view>
    </template>
  </MyPopup>

</template>

<script setup>
import { ref } from "vue";
import request from "@/utils/request";
import myBottomBtn from "@/components/MyBottomBtn/MyBottomBtn";
import MyPopup from "@/components/MyPopup"
import { getApiRoot } from "@/config";
import Taro, {
  useDidShow,
  useDidHide,
  useReady,
  useLoad,
  usePullDownRefresh,
  useReachBottom,
} from "@tarojs/taro";
definePageConfig({
  navigationBarTitleText: "我的钱包",
  navigationBarBackgroundColor: "#F7F9FF",
});

const data = ref({
  user: {},
  account: {}
})

usePullDownRefresh(() => {
  fetch()
  Taro.stopPullDownRefresh()
})

useDidShow(() => {
  fetch()
})

const onJump = (url) => {
  Taro.navigateTo({
    url: url
  })
}

const fetch = () => {
  request.get({
    url: 'account'
  }).then(res => {
    data.value = res.data
  })
}

const onDraw = () => {
  if (data.value.account.account_info && data.value.account.account_info.card_id ) {
    onJump('/pages/money/draw')
  } else {
    onChangeShowTip()
  }
}

const showTip = ref(false)
const onChangeShowTip = () => {
  showTip.value = !showTip.value
}

const onBindCard = () => {
  onChangeShowTip()
  onJump('/pages/money/bindCard')
}

</script>

<style lang="scss">
page {
  background-color: #f7f9ff;
}
.top {
  width: 700rpx;
  height: 186rpx;
  border-radius: 20rpx 20rpx 0rpx 0rpx;
  margin: 25rpx auto 0;
  background: url(https://yimidb.oss-cn-hangzhou.aliyuncs.com/mp/images/money/top-bg.png)
    right 36rpx bottom no-repeat;
  background-size: 269rpx auto;
  background-color: #1352fd;
  box-sizing: border-box;
  position: relative;
  padding: 40rpx 0 40rpx 40rpx;
  display: flex;
  align-items: flex-end;
  .top-left {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
    .top-left_title {
      font-size: 20rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #8eacff;
      display: flex;
      align-items: center;
      .top-left_img {
        width: 111rpx;
        height: 24rpx;
        margin-left: 12rpx;
      }
    }
    .amount {
      font-size: 50rpx;
      font-family: Bahnschrift;
      font-weight: 400;
      color: #ffffff;
      line-height: 1;
    }
  }
  .top-right {
    width: 260rpx;
    text-align: center;
    .top-right_title {
      font-size: 20rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #96b2ff;
    }
    .top-right_amount {
      font-size: 36rpx;
      font-family: Bahnschrift;
      font-weight: 400;
      color: #ffffff;
      margin-top: 18rpx;
    }
  }
}

.content {
  height: 190rpx;
  background: #ffffff;
  box-shadow: 0rpx -20rpx 32rpx 0rpx rgba(54, 69, 193, 0.24);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin-bottom: 6rpx;
}

.top-dian {
  width: 41rpx;
  height: 21rpx;
  margin: -2rpx auto 0;
}

.list {
  height: 100rpx;
  display: flex;
  align-items: center;
  padding: 0 32rpx;
  background: url(https://yimidb.oss-cn-hangzhou.aliyuncs.com/mp/images/money/more.png)
    right 37rpx center no-repeat;
  background-size: 11rpx auto;
  background-color: #fff;
  font-size: 30rpx;
  font-family: OPPOSans;
  font-weight: 500;
  color: #000000;
  .list-img {
    width: 47rpx;
    height: 47rpx;
    margin-right: 20rpx;
  }
}

.bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 750rpx;
  height: 249rpx;
  background: #ffffff;
  box-shadow: 0rpx -4rpx 32rpx 0rpx rgba(54, 69, 193, 0.24);
  padding-bottom: env(safe-area-inset-bottom);
  overflow: hidden;
  .bottom-btn {
    width: 700rpx;
    height: 88rpx;
    background: #1352fd;
    border-radius: 20rpx;
    margin: 44rpx auto;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 36rpx;
    font-family: OPPOSans;
    font-weight: 500;
    color: #ffffff;
  }
  .bottom-sub {
    font-size: 30rpx;
    font-family: OPPOSans;
    font-weight: 500;
    color: #b6bec5;
    text-align: center;
  }
}
</style>
