<template>
  <view class="container-stat">
    <!-- <view class="stat-bg"></view> -->
    <view class="price-card">
      <!-- 上部 -->
      <view class="card">
        <view class="left">
          <view class="text">
            {{ typeStr == 0 ? "收入总计(元)" : deviceType == 3 ? "" : "用水量总计(m³)" }}
          </view>
          <view class="price" v-if="typeStr == 1 && deviceType == 3 "></view>
          <!-- <view class="price" v-else>{{
            typeStr == 0
              ? dataLis?.all
              : dataDian?.all
              ? (dataDian?.all / 1000).toFixed(2)
              : "0"
          }}</view> -->
          <view class="price" v-else>{{
           dataLis?.month
          }}</view>
        </view>
        <view class="right">
          <view class="cell" @tap="isShowSwitchTag = true">
            <text class="tit">{{ switchText }}</text>
            <text class="iconfont icon-qiehuan"></text>
          </view>
        </view>
      </view>
      <!-- 底部 -->
      <view class="card-bom">
        <view class="top">
          <view class="text">
            {{ typeStr == 0 ? "本月收入金额" : deviceType == 3 ? "本月用电量" : "本月用水量" }}
          </view>
          <picker
            mode="date"
            fields="month"
            :value="dateSel"
            @change="handleDateChange"
          >
            <view class="date-box">
              <text>{{ dateSel }}</text>
              <text class="iconfont icon-xiajiantou"></text>
            </view>
          </picker>
        </view>
        <view class="middle" v-if="typeStr == 0">
          <view class="li">
            <view class="top">
              <text class="p"> {{ dataLis?.month }}</text>
              <text> 元 </text>
            </view>
            <view class="bot"> 总营业额 </view>
          </view>
          <view class="li">
            <view class="top">
              <text class="p"> {{ dataLis?.online }}</text>
              <text> 元 </text>
            </view>
            <view class="bot"> 微信支付宝 </view>
          </view>
          <view class="li">
            <view class="top">
              <text class="p"> {{ dataLis?.under }}</text>
              <text> 元 </text>
            </view>
            <view class="bot"> 房东充值 </view>
          </view>
        </view>
        <view class="middle" v-else>
          <view class="li">
            <view class="top">
              <text class="p"> {{ dataDian?.now_total.toFixed(2) }}</text>
              <text> {{ deviceType == 3 ? "kWh" : "m³" }} </text>
            </view>
          </view>
        </view>
        <view class="bottom-box">
          <view :class="{ active: typeStr == 0 && deviceType == 3 }" @tap="typeStr = 0;deviceType = 3">
            {{'电费收入'  }}
          </view>
          <view :class="{ active: typeStr == 1 && deviceType == 3 }" @tap="typeStr = 1;deviceType = 3">
            {{'用电量' }}
          </view>
          <view :class="{ active: typeStr == 0 && deviceType == 4 }" @tap="typeStr = 0;deviceType = 4">
            {{ '水费收入' }}
          </view>
          <view :class="{ active: typeStr == 1 && deviceType == 4 }" @tap="typeStr = 1;deviceType = 4">
            {{ '用水量' }}
          </view>
        </view>
      </view>
    </view>
    <view class="stat">
      <view
        class="stat-box"
        @touchend="chartClickHandel"
        @touchmove="touchmove"
        @touchstart="chartStartHandel"
        @tap="clickChart"
      >
        <view class="title">
          {{ typeStr == 0 ? "收入趋势" : deviceType == 3 ? "用电量趋势" : "用水量趋势" }}
        </view>
        <!-- :style="isSlider ? 'pointer-events: none;' : ''"  -->
        <view class="chart-box">
          <!-- <canvas canvas-id="bill" style="width: 660px; height: 500px" /> -->
          <Line v-if="typeStr == 0 && !isShowSwitchTag" :dataObj="dataObj" :isUpdate="isUpdate" />
          <Line2 :deviceType="deviceType" v-else-if="typeStr == 1 && !isShowSwitchTag" :dataObj="dataObj" :isUpdate="isUpdate2" />
        </view>
        <!-- <view class="chart-btn flex flex-space-between">
          <view
            ><text
              :class="days === 7 ? 'tag-active' : 'tag-normal'"
              @tap="onChangeDays(7)"
              >近7天</text
            ></view
          >
          <view
            ><text
              :class="days === 30 ? 'tag-active' : 'tag-normal'"
              @tap="onChangeDays(30)"
              >近30天</text
            ></view
          >
          <view
            ><text
              :class="days === 90 ? 'tag-active' : 'tag-normal'"
              @tap="onChangeDays(90)"
              >近90天</text
            ></view
          >
        </view> -->
      </view>

      <view class="bills">
        <view class="bills-title">
          {{ typeStr == 0 ? "收入详情" : deviceType == 3 ? "用电量详情" : "用水量详情" }}</view
        >
        <view class="bills-list" v-if="typeStr == 0">
          <view class="bills-header flex">
            <view>时间</view>
            <view>项目</view>
            <view>支付方式</view>
            <view>收入</view>
            <view>房间号</view>
            <view>{{ deviceType == 3 ? "度数" : "m³" }}</view>
          </view>
          <view v-if="arrList && arrList.length">
            <view
              class="bills-item flex"
              v-for="bill in arrList"
              :key="bill.id"
            >
              <view class="paid-at">{{ bill.paid_at }}</view>
              <view>
                <text v-if="bill.type === 1">房租</text>
                <text v-if="bill.type === 2">押金</text>
                <text v-if="bill.type === 3">电费</text>
                <text v-if="bill.type === 4">水费</text>
              </view>
              <view>
                <text v-if="bill?.payment === 0">房东充值</text>
                <text v-else-if="bill?.payment === 1">线下微信</text>
                <text v-else-if="bill?.payment === 2">线下支付宝</text>
                <text v-else-if="bill?.payment === 3">线下银行卡</text>
                <text v-else-if="bill?.payment === 4">线上微信</text>
                <text v-else-if="bill?.payment === 5">线上支付宝</text>
                <text v-else>{{ bill?.payment_desc }}</text>
                <!-- <text v-if="!bill">线下微信</text> -->
              </view>
              <view>￥{{ bill.amount }}</view>
              <view>{{ bill.house_name }}</view>
              <view>{{  parseFloat(bill.title.match(/(\d+(\.\d+)?)/)?.[1] || 0) }}</view>
            </view>
          </view>
          <view
            v-else
            style="
              color: #ddd;
              font-size: 16px;
              text-align: center;
              margin-top: 15px;
            "
            >暂无数据</view
          >
        </view>
        <view class="bills-list" v-else>
          <view
            class="bills-header flex"
            :style="{
              justifyContent: 'center',
              fontSize: '30px',
            }"
          >
            <view>{{ dateSel }}</view>
          </view>
          <view v-if="arrList && arrList.length">
            <view
              class="bills-item flex"
              v-for="(item, idx) in arrList"
              :key="idx"
            >
              <view :style="{ width: '35%' }">{{ item?.power }}{{ deviceType == 3 ? "kWh" : "m³" }}</view>
              <view>
                <text :style="{ color: '#B6BEC5' }">{{
                  item?.start?.replaceAll("-", "/")
                }}</text>
              </view>
              <view :style="{ color: '#B6BEC5', width: '10%' }">
                <text>-</text>
              </view>
              <view :style="{ color: '#B6BEC5' }">{{
                item?.end?.replaceAll("-", "/")
              }}</view>
            </view>
          </view>
          <view
            v-else
            style="
              color: #ddd;
              font-size: 16px;
              text-align: center;
              margin-top: 15px;
            "
            >暂无数据</view
          >
        </view>
      </view>
    </view>
    <View> </View>
  </view>
  <SwitchTagDialog
    :showDialog="isShowSwitchTag"
    @close="isShowSwitchTag = false"
    @confirm="onConfirmSwitchTag"
  />
</template>
<script setup>
import { ref, computed, unref, watch } from "vue";
import Taro, {
  useDidShow,
  useDidHide,
  useReady,
  useLoad,
  usePullDownRefresh,
  useReachBottom,
} from "@tarojs/taro";
import request from "@/utils/request";
// import Charts from "@/utils/charts";

import { Picker } from "@tarojs/components";

import SwitchTagDialog from '@/components/SwitchTag/index.vue'

import MyIcon from "@/components/MyIcon";

import Line from "../components/line/index.vue";
import Line2 from "../components/line2/index.vue";

import { useGlobalStore } from "@/stores";
const globalStore = useGlobalStore();

const switchText = ref('全部')

const deviceType = ref(3)//3:电费  4：水费

const onConfirmSwitchTag = async (item) => {
  console.log("onConfirmSwitchTag", item);
  // switchText.value = item?.label
  // pages.value.class_id = item?.id
  switchText.value = item?.labels && item?.labels.join('、')
  pages.value.class_id = item?.ids
  getListData()
  // getDataHandel()
  // getDianHandel()
}

definePageConfig({
  navigationBarTitleText: "数据统计",
  navigationBarBackgroundColor:
    Taro.getEnv() === Taro.ENV_TYPE.ALIPAY ? "#1352fd" : "#f7f9ff",
  navigationBarTextStyle: "black",
});

let lineChart = null;

const isShowSwitchTag = ref(false)

useLoad(() => {
  // lineChart = new Charts({
  //   canvasId: "bill",
  //   width: 412,
  //   height: 300,
  //   type: "line",
  //   animation: true,
  //   categories: ["none"],
  //   series: [
  //     {
  //       name: "实收款项",
  //       data: [0],
  //     },
  //   ],
  //   yAxis: {
  //     min: 0,
  //   },
  // });
  // getDetail();
  getDataHandel().then(() => {
    getListData();
  })
});

const stat = ref({});
const days = ref(30);

// ------------------

const dateSel = ref(new Date().toISOString().split("T")[0].slice(0, 7));

const pages = ref({
  page: 1,
  limit: 15,
  class_id :''
});

const listTotal = ref(0);

const dataLis = ref();

const isSlider = ref(true);

const dataDian = ref();

const arrList = ref([]);

const isUpdate = ref(false);
const isUpdate2 = ref(false);

const dataObj = ref({
  dataKey: [],
  dataValue: [],
}); //图表数据

const typeStr = ref(0); //0收入 1用电

watch(
  () => [typeStr.value,deviceType.value],
  async (newVal) => {
    console.log(newVal);
    listTotal.value = 0;
    arrList.value = [];
    pages.value.page = 1;

    dataObj.value.dataKey = [];
    dataObj.value.dataValue = [];

    if (newVal[0] == 1) {
      isUpdate2.value = false;
      await getDianHandel();
      isUpdate2.value = true;
    } else if (newVal[0] == 0) {
      isUpdate.value = false;
      console.log("shouru");
      await getDataHandel();
      console.log(dataObj.value);
      getListData();
      isUpdate.value = true;
    }
  }
);

watch(
  () => isShowSwitchTag.value,
  async (newVal) => {
    console.log(newVal);
    if(newVal) return
    listTotal.value = 0;
    arrList.value = [];
    pages.value.page = 1;

    dataObj.value.dataKey = [];
    dataObj.value.dataValue = [];

    if (typeStr.value == 1) {
      isUpdate2.value = false;
      console.log('1111');
      await getDianHandel();
      isUpdate2.value = true;
    } else if (typeStr.value == 0) {
      isUpdate.value = false;
      console.log("shouru");
      await getDataHandel();
      console.log(dataObj.value);
      getListData();
      isUpdate.value = true;
    }
  }
);


const chartClickHandel = () => {
  console.log("end");
  isSlider.value = true;
};

const chartStartHandel = () => {
  console.log("start");
  isSlider.value = false;
};

const touchmove = () => {
  console.log("move");
  isSlider.value = false;
};

const clickChart = () => {
  console.log("click");
  isSlider.value = false;
  setTimeout(() => {
    isSlider.value = true;
  }, 300);
};

const handleDateChange = async (e) => {
  console.log(e);
  dateSel.value = e.detail.value.replace(/\//g, "-");
  isUpdate.value = false;
  listTotal.value = 0;
  arrList.value = [];
  pages.value.page = 1;

  dataObj.value.dataKey = [];
  dataObj.value.dataValue = [];
  if (unref(typeStr) == 0) {
    getListData();
    await getDataHandel();
  } else {
    isUpdate2.value = false;
    await getDianHandel();
    isUpdate2.value = true;
  }
  isUpdate.value = true;
};

const getDetail = () => {
  request
    .get({
      url: "business/stat",
      data: {
        days: days.value,
      },
    })
    .then((res) => {
      stat.value = res.data;
      renderCharts(res.data);
    });
};

/**
 * 获取统计图和收益数据
 */
const getDataHandel = () => {
  return new Promise((resolve, reject) => {
    request
      .get({
        url: "user/getBills",
        data: {
          date: dateSel.value.slice(0, 7),
          class_id: pages.value.class_id,
          device_type: deviceType.value,
        },
      })
      .then((res) => {
        // stat.value = res.data;
        dataLis.value = res.data;
        // 截取从当天往前数15天
        let today = new Date().getDate(); // 获取当前日期中的"天"

        let start = Math.max(0, today - 15); // 防止索引变成负数
        let end = today; // 不包括今天
        // let last15DaysDataExcludingToday = res.data.chart.slice(start, end);
        let last15DaysDataExcludingToday = res.data.chart;
        // let last15DaysDataExcludingToday = res.data.chart.slice(15);

        last15DaysDataExcludingToday.forEach((item, idx) => {
          dataObj.value.dataKey.push(item.date.slice(5));
          dataObj.value.dataValue.push(item.value);
        });
        isUpdate.value = true;
        resolve();
        // renderCharts(res.data);
      });
  });
};

/**
 * 获取用电量统计
 */
const getDianHandel = async () => {
  return new Promise((resolve, reject) => {
    request
      .get({
        url: "user/getPowers",
        data: {
          device_id: "",
          type: "month",
          time: dateSel.value,
          class_id: pages.value.class_id,
          device_type: deviceType.value,
        },
      })
      .then((res) => {
        dataDian.value = res.data;

        dataObj.value.dataKey = res.data.keys.map((item) => item.slice(5));
        dataObj.value.dataValue = res.data.data;
        if (res.data.table) {
          arrList.value = res.data.table;
          // console.log("arrList.value",arrList.value);
        }
        setTimeout(() => {
          resolve();
        },0)
      })
      .catch((e) => {
        console.log(e);
        reject();
      });
  });
};

/**
 * 获取收入列表数据
 */
const getListData = (str) => {
  if(typeStr.value == 1) return
  request
    .get({
      url: "user/billsList",
      data: {
        date: dateSel.value.slice(0, 7),
        ...unref(pages),
        device_type: deviceType.value,
      },
    })
    .then((res) => {
      // dataLis.value = res.data;
      if (str == "push") {
        arrList.value = arrList.value.concat(res.data.list);
      } else {
        listTotal.value = res.data.total;
        arrList.value = res.data.list;
      }
    });
};

useReachBottom(() => {
  if (unref(typeStr) == 1) return;
  if (unref(listTotal) != unref(arrList).length) {
    pages.value.page = pages.value.page + 1;
    getListData("push");
  } else {
    if (unref(listTotal) <= 0) return;
    Taro.showToast({
      title: "没有更多数据了",
      icon: "none",
      duration: 2000,
    });
  }
});

const onChangeDays = (v) => {
  days.value = v;
  // getDetail();
};

const renderCharts = (data) => {
  if (data.bill.label.length === 0) {
    return;
  }
  console.log(data);
  lineChart.updateData({
    categories: data.bill.label,
    series: [
      {
        name: "实收款项",
        data: data.bill.data[1],
      },
    ],
  });
};
</script>
<style lang="scss">
.container-stat {
  position: relative;
  padding-bottom: 50px;
  background-color: #f7f9ff;
  // ------------
  .price-card {
    .card {
      width: 700px;
      height: 186px;
      border-radius: 20px 20px 0px 0px;
      margin: 25px auto 0;
      background: url(https://yimidb.oss-cn-hangzhou.aliyuncs.com/mp/images/money/top-bg.png)
        right 36px bottom no-repeat;
      background-size: 269px auto;
      background-color: #1352fd;
      box-sizing: border-box;
      position: relative;
      padding: 40px 0 40px 40px;
      display: flex;
      align-items: flex-end;
      justify-content: space-between;
      color: #fff;
      .left {
        .text {
          color: #9caefe;
          font-size: 25px;
        }
        .price {
          font-size: 50px;
        }
      }
      .right {
        .cell {
          width: 183px;
          height: 52px;
          background: #083DCC;
          border-radius: 26px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 20px;
          margin-bottom: 26px;
          font-size: 28px;
          padding: 0 10px;
          .tit {
          width: 180px;
          // 单行文本溢出显示省略号
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          text-align: center;
          }
          .iconfont {
            font-size: 24px;
            margin-left: 6px;
          }
          &:active {
            opacity: 0.8;
          }
        }
      }
    }
    .card-bom {
      background-color: #fff;
      width: 100%;
      height: 270px;
      margin-bottom: 25px;
      box-shadow: 10px 10px 0px 10px #e2e5f8;
      .top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 30px 20px;
        .date-box {
          min-width: 161px;
          padding: 0 15px;
          height: 44px;
          background: #e4e9ff;
          border-radius: 22px;
          font-size: 24px;
          text-align: center;
          line-height: 44px;
          color: #7280bd;
          &:active {
            background-color: #a8b1dd;
          }
          .iconfont {
            font-size: 24px;
            color: #7280bd;
          }
        }
      }
      .middle {
        display: flex;
        justify-content: space-around;
        align-items: center;
        .li {
          text-align: center;
          .top {
            color: #1352fd;
            padding: 0;
            text {
              font-size: 30px;
              vertical-align: bottom;
            }

            .p {
              font-size: 40px;
              color: #1352fd;
              margin-right: 5px;
            }
          }
          .bot {
            color: #999;
            font-size: 24px;
          }
        }
      }
      .bottom-box {
        display: flex;
        justify-content: center;
        margin-top: 20px;
        > view {
          background: #dbdde5;
          border-radius: 30px;
          font-size: 22px;
          text-align: center;
          padding: 5px 20px;
          line-height: 40px;
          color: #828cb4;
          margin-left: 15px;
          &.active {
            background: #1352fd;
            color: #fff;
          }
        }
      }
    }
  }
}
.stat-bg {
  width: 100%;
  height: 313px;
  background: linear-gradient(180deg, #1452fd 43%, #ffffff 100%);
}
.stat {
  // position: absolute;
  // top: 70px;
  // left: 0;
  padding: 0 30px;
  width: 100%;
  box-sizing: border-box;
  background-color: #fff;
}
.chart-box {
  //padding: 34px 0;
  width: 700px;
  height: 400px;
  margin-left: -30px;
  .charts {
    width: 700px;
    height: 400px;
  }
  canvas {
    // width: 710px;
    // height: 530px;
  }
}
.chart-btn {
  padding: 30px 80px;
  text-align: center;

  .tag-normal {
    display: inline-block;
    background: #f2f5f7;
    border-radius: 8px;
    font-size: 24px;
    font-weight: 500;
    color: #000000;
    padding: 13px 28px;
  }
  .tag-active {
    display: inline-block;
    background: #1453fd;
    border-radius: 8px;
    font-size: 24px;
    font-weight: 500;
    color: #ffffff;
    padding: 13px 28px;
  }
}
.stat-box {
  background: #ffffff;
  // box-shadow: 0 7px 29px 0 rgba(112, 145, 178, 0.2);
  border-radius: 25px;
  padding-top: 15px;
  > .title {
    margin-bottom: 15px;
  }
}
.bills-title {
  font-size: 36px;
  font-weight: 500;
  color: #000000;
  padding: 30px 0;
}
.bills-header {
  height: 88px;
  background: #f2f5f7;
  border-radius: 15px;
  line-height: 88px;
  font-size: 24px;
  font-weight: 500;
  color: #000000;
  text-align: center;
}
.bills-header view,
.bills-item view {
  width: 25%;
  font-size: 29px;
  font-weight: 500;
  color: #000000;
     display: flex;
    justify-content: center;
    align-items: center;
}
.bills-item {
  text-align: center;
  padding: 35px 0;
  .paid-at {
    font-size: 25px;
  }
}
</style>
