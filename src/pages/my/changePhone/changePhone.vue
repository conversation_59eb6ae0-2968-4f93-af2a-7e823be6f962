<template>
  <View class="container">
    <view>
      <MyCell prefix="手机号">
        <template #content
          ><input
            v-model="formState.mobile"
            type="number"
            placeholder="请输入手机号"
        /></template>
      </MyCell>
      <MyCell prefix="验证码">
        <template #content>
          <view class="code-ipt">
            <input
              class="my-input-m"
              v-model="formState.sms_code"
              type="number"
              placeholder="请输入"
            />
            <view class="code">
              <text class="btn-text-small" v-if="!isSend" @tap="getSmsCode"
                >获取验证码</text
              >
              <text class="color-low" v-if="isSend">{{ timeout }}秒后重发</text>
            </view>
          </view>
        </template>
      </MyCell>
    </view>
    <view class="p20">
      <button class="btn-primary" @tap="handleSubmit">确认修改</button>
    </view>
  </View>
</template>

<script setup>
import { ref } from "vue";
import Taro, { useDidShow, useDidHide, useReady, useLoad } from "@tarojs/taro";
import request from "@/utils/request";

import { useGlobalStore } from "@/stores";

import MyCell from "@/components/MyCell";

import MyInput from "@/components/MyInput";

const globalStore = useGlobalStore();

definePageConfig({
  navigationBarTitleText: "修改手机号",
  navigationBarBackgroundColor: "#1352FD",
  navigationBarTextStyle: "white",
});

const formState = ref({
  mobile: "",
  sms_code: "",
});

useLoad(async () => {
});

const isSend = ref(false);
const timeout = ref(60);

const timeoutCountdown = () => {
  setTimeout(() => {
    timeout.value = timeout.value - 1;

    if (timeout.value > 0) {
      timeoutCountdown();
    } else {
      isSend.value = false;
      timeout.value = 60;
    }
  }, 1000);
};

const getSmsCode = () => {
  if (!formState.value.mobile) {
    Taro.showToast({
      title: "请输入手机号！",
      icon: "none",
    });
    return;
  }
  request
    .post({
      url: "sendSmsCode",
      data: {
        mobile: formState.value.mobile,
      },
    })
    .then((res) => {
      isSend.value = true;
      timeoutCountdown();
      Taro.showToast({
        title: "验证码已发送",
        icon: "success",
      });
    });
};

const handleSubmit = () => {
  if (!formState.value.mobile || !String(formState.value.sms_code).trim()) {
    Taro.showToast({
      title: "表单填写不全",
      icon: "error",
    });
    return;
  }
  request
    .post({
      url: "user/changeMobile",
      data: {
        mobile: formState.value.mobile,
        sms_code: String(formState.value.sms_code).trim(),
      },
    })
    .then((res) => {
      Taro.showModal({
        title: "修改手机号成功",
        content: "",
        showCancel: false,
        success: function (res) {
          if (res.confirm) {
            // globalStore.logout();
            Taro.reLaunch({
              url: "/pages/my/my",
            });
          } else {
            console.log("用户点击取消");
          }
        },
      });
    });
};
</script>

<style lang="scss">
.code-ipt {
  display: flex;
  justify-content: space-between;
}
</style>
