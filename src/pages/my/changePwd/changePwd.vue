<template>
  <View class="container">
    <view>
      <MyCell prefix="原密码">
        <template #content><input v-model="formState.old_password" type="password" :password="true" placeholder="请输入原登录密码" /></template>
      </MyCell>
      <MyCell prefix="新密码">
        <template #content><input v-model="formState.new_password" type="password" :password="true" placeholder="请输入新密码" /></template>
      </MyCell>
      <MyCell prefix="确认密码">
        <template #content><input v-model="formState.confirm_password" type="password" :password="true" placeholder="请再输入一遍新密码" /></template>
      </MyCell>
    </view>
    <view class="p20">
        <button class="btn-primary" @tap="handleSubmit">确认修改</button>
    </view>
  </View>
</template>

<script setup>
  import { ref } from 'vue'
  import Taro, { useDidShow, useDidHide, useReady, useLoad } from '@tarojs/taro'
  import request from '@/utils/request'


  import { useGlobalStore } from '@/stores'

  import MyCell from '@/components/MyCell'

  const globalStore = useGlobalStore()

  const formState = ref({
    old_password: '',
    new_password: '',
    confirm_password: '',
  })

  const handleSubmit = () => {
    if (!formState.value.old_password || !formState.value.new_password || !formState.value.confirm_password) {
      Taro.showToast({
        title: '表单填写不全',
        icon: 'error'
      })
      return
    }
    if (formState.value.new_password.length < 6) {
      Taro.showToast({
        title: '密码不能少于6位数',
        icon: 'none'
      })
      return
    }
    if (formState.value.new_password !== formState.value.confirm_password) {
      Taro.showToast({
        title: '两次输入的密码不一样',
        icon: 'none'
      })
      return
    }
    request.post({
      url: 'user/update',
      data: formState.value
    }).then(res => {
      Taro.showModal({
        title: '修改成功',
        content: '请使用新密码重新登录',
        showCancel: false,
        success: function (res) {
          if (res.confirm) {
            globalStore.logout()
            Taro.redirectTo({
              url: '/pages/login/login'
            })
          } else if (res.cancel) {
            console.log('用户点击取消')
          }
        }
      })
    })
  }



</script>
