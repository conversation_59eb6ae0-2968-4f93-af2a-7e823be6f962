page,body {
  background-color: #f6f7fb;
}
.gao-container {
  .ipt-box {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 30px auto;
    margin-bottom: 50px;
  }
  text{
    margin-left: 15px;
    color: #999;
  }
  .input{
    border: 1px solid #ccc;
    border-radius: 15px;
    width: 80%;
    height: 60px;
    text-align: center;

  }
}
.serve-img {
  position: fixed;
  right: 0;
  top: 38%;
  width: 189px;
  height: 270px;
  z-index: 999;
}
.container {
  background: url("https://yimits.oss-cn-beijing.aliyuncs.com/images/bg-my.png") no-repeat top right;
  background-size: contain;
  .user-info {
    padding-left: 42px;
    padding-bottom: 46px;
    .avatar {
      width: 132px;
      .avatar-img {
        width: 132px;
        height: 132px;
        border-radius: 100px;
      }
    }
    .info {
      margin-left: 32px;
      width: 60%;

      .nickname {
        font-size: 42px;
        font-family: OPPOSans;
        font-weight: bold;
        color: #000000;
      }
      .mobile {
        font-size: 32px;
        font-family: OPPOSans;
        font-weight: 500;
        color: #000000;
      }
    }
    .fdd {
      margin-right: 48px;

      .fdd-yes {
        background-color: #1352FD;
        color: #FFFFFF;
        font-size: 26px;
        height: 60px;
        line-height: 60px;
        padding: 0 29px;
        display: inline-block;
        border-radius: 16px;
      }
      .fdd-no {
        background-color: #FFFFFF;
        color: #1352FD;
        font-size: 26px;
        height: 60px;
        line-height: 60px;
        padding: 0 29px;
        display: inline-block;
        border-radius: 16px;
      }
    }
  }
  .block {
    margin: 0 24px 24px 24px;
    background: #FFFFFF;
    box-shadow: 0 0 7px 0 rgba(0,0,0,0.03);
    border-radius: 20px;

    .block-head {
      font-size: 32px;
      padding: 39px 25px;
    }
  }
  .who-left {
    padding: 41px 0 41px 39px;
    font-size: 30px;
    line-height: 30px;
  }
  .who-img {
    width: 36px;
    height: 36px;
    vertical-align: middle;
    margin-right: 10px;
  }
  .who-txt {
    color: #3F83FF;
    display: inline-block;
    padding-left: 10px;
    padding-right: 10px;
  }
  .who-change {
    text-align: right;
    padding: 41px 39px 41px 0;
    font-size: 30px;
    line-height: 30px;
  }
  .who-change-img {
    width: 27px;
    height: 24px;
    vertical-align: middle;
    margin-right: 10px;
  }
}

.grid-3 {
  display: grid;
  grid-template-columns: repeat(3, auto);
  padding: 0 31px 31px 31px;
  grid-gap: 72px 35px;
  .grid-item {
    text-align: center;
    .grid-item-img {
      width: 61px;
      height: 61px;
    }
  }
}

.my-title {
  margin: 0 24px 24px 24px;
  font-size: 32px;
  padding: 0 25px;
}
.grid-1 {
  // max-height: 600px;
  overflow-y: auto;
  padding-bottom: calc(env(safe-area-inset-bottom) + 190px);

  .grid-item {
    display: flex;
    align-items: center;

    margin: 0 24px 24px 24px;
    background: #FFFFFF;
    box-shadow: 0 0 7px 0 rgba(0,0,0,0.03);
    border-radius: 20px;
    height: 55px;
    line-height: 55px;

    padding: 20px 30px;
    font-size: 30px;
    &:active {
    background: #f1f1f1;
    }

    .grid-item-img {
      width: 61px;
      height: 61px;
      display: block;
      margin-right: 24px;
    }
    .grid-item-arrow {
      flex: 1;
      text-align: right;
      .grid-item-arrow-img {
        width: 11px;
        height: 19.5px;
      }
    }
  }
}

.serv {
  position: relative;
}

.simple-btn {
  background: none;
  height: 100%;
  font-size: 14px;
  line-height: 30px;
  width: 100%;
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
}

.logout {
  // position: fixed;
  // left: 50%;
  // transform: translateX(-50%);
  // bottom:  calc(env(safe-area-inset-bottom) + 150px);
  width: 100%;
  text-align: center;
  margin-top: calc(-190px - env(safe-area-inset-bottom));
  font-size: 32px;
  font-weight: 500;
  color: #000000;
}
.charts{
  width: 750px;
  height: 500px;
}