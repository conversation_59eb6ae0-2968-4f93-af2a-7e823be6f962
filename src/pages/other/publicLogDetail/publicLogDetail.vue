<script setup>
import { reactive, ref } from "vue";
import dayjs from "dayjs";
import Taro, {
  useDidShow,
  useLoad,
  useReachBottom,
  usePullDownRefresh,
} from "@tarojs/taro";
import request from "@/utils/request";

const date = ref(dayjs().format("YYYY-MM"));

const pages = reactive({
  limit: 3,
  page: 1,
  status: "", //0=分摊中 1=成功 3=失败
});

const total = ref(0);

const itemObj = ref(null);

const list = ref([]);

useLoad((option) => {
  if (!option.item) return;
  const obj = JSON.parse(option.item);
  itemObj.value = obj;
  getDetailList();
});

usePullDownRefresh(() => {
  console.log("refresh");
  pages.page = 1;
  getDetailList();
  Taro.stopPullDownRefresh();
});

useReachBottom(() => {
  console.log("bottom");
  if (list.value.length >= total.value) return;
  pages.page += 1;
  getDetailList(true);
});

const getDetailList = (bol) => {
  request
    .get({
      url: "device/busLog",
      data: {
        ...pages,
        class_id: itemObj.value?.class_id,
        date: date.value,
        log_id: itemObj.value?.id,
      },
    })
    .then((res) => {
      if (res.code != 200) return;
      total.value = res.data.total;
      if (bol) {
        list.value = list.value.concat(res.data.list);
      } else {
        list.value = res.data.list;
      }
    });
};

const leftHandel = () => {
  date.value = dayjs(date.value).subtract(1, "month").format("YYYY-MM");
  getDetailList();
};

const rightHandel = () => {
  date.value = dayjs(date.value).add(1, "month").format("YYYY-MM");
  getDetailList();
};
</script>

<template>
  <view class="public-logdetail-container">
    <view class="top-b">
      <view class="date-box">
        <view class="left" @tap="leftHandel">
          <text
            class="iconfont icon-youjiantou1"
            :style="{ transform: 'rotate(180deg)', display: 'block' }"
          ></text>
        </view>
        <text class="date">{{ date }}</text>
        <view class="right" @tap="rightHandel">
          <text class="iconfont icon-youjiantou1"></text>
        </view>
      </view>
      <text class="iconfont icon-shuaxin" @tap="getDetailList(false)"></text>
    </view>
    <view v-if="list.length <= 0" class="list-emty"> 暂无数据~ </view>
    <view class="list" v-for="item in list" :key="item.id">
      <view class="li">
        <view class="le" style="color: #1352fd"> 公摊扣费 </view>
        <view class="ri"> {{ item?.created_at }} </view>
      </view>
      <view class="li">
        <view class="le"> 房号： </view>
        <view class="ri"> {{ item?.device?.house?.name }}</view>
      </view>
      <view class="li">
        <view class="le"> 表号： </view>
        <view class="ri"> {{ item?.device?.sn }} </view>
      </view>
      <view class="li">
        <view class="le"> 数据： </view>
        <view class="ri"> -{{ item?.du }}kWh </view>
      </view>
      <view class="li">
        <view class="le"> 对应公表： </view>
        <view class="ri"> {{ itemObj?.p_sn }} </view>
      </view>
      <view class="li">
        <view class="le"> 扣除前： </view>
        <view class="ri"> {{ item?.mem_du }}kWh </view>
      </view>
      <view class="li">
        <view class="le"> 扣除后： </view>
        <view class="ri"> {{ item?.after_du }}kWh </view>
      </view>
      <view class="li">
        <view class="le"> 任务结果： </view>
        <view
          class="ri"
          :style="
            item?.status == 1
              ? 'color: #298E31'
              : tem?.status == 0
              ? 'color: #158FFF'
              : 'color: #ff0000'
          "
        >
          {{
            item?.status == 1 ? "成功" : tem?.status == 0 ? "分摊中" : "失败"
          }}
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss">
page {
  background-color: #f8f9fe;
}
.public-logdetail-container {
  padding-bottom: 100px;
  .list-emty {
    text-align: center;
    margin-top: 20px;
    color: #999;
    font-size: 28px;
  }
  .top-b {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20rpx;
    height: 80rpx;
    box-sizing: border-box;
    .iconfont {
      font-size: 46rpx;
      color: #494b5c;
    }
  }
  .date-box {
    display: flex;
    justify-content: center;
    align-items: center;
    // background-color: #fff;
    text {
      font-size: 28rpx;
      color: #214fcb;
    }
    .date {
      margin: 0 40px;
    }
    .left,
    .right {
      width: 48px;
      height: 29px;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #494b5c;
      border-radius: 20px;
      .iconfont {
        font-size: 24px;
        color: #fff;
      }
    }
  }
  .list {
    margin-top: 20rpx;
    background-color: #fff;
    padding-bottom: 20px;
    padding-top: 20px;
    .li {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 25rpx;
      height: 60rpx;
      // border-bottom: 1px solid #f8f9fe;
      font-size: 30rpx;
      margin-bottom: 25px;
      .le {
        color: #000;
      }
      .ri {
        color: #333;
      }
    }
    .detail {
      text-align: right;
      color: #214fcb;
      padding-bottom: 20px;
      border-top: 1px solid #f8f9fe;
      padding-top: 20px;
      padding-right: 20px;

      text {
        border-bottom: 1px solid #214fcb;
        padding-bottom: 1px;
      }
    }
  }
}
</style>
