<script setup>
import { ref, unref } from "vue";
import emtyImg from "@/assets/pic/emty.png";
import MyPopup from "@/components/MyPopup";
import request from "@/utils/request";
import Taro, { useDidShow, useLoad } from "@tarojs/taro";
import { watch } from "vue";
import { AtCheckbox } from "taro-ui-vue3";
import YToast from '@/components/YToast/index.vue'

const isMore = ref(false);

const showToastShow = ref(false)

const yToastText = ref('')

const addAdminShow = ref(false);

const adminInfoShow = ref(false);

const delShow = ref(false);

const adminPhone = ref("");

const adminPwd = ref("");

const username = ref('')

const editPopShow = ref(false);

const tagName = ref("");

const classList = ref([]);

const curentIdx = ref(0);

const curentItemObj = ref(null);

const curentItemSettingObj = ref(null);

const checkedList = ref([]);

const curentItem = ref();

const adminList = ref([]);

useLoad((options) => {
  const classId = options?.classId;
  console.log(options);
  getList().then(() => {
    if (classId) {
      classList.value.forEach((item, index) => {
        if (item.id == classId) {
          curentIdx.value = index;
          itemClickHandel(index);
        }
      });
    }
  });
});

const createTagHandel = () => {
  Taro.navigateTo({
    url: "/pages/other/houseTagAdmin/houseTagAdmin",
  });
};

const addHandek = () => {
  if(!checkedList.value.length) return
  addAdminShow.value = true;
};

const delItem = ref()

const delAdmin = (item) => {
  console.log(item);
  delItem.value = item
  delShow.value = true;
};

const yesDelAdmin = () => {
  request
    .post({
      url: "admin/delete",
      data: {
        id: unref(delItem)?.id,
      },
    })
    .then((res) => {
      if (res.code != 200) return;
      delShow.value = false;
      adminInfoShow.value = false
      curentItemSettingObj.value = null;
      getList();
      Taro.showToast({
        title: "删除成功",
        icon: "none",
      });
    });
};
const handleChange = (val, item) => {
  curentItemObj.value = item;
  const idx = checkedList.value.findIndex((item) => item == val);
  if (idx != -1) {
    checkedList.value.splice(idx, 1);
    console.log(checkedList.value, "arrset");
    if (!checkedList.value.length) curentItemObj.value = null;
    return;
  }

  console.log(val);

  checkedList.value.push(val);
  // checkedList.value = [val];

  const arr = new Set(checkedList.value);
  checkedList.value = Array.from(arr);

  console.log(checkedList.value, "arrset");
};

const confirmCreateHandel = () => {
  if (!adminPhone.value || !adminPwd.value) {
    Taro.showToast({
      title: "请输入手机号或密码！",
      icon: "none",
    });
    return;
  }
  if (!username.value) { 
    Taro.showToast({
      title: "请输入用户名！",
      icon: "none",
    });
    return;
  }
  request
    .post({
      url: "admin/create",
      data: {
        mobile: unref(adminPhone),
        password: unref(adminPwd),
        classId: JSON.stringify(unref(checkedList.value)),
        nickname: unref(username)
      },
      showToast: false
    })
    .then((res) => {
      if (res.code != 200) return;
      addAdminShow.value = false;
      adminPhone.value = "";
      adminPwd.value = "";
      username.value = ''
      getList();
      Taro.showToast({
        title: "添加成功",
        icon: "none",
      });
    }).catch((err) => {
      console.log(err);
      if (err.data.code != 200) {
        yToastText.value = err.data.message
        showToastShow.value = true
      }
    })
};

const getAdmitList = () => {
  return new Promise((resolve, reject) => {
    request
      .get({
        url: "admin/list",
        data: {},
      })
      .then((res) => {
        if (res.code != 200) return;
        console.log(res);
        adminList.value = res.data;
        resolve(res.data);
      })
      .catch(() => {
        reject();
      });
  });
};

// 获取分类列表
const getList = () => {
  return new Promise((resolve, reject) => {
    getAdmitList().then((adminList) => {
      curentItemObj.value = null;
      checkedList.value = [];
      request
        .get({
          url: "houseClass/list",
          data: {
            type: "edit",
          },
        })
        .then((res) => {
          if (res.code != 200) return;
          adminList.forEach((item) => {
            const classIds = item.class_id ? JSON.parse(item.class_id) : [];
            console.log(classIds, "classIds");
            res.data.forEach((item2) => {
              if (classIds.includes(item2.id)) {
                item2.isAdmin = true;
                item2.adminId = item.id;
              }
            });
          });
          classList.value = res.data;
          console.log(classList.value, "classList");
          resolve();
        })
        .catch((err) => {
          reject(err);
        });
    });
  });
};

useDidShow(() => {
  // getList();
});

const itemClickHandel = (index) => {
  isMore.value = !isMore.value;
  curentIdx.value = index;
  curentItemSettingObj.value = null
};

const settingItemHandel = (item) => {
  adminInfoShow.value = true;
  curentItemSettingObj.value = item
  checkedList.value = []
  console.log(item, "item");
  
}

const editPopHandel = (item) => {
  tagName.value = item?.label;
  curentItem.value = item;
  editPopShow.value = true;
};

watch(
  () => editPopShow.value,
  (newVal) => {
    if (!newVal) {
      tagName.value = "";
      curentItem.value = null;
    }
  }
);
</script>

<template>
  <view class="set-admin-container">
    <!-- 列表 -->
    <view class="list" v-if="classList.length > 0">
      <view
        class="item"
        @tap="itemClickHandel(index)"
        v-for="(item, index) in classList"
        :key="index"
      >
        <view class="top">
          <view class="left">
            <at-checkbox
              :options="[{ label: item.label, value: item.id }]"
              :selectedList="checkedList"
              @change="handleChange(item.id, item)"
            />
            <view
              class="icon-xiajiantou iconfont"
              v-if="item?.houses?.length > 0"
              :style="{
                transform: `rotate(${
                  isMore && curentIdx == index ? '180' : '0'
                }deg) translateY(3rpx)`,
                transition: 'all .3s',
              }"
            ></view>
            <!-- <view class="title">{{ item?.label }}</view> -->
          </view>
          <view class="right" v-if="item?.isAdmin" @tap.stop="settingItemHandel(item)">
            <view class="admin-btn">
                <image
                  src="https://yimits.oss-cn-beijing.aliyuncs.com/lins/set-admin.png"
                  mode="scaleToFill"
                  class="icon-s"
                />
              <text>管理员</text>
            </view>
          </view>
        </view>
        <template
          v-if="isMore && curentIdx == index && item?.houses?.length > 0"
        >
          <view class="_bottom" v-for="oitem in item.houses" :key="item.id">
            <view class="tit">
              <image
                src="https://yimits.oss-cn-beijing.aliyuncs.com/images/fangzi.png"
                mode="scaleToFill"
                class="icon-s"
              />
              <view>{{ oitem?.name }}</view>
            </view>
            <!-- 电表 -->
            <view class="li" v-for="(sitem, idx) in oitem.device" :key="idx">
              <view class="le">
                <text class="iconfont icon-dianbiao_shiti"></text>
                <text
                  :style="
                    sitem?.net_type == 1 && sitem?.signal_num <= 0
                      ? 'color: #98A6C3;'
                      : ''
                  "
                  >{{ sitem?.net_type == 1 ? "电表" : "电表" }}-{{
                    sitem?.sn
                  }}</text
                >
              </view>
              <view
                class="ri"
                v-if="sitem?.signal_num > 0 && sitem?.net_type == 1"
              >
                在线
              </view>
              <view
                class="ri"
                style="color: #98a6c3"
                v-if="sitem?.net_type == 1 && sitem?.signal_num <= 0"
              >
                离线
              </view>
            </view>
            <!-- 门锁 -->
            <view
              class="li"
              v-for="(sitem, idx) in oitem.lock.filter(
                (o, i) =>
                  oitem.lock.findIndex((f) => o.lock_id == f.lock_id) == i
              )"
              :key="idx"
            >
              <view class="le">
                <text
                  class="iconfont icon-a-jigouguanliduantubiao_huaban1fuben13"
                  style="font-size: 35rpx; margin-left: -5rpx"
                ></text>
                <text>门锁-{{ sitem?.lock_id }}</text>
              </view>
            </view>
          </view>
        </template>
      </view>
    </view>
    <!-- emty -->
    <view class="emty-box" v-if="classList.length == 0">
      <image :src="emtyImg" class="emty-img" mode="aspectFill"></image>
      <view>暂无分组</view>
    </view>
    <!-- utils -->
    <view class="footer-fixed utils" >
      <text @tap="createTagHandel">创建分组</text>
      <button
        class="btn-add m33"
        :style="{
          backgroundColor: checkedList.length <= 0 ? '#c1cff3' : '#1352fd',
        }"
        @tap="addHandek"
      >
        设置添加管理员
      </button>
    </view>
    <!-- <view class="footer-fixed utils" v-if="curentItemSettingObj">
      <text @tap="adminInfoShow = true" style="font-size: 28rpx"
        >查看管理员身份</text
      >
      <button
        class="btn-add m33"
        :style="{
          backgroundColor: '#ff532f',
        }"
        @tap="delAdmin"
      >
        删除管理员
      </button>
    </view> -->

    <MyPopup :show="addAdminShow" @close="addAdminShow = false">
      <template #content>
        <view class="pop-content">
          <view class="pop-title">添加管理员</view>
          <input
            placeholder="请输入管理员手机号"
            class="ipt"
            v-model="adminPhone"
          />
          <input
            placeholder="请输入登录密码"
            type="password"
            class="ipt"
            v-model="adminPwd"
          />
          <input
            placeholder="请输入管理员姓名"
            type="username"
            class="ipt"
            v-model="username"
          />
          <view class="utils">
            <view @tap="addAdminShow = false">取消</view>
            <button @tap="confirmCreateHandel">确认添加</button>
          </view>
        </view>
      </template>
    </MyPopup>

    <!-- edit弹窗 -->
    <MyPopup :show="adminInfoShow" @close="adminInfoShow = false">
      <template #content>
        <view class="pop-content">
          <view class="pop-title">管理员信息</view>
          <view class="card">
            <view class="item" v-for="(item,i) in adminList" :key="i">
              <view v-if="item.class_id ? JSON.parse(item.class_id).includes(curentItemSettingObj?.id) : false">
                <view class="le">
                  <view class="txt">{{ item?.nickname || '管理员' }}</view>
                </view>
                <view class="ri">
                  <view class="txt">{{ item?.mobile }}</view>
                  <view class="del-btn" @tap="delAdmin(item)">删除</view>
                </view>
              </view>
            </view>
          </view>
          <view class="utils-sigin">
            <view @tap="adminInfoShow = false">取消</view>
          </view>
        </view>
      </template>
    </MyPopup>

    <MyPopup :show="delShow" @close="delShow = false">
      <template #content>
        <view class="pop-content">
          <view class="content"> 确认删除{{ delItem?.mobile }}？ </view>
          <view class="utils" style="border-top: 1px solid #d7dbe8">
            <view @tap="delShow = false">取消</view>
            <button @tap="yesDelAdmin">确认</button>
          </view>
        </view>
      </template>
    </MyPopup>
    <YToast
      :show="showToastShow"
      width="310"
      height="100"
      padding="35rpx 35rpx"
      :text="yToastText"
      @close="showToastShow = false"
    />
  </view>
</template>

<style lang="scss">
page {
  background: #f8f9ff;
}
.set-admin-container {
  .at-checkbox__icon-cnt {
    border-color: #6c7c9c;
  }
  .at-checkbox::before,
  .at-checkbox::after {
    border-color: transparent;
  }
  .at-checkbox__title {
    color: #000;
    font-weight: 700;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    width: 360px;
    display: block;
  }
  > .utils {
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
    padding-left: 72px;
    button {
      width: 424px;
    }
  }
  > .list {
    padding: 0 30rpx;
    box-sizing: border-box;
    padding-bottom: 300px;
    .item {
      border: 1px solid #d7dbe8;
      width: 700px;
      min-height: 110px;
      box-sizing: border-box;
      margin-top: 30px;
      background: #ffffff;
      border-radius: 20px;
      padding: 20px;
      .tag {
        background: #f86811;
        border-radius: 15px;
        display: flex;
        justify-content: center;
        color: #fff;
        align-items: center;
        font-size: 30px;
        transform: translateX(10px);
        padding: 3px 10px;
        font-weight: 700;
      }
      ._bottom {
        border-top: 1px solid #d7dbe8;
        padding-top: 15px;
        padding-bottom: 20px;
        animation: opacity 0.3s;
        @keyframes opacity {
          0% {
            opacity: 0;
          }
          100% {
            opacity: 1;
          }
        }
        .tit {
          display: flex;
          align-items: center;
          font-size: 32px;
          color: #204eca;
          font-weight: 700;
          .icon-s {
            width: 32px;
            height: 32px;
            vertical-align: middle;
            margin-right: 10px;
          }
        }
        .li {
          display: flex;
          // justify-content: space-between;
          margin-top: 10px;
          font-size: 26px;
          .le {
            color: #090909;
            display: flex;
            align-items: center;
            .iconfont {
              font-size: 24px;
              color: #6f7c9a;
              margin-right: 5px;
            }
          }
          .ri {
            color: #0072ff;
            margin-left: 20px;
          }
        }
      }
      .top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-bottom: 15px;
        padding-top: 15px;
        .left {
          display: flex;
          align-items: center;
          // width: 340px;
          font-size: 24px;
          .iconfont {
            font-size: 25px;
            margin-right: 7px;
            color: #000;
          }
          .title {
            margin-right: 10px;
            font-size: 36px;
            font-weight: 700;
            overflow: hidden;
            text-overflow: ellipsis;
            // width: 100%;
          }
        }
        .right {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 168px;
          height: 43px;
          image {
            width: 30px;
            height: 30px;
          }
          .admin-btn {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            font-size: 24px;
            background-color: #275ff9;
            color: #fff;
            border-radius: 10px;
            padding: 5px 15px;
            &:active {
              opacity: .85;
            }
          }
        }
      }
    }
  }
  .emty-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100vh;
    font-size: 28rpx;
    color: #999;
    transform: translateY(-10%);
    .emty-img {
      width: 360px;
      height: 284px;
      margin: 0 auto;
    }
  }

  .pop-content {
    padding: 30px;
    .content {
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 40px;
      padding-top: 40px;
      padding-bottom: 40px;
    }
    .ipt {
      margin-bottom: 35px;
      padding-left: 40px;
      color: #999;
    }
    .pop-title {
      font-size: 32px;
      color: #000;
      text-align: center;
      padding-bottom: 40px;
      margin-top: 20px;
      font-weight: 700;
    }
    .card {
      .item {
        >view {
          display: flex;
          justify-content: space-between;
          margin-bottom: 20px;
          .le {
            color: #939394;
          }
          .ri {
            color: #000;
            display: flex;
            align-items: center;
            .txt {
              margin-right: 150px;
              color: #000;
            }
          }
        }
        .del-btn {
          color: #1352fd;
          border-radius: 10px;
          padding: 5px 10px;
          display: flex;
          justify-content: center;
          align-items: center;
          &:active {
            opacity: .85;
          }
        }
      }
    }
    input {
      // width: 681px;
      height: 101px;
      background: rgba(87, 114, 150, 0);
      border-radius: 20px;
      border: 2px solid #e9e9e9;
      padding-left: 20px;
    }
    .utils-sigin {
      display: flex;
      justify-content: center;
      border-top: 1px solid #e9e9e9;
      padding: 40px 0;
      margin-top: 50px;
    }
    .utils {
      display: flex;
      justify-content: space-between;
      margin-top: 20px;
      // border-top: 1px solid #E9E9E9;
      padding-top: 80px;
      view {
        width: 181px;
        height: 88px;
        border-radius: 20px;
        font-size: 28px;
        color: #000;
        text-align: center;
        line-height: 88px;
      }
      button {
        width: 460px;
        height: 88px;
        background: #1352fd;
        border-radius: 20px;
        font-size: 28px;
        color: #fff;
        text-align: center;
        line-height: 88px;
      }
    }
  }
}
</style>
