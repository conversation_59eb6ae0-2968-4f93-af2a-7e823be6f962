<script setup>
import { searchDevice } from "@/utils/waterDevice.js";
import { ref, unref } from "vue";
import Taro, { useDidShow, useLoad, usePullDownRefresh } from "@tarojs/taro";
import shuiImg from "@/assets/water/device.png";
import dishu from "@/assets/water/dishu.png";
import MyIcon from "@/components/MyIcon";
import request from "@/utils/request";
import GlobDialog from '@/components/globDialog/index.vue'


const id = ref(0);

const isNetNoSingle = ref(false); //是否为4g并且没信号

const device = ref({});

const showPrice = ref(false);

const isAp = process.env.TARO_ENV === "alipay";

useLoad(() => {
  searchDevice();
});

const onShowPrice = () => {
  showPrice.value = true;
};

const confirmGlobHandel = () => {
  console.log('confirmGlobHandel');
  
}

const dishuHandel = () => {
  Taro.navigateTo({
    url: "/pages/other/waterDiNumEdit/waterDiNumEdit",
  });
}
</script>

<template>
  <view class="water-detail">
    <GlobDialog @confirm="confirmGlobHandel"/>
    <!-- 顶部 -->
    <view class="flex device-top">
      <view
        class="device-icon device-icon-new"
        style="background-color: #1454c8"
      >
        <MyIcon icon="icon-dianbiao" width="44rpx" height="57rpx"></MyIcon
      ></view>
      <view style="flex: 1"
        ><view class="device-name"
          >房间号：{{ device?.house?.name || "无名称" }}</view
        ><view class="device-sn">
          <text v-if="device.net_type === 1 || isNetNoSingle">4G双模水表号</text
          ><text v-if="device.net_type !== 1 && !isNetNoSingle">蓝牙水表号</text
          >: {{ device.sn }}
        </view></view
      >
      <view style="width: 64rpx" v-if="device.net_type === 1 || isNetNoSingle">
        <MyIcon
          class="sing-con"
          :icon="'signal/' + device?.signal_num"
          width="60rpx"
          height="49rpx"
        ></MyIcon>
      </view>
    </view>
    <!-- 中部 -->
    <view class="device-du">
      <view class="device-du1">剩余水量(m³)</view>
      <view class="device-du2">{{ device?.du || "0.00" }}</view>
      <view class="device-du3">读表时间</view>
      <view class="device-du4"
        ><text v-if="!device.read_at">未读表</text
        ><text v-if="device.read_at">{{ device.read_at }}</text></view
      >
    </view>
    <!-- 设备信息 -->
    <view class="flex text-center flex-v-center bot-btn-box info">
      <view class="device-total">
        <view class="val">{{ Number(device?.total || 0).toFixed(2) }}</view>
        <view class="lab">总水量</view>
      </view>
      <view class="device-line1"></view>
      <view class="device-price">
        <view class="val">{{ device?.price || "0" }}</view>
        <view class="lab">单价(元/m³)</view>
        <view class="iconfont icon-bianji" @tap="onShowPrice"></view>
      </view>
    </view>
    <!-- 常用工具card -->
    <view class="main-btn flex flex-space-between text-center">
      <view class="main-btn-col" @tap="onChangeStatus">
        <view
          ><MyIcon
            icon="icon-device-close"
            width="54rpx"
            height="54rpx"
          ></MyIcon
        ></view>
        <view class="mt10"
          ><text v-if="device.status !== 2">断水</text
          ><text v-if="device.status === 2">开闸</text></view
        >
      </view>
      <view class="main-btn-col" @tap="dishuHandel()">
        <view>
          <image
            :src="dishu"
            style="width: 54rpx; height: 54rpx"
            mode="aspectFill"
          ></image>
        </view>
        <view class="mt10">底数校准</view>
      </view>
      <view class="main-btn-col" @tap="onRecharge">
        <view
          ><MyIcon
            icon="icon-device-recharge"
            width="54rpx"
            height="54rpx"
          ></MyIcon
        ></view>
        <view class="mt10">充值</view>
      </view>
    </view>
    <!-- 工具列表 -->
    <view class="other-btn flex flex-row flex-space-between">
      <view class="other-btn-col" @tap="shareHandel(device?.need_people)">
        <!-- device?.need_people == 1 ? '' :  'share'-->
        <button
          v-if="device?.need_people != 1"
          open-type="share"
          :plain="true"
          size="mini"
          class="btn-share-c"
        ></button>
        <button
          v-if="device?.need_people == 1"
          :open-type="fastDetailInfo != null ? 'share' : ''"
          :plain="true"
          size="mini"
          class="btn-share-c"
        ></button>
        <MyIcon icon="icon-device-share" width="33rpx" height="33rpx"></MyIcon>
        <text class="text-v-center">{{
          isAp ? "支付宝分享" : "微信分享"
        }}</text>
      </view>
      <!-- <view
        class="other-btn-col"
        @tap="bindHandel"
        v-if="device?.need_people != 1"
      >
        <text class="iconfont icon-bangding" style="font-size: 33rpx"></text>
        <text class="">手机号分享</text>
      </view> -->
      <!-- <view class="other-btn-col" @tap="fastCheckInHandel">
        <text class="iconfont icon-bangding" style="font-size: 33rpx"></text>
        <text class="">快速入住</text>
      </view> -->
      <view class="other-btn-col" @tap="onQueryStatus">
        <MyIcon icon="icon-device-cb" width="33rpx" height="33rpx"></MyIcon>
        <text class="text-v-center">水表抄表</text>
      </view>
      <!-- <view class="other-btn-col" @tap="onShowGaoJing">
        <MyIcon icon="icon-device-gj" width="33rpx" height="33rpx"></MyIcon>
        <text class="text-v-center">告警设置</text>
      </view> -->
      <view class="other-btn-col" @tap="onShowQrCode">
        <MyIcon icon="icon-device-qr" width="33rpx" height="33rpx"></MyIcon>
        <text class="text-v-center">二维码</text>
      </view>
      <view class="other-btn-col" @tap="onClear">
        <MyIcon icon="icon-device-clear" width="33rpx" height="33rpx"></MyIcon>
        <text class="text-v-center">水量清零</text>
      </view>
      <view class="other-btn-col" @tap="onRechargeLog">
        <MyIcon icon="icon-device-czlog" width="33rpx" height="33rpx"></MyIcon>
        <text class="text-v-center">充值记录</text>
      </view>
      <view class="other-btn-col" @tap="onClearLog">
        <MyIcon icon="icon-device-qllog" width="33rpx" height="33rpx"></MyIcon>
        <text class="text-v-center">清零记录</text>
      </view>
      <view class="other-btn-col" @tap="onDelete">
        <MyIcon icon="icon-device-bglog" width="33rpx" height="33rpx"></MyIcon>
        <text class="text-v-center">删除设备</text>
      </view>
    </view>
  </view>
</template>

<style lang="scss">
page,
body {
  background-color: #468ffe;
}
.water-detail {
  padding: 29px 39px;
  box-sizing: border-box;
  .device-top {
    display: flex;
    margin-bottom: 25px;
    align-items: center;
    .device-name {
      font-size: 28rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #fff;
    }
    .device-sn {
      font-size: 24rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #ffff;
    }
    .device-icon {
      width: 60rpx;
      height: 60rpx;
      background: #e9effa;
      border-radius: 10rpx;
      margin-right: 12px;
      text-align: center;
      padding: 2px 0;
    }
    .device-icon-new {
      padding-top: 7px;
    }
  }

  .device-du {
    width: 555px;
    height: 555px;
    margin-left: 45px;
    margin-top: 55px;
    background: url("https://yimits.oss-cn-beijing.aliyuncs.com/images/bg-device.png")
      no-repeat center;
    background-size: contain;
    text-align: center;
    color: #000000;

    .device-du1 {
      font-size: 24rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #000000;
      padding-top: 160px;
    }
    .device-du2 {
      font-size: 106px;
      font-family: Bahnschrift;
      font-weight: 400;
      color: #000000;
    }
    .device-du3 {
      font-size: 26rpx;
      font-family: OPPOSans;
      color: #1352fd;
      padding-top: 20px;
    }
    .device-du4 {
      font-size: 24rpx;
      font-family: OPPOSans;
      color: #b6bec5;
      padding-top: 10px;
    }
  }
  .info {
    .device-total {
      width: 49%;
    }
    .device-price {
      width: 49%;
    }
    .device-line1 {
      width: 2px;
      height: 54px;
      background: #6aa5ff;
    }
    .val {
      font-size: 60rpx;
      font-family: Bahnschrift;
      font-weight: 400;
      color: #ffffff;
    }
    .lab {
      font-size: 24rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #a3c8ff;
    }
    .device-price {
      position: relative;
    }
    .icon-bianji {
      position: absolute;
      right: 35px;
      top: 0px;
      color: #fff;
      font-size: 40px;
      width: 60px;
      height: 60px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  .main-btn {
    margin-top: 50px;
    background: #5a9bff;
    border-radius: 11rpx;
    padding: 34px 38px;
    width: 100%;
    box-sizing: border-box;
    justify-content: space-around;
    .main-btn-col {
      font-size: 22px;
      color: #fff;
    }
  }

  .other-btn {
    margin-top: 22px;
    .other-btn-col {
      position: relative;
      width: 310rpx;
      padding: 24rpx 0;
      background: #679ffe;
      border-radius: 11rpx;
      text-align: center;
      margin-bottom: 22rpx;
      color: #fff;
      > text {
        margin-left: 8px;
      }
      .btn-share-c {
        position: absolute;
        width: 100%;
        left: 0;
        height: 100%;
        top: 0;
        opacity: 0;
      }
    }
    .btn-share {
      position: relative;
    }
    .text-btn {
      left: 0;
      right: 0;
      border: none;
      outline: none;
      color: #ffffff;
      position: absolute;
      width: 100%;
      height: 94rpx;
    }
    .text-btn[plain] {
      border: 0;
      padding: 0;
      line-height: 160%;
      color: #ffffff;
      font-size: 22px;
    }
    .text-btn::after {
      outline: none;
      border: none;
    }
  }
}
</style>
