<script setup>
import dishuEdit from "@/assets/water/dishu-edit.png";
import request from "@/utils/request";

const setBottomNumHandel = () => {
  console.log("确认");
  // request
  //   .get({
  //     url: "admin/list",
  //     data: {},
  //   })
  //   .then((res) => {
  //     if (res.code != 200) return;
  //     console.log(res);
  //   })
  //   .catch(() => {
  //   });
}
</script>

<template>
  <view class="water-bottom-edit">
    <!-- 顶部提示 -->
    <view class="tip-top">
      <text class="iconfont icon-tanhao"></text>
      <text>温馨提示：请对比水表水量数值，输入数值并校准</text>
    </view>
    <!-- 中部 -->
    <view class="middle-main">
      <view class="img">
        <image :src="dishuEdit" mode="aspectFill"></image>
      </view>
      <view class="ipt">
        <view class="tip"> m³ </view>
        <input type="text" placeholder="请输入底数" />
      </view>
      <view class="desc">
        <view>水量底数修改说明： </view>
        <view
          >1. 水表安装后，显示数值与实际数值有差异，需要进 行修改水量底数</view
        >
        <view>2. 修改的水量底数，不会被水表清零所清楚</view>
        <view>3. 水量底数修改成功之后，将会清楚历史抄表记录</view>
      </view>
    </view>
    <!-- 按钮 -->
    <view class="footer-fixed">
    <view class="p20">
      <button class="btn-primary" @tap="setBottomNumHandel">
        确认
      </button>
    </view>
  </view>
  </view>
</template>

<style lang="scss">
.water-bottom-edit {
  padding-bottom: 200px;
  .tip-top {
    width: 100%;
    height: 84px;
    background: #ffa517;
    color: #fff;
    text-align: center;
    line-height: 84px;
    font-size: 28px;
    margin-bottom: 70px;
    .iconfont {
      font-size: 28px;
      margin-right: 10px;
      color: #fff;
    }
  }
  .middle-main {
    padding: 0 50px;
    .img {
      text-align: center;
      image {
        width: 558px;
        height: 399px;
      }
    }
    .ipt {
      margin-top: 90px;
      box-sizing: border-box;
      position: relative;
      .tip {
        position: absolute;
        right: 30px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 28px;
        color: #525252;
      }
      input {
        width: 100%;
        height: 120px;
        background: #f1f1f1;
        border-radius: 20px;
        padding: 0 45px;
        font-size: 36px;
        box-sizing: border-box;
        padding-right: 85px;
      }
    }
    .desc {
      color: #525252;
      font-size: 28px;
      margin-top: 50px;
      line-height: 50px;
    }
  }
}
</style>
