<script setup>
import { ref } from 'vue'
import Taro, { useDidShow, useDidHide, useReady, useLoad, usePullDownRefresh, useShareAppMessage } from '@tarojs/taro'
import request from '@/utils/request'
import { useGlobalStore } from '@/stores'


const globalStore = useGlobalStore()

definePageConfig({
    navigationBarTitleText: "常见问题",
    navigationBarBackgroundColor: "#1352FD",
    navigationBarTextStyle: "white",
  });

const items = ref([])
const key = ref('')

const getList = () => {
  request.get({
    url: 'qa',
  }).then(res => {
    items.value = res.data
  })
}

useLoad(() => {
  getList()
})

const onDetail = (id) => {
  Taro.navigateTo({
    url: '/pages/qa/detail?id=' + id
  })
}

</script>
<template>
  <view class="qa-con">
    <view class="qa-list">
      <view class="qa-item" v-for="item in items" :key="item.id" @tap="onDetail(item.id)">
        <view>{{item.title}}</view>
      </view>
    </view>
  </view>
</template>
<style lang="scss">
  page,body {
    background-color: #f1f2f3;
  }
  .qa-con {
    padding: 27px 27px 200px 27px;
  }
  .qa-item {
      margin-bottom: 26px;
      background: #FFFFFF;
      text-align: left;
      border-radius: 14px;
      padding: 20px;
    }
</style>
