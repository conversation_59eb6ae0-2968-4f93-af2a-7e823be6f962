<template>
  <View class="container">
    <view class="login-top">
      <view class="logo2"><image :src="Logo2" class="logo2-img"></image></view>
      <view class="t1">账号注册</view>
    </view>
    <view class="login-form">
      <MyInput placeholder="请输入">
        <template #prefix><text>手机号</text></template>
        <template #content><input class="my-input-m" v-model="formState.mobile" type="number" placeholder="请输入" /></template>
      </MyInput>
      <MyInput prefix="验证码" placeholder="请输入">
        <template #suffix>
          <text class="btn-text-small" v-if="!isSend" @tap="getSmsCode">获取验证码</text>
          <text class="color-low" v-if="isSend">{{timeout}}秒后重发</text>
        </template>
        <template #content><input class="my-input-m" v-model="formState.sms_code" type="number" placeholder="请输入" /></template>
      </MyInput>
      <MyInput prefix="设置密码" placeholder="请输入">
        <template #suffix>
            <image @tap="onChangePasswordType" v-if="passwordType === 'password'" :src="iconEye" class="icon-eye"></image>
            <image @tap="onChangePasswordType" v-if="passwordType === 'text'" :src="iconEyeClose" class="icon-eye"></image>
        </template>
        <template #content><input class="my-input-m" v-model="formState.password" :type="passwordType" placeholder="请输入" /></template>
      </MyInput>
      <MyInput prefix="确认密码" placeholder="请输入">
        <template #suffix>
            <image @tap="onChangePasswordType" v-if="passwordType === 'password'" :src="iconEye" class="icon-eye"></image>
            <image @tap="onChangePasswordType" v-if="passwordType === 'text'" :src="iconEyeClose" class="icon-eye"></image>
        </template>
        <template #content>
          <input class="my-input-m" v-model="formState.confirm_password" :type="passwordType" placeholder="请输入" />
        </template>
      </MyInput>
      <view class="privacy flex flex-v-center">
        <view class="privacy-left">
          <image @tap="onChangePrivacy(1)" v-if="!formState.privacy" :src="iconRadio" class="icon-radio"></image>
            <image @tap="onChangePrivacy(0)" v-if="formState.privacy" :src="iconRadioChecked" class="icon-radio"></image>
          <text @tap="onChangePrivacy(1)">阅读并同意</text> <text>用户手册</text> 和 <text>隐私政策</text></view>
      </view>
      <view>
        <button class="btn-primary" @tap="handleLogin">注册</button>
      </view>
    </view>
    <view class="reg" @tap="onLogin">
        立即登录 <image :src="iconRegArrow" class="icon-reg-arrow"></image>
    </view>
  </View>
</template>

<script setup>
  import { ref } from 'vue'
  import Taro, { useDidShow, useDidHide, useReady, useLoad } from '@tarojs/taro'
  import request from '../../utils/request'
  import {checkPhoneNumber, checkPassword} from '../../utils/index'
  import { useGlobalStore } from '../../stores'
  import MyInput from '../../components/MyInput'

  import './reg.scss'
  const Logo2 = 'https://yimits.oss-cn-beijing.aliyuncs.com/images/logo2.png'
  const iconEye = 'https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-eye.png'
  const iconEyeClose = 'https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-eye-close.png'
  const iconRadio = 'https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-radio.png'
  const iconRadioChecked = 'https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-radio-checked.png'
  const iconRegArrow = 'https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-reg-arrow.png'

  const globalStore = useGlobalStore()

  const formState = ref({})

  const passwordType = ref('password')

  const onChangePasswordType = () => {
    if (passwordType.value === 'password') {
        passwordType.value = 'text'
    } else {
        passwordType.value = 'password'
    }
  }
  const onChangePrivacy = (val) => {
    formState.value.privacy = val
  }

  const handleLogin = () => {
    console.log(formState)
    if (!formState.value.mobile || !formState.value.password || !formState.value.confirm_password || !formState.value.sms_code) {
      Taro.showToast({
        title: '表单填写不全',
        icon: 'none'
      })
      return
    }
    if (!checkPhoneNumber(formState.value.mobile)) {
      Taro.showToast({
        title: '手机号码格式错误',
        icon: 'none'
      })
      return
    }
    if (!checkPassword(formState.value.password)) {
      Taro.showToast({
        title: '密码不能少于6位数',
        icon: 'none'
      })
      return
    }
    if (formState.value.password !== formState.value.confirm_password) {
      Taro.showToast({
        title: '两次输入的密码不一致',
        icon: 'none'
      })
      return
    }
    if (!formState.value.privacy) {
      Taro.showToast({
        title: '请阅读并同意用户手册及隐私协议',
        icon: 'none'
      })
      return
    }
    //发起网络请求
    formState.value.who = globalStore.who
    request.post({
      url: 'user/register',
      data: formState.value,
    }).then((res) => {
      Taro.setStorageSync('token', res.data.token)
      globalStore.setUserInfo(res.data)
      Taro.navigateBack()
    })

  }

  const onLogin = () => {
    Taro.redirectTo({
      url: '/pages/login/login'
    })
  }

  const isSend = ref(false)
  const timeout = ref(60)

  const getSmsCode = () => {
    if (!formState.value.mobile) {
      Taro.showToast({
        title: '请输入手机号码',
        icon: 'none'
      })
      return
    }
    request.post({
      url: 'sendSmsCode',
      data: {
        mobile: formState.value.mobile
      }
    }).then(res => {
      isSend.value = true
      timeoutCountdown()
    })
  }

  const timeoutCountdown = () => {
    setTimeout(() => {
      timeout.value = timeout.value - 1

      if (timeout.value > 0) {
        timeoutCountdown()
      } else {
        isSend.value = false
        timeout.value = 60
      }
    }, 1000)
  }

</script>
