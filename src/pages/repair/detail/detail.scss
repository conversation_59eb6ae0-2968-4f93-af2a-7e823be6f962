page, body {
  background-color: #f1f2f3;
}
.container {
  padding: 27px 27px 200px 27px;
}
.repair-item {
  background-color: #FFFFFF;
  border-radius: 16px;
  font-size: 26px;
  font-weight: 500;
  color: #A8B1CA;
  position: relative;
  margin-bottom: 27px;
}
.house-name {
  padding: 33px;
}
.estate-name {
  font-size: 36px;
  font-weight: bold;
  color: #000000;
}
.fang {
  font-size: 26px;
  font-weight: 500;
  color: #A8B1CA;
}
.repair-status {
  position: absolute;
  top: 33px;
  right: 33px;
}
.status2 {
  background: #DBDBDB;
  border-radius: 6px;
  font-size: 22px;
  font-weight: 400;
  color: #787878;
  padding: 6px 15px;
}
.status1 {
  background: #F9E8D6;
  border-radius: 6px;
  font-size: 22px;
  font-weight: 400;
  color: #F3560A;
  padding: 6px 15px;
}
.repair-btn {
  padding: 33px;
  border-top: 1px dashed #DEE5F4;
}
.repair-info {
  padding: 0 33px 33px 33px;
}
.upload-image {
  width: 147px;
  height: 147px;
  border-radius: 20px;
  margin-left: 20px;
  position: relative;

  .image {
    border-radius: 20px;
    width: 147px;
    height: 147px;
  }
}
