<template>
  <view>
    <MySearch placeholder="关健词搜索" @search="onSearch"></MySearch>
    <MyFilter :options="options" @change="onChangeFilter"></MyFilter>
    <view class="container">
      <view class="bill-item" v-for="item in items" :key="item.id">
        <view class="flex flex-v-center">
          <view v-if="!item.is_paid" class="bill-check" @tap="onCheckBill(item)">
            <MyIcon v-if="!item.checked" icon="icon-check" width="40rpx" height="40rpx"></MyIcon>
            <MyIcon v-if="item.checked" icon="icon-checked" width="40rpx" height="40rpx"></MyIcon>
          </view>
          <view class="bill-con">
            <view class="house-name flex flex-space-between flex-v-center">
              <view>{{item.house.estate_name + item.house.name}}</view>
              <view><MyIcon icon="icon-arrow-right" width="15rpx" height="28rpx"></MyIcon></view>
            </view>
            <view class="flex flex-space-between bill-m flex-v-center">
              <view class="flex-l bill-title">{{item.title}}</view>
              <view class="flex-r bill-amount" style="text-align: right;"><text class="cny">￥</text>{{item.amount}}</view>
            </view>
            <view class="bill-m bill-info">
              <view>合同编号 {{item.contract.sn}}</view>
              <view class="mt10">应付日期 {{item.pay_date}}</view>
              <view class="mt10" v-if="item.is_paid">付款日期 {{item.paid_at}}</view>
            </view>
            <view class="flex bill-m">
              <view v-if="item.is_fail" class="bill-fail"><MyIcon icon="icon-warning" width="32rpx" height="30rpx"></MyIcon> <text class="text-v-center">已逾期 {{item.fail_days}} 天</text></view>
              <view v-if="item.is_paid" class="bill-paid">已支付</view>
            </view>
            <view v-if="!item.is_paid" class="text-right bill-m">
              <button class="btn-primary-small btn-inline" @tap="onShowPayOne(item)">立即支付</button>
            </view>
          </view>
        </view>
      </view>
    </view>

    <view class="footer-fixed">
      <view class="flex flex-space-between flex-v-center p20">
        <view>共 {{billList.length}} 笔，合计<text class="color-primary">￥{{ billAmount }}元</text></view>
        <view><button :class="billAmount > 0 ? 'btn-primary' : 'btn-disabled'" @tap="onShowPay">批量支付</button></view>
      </view>
    </view>

    <MyPopup :show="showPay" title="在线支付" @close="changeShowPay">
      <template #content>
        <view class="b-pay-amount p20 text-center"><text class="cny">￥</text> {{payData.amount}}</view>
        <view v-if="payData.fee > 0" class="b-pay-fee p20"> 包含手续费 {{payData.fee}}元 </view>
        <view class="b-pay-tips">您支付的费用直达房东账户，如需退款请直接联系房东。</view>
        <view class="p20"><button class="btn-primary" @tap="onPay">立即支付</button></view>
      </template>
    </MyPopup>

    <YToast :show="showToast" width="310" height="100" padding="35rpx 35rpx" :text="yToastText" @close="showToast = false"/>

  </view>
</template>

<script setup>
import {computed, ref} from 'vue'
  import Taro, { useDidShow, useDidHide, useReady, useLoad, usePullDownRefresh, useReachBottom } from '@tarojs/taro'
  import request from '@/utils/request'
  import { toFixed } from '@/utils/index'
  import './bill.scss'

  import MyFilter from '@/components/MyFilter'
  import MySearch from '@/components/MySearch'

  import MyIcon from '@/components/MyIcon'
  import MyPopup from '@/components/MyPopup'

  import YToast from '@/components/YToast/index.vue'

  const showToast = ref(false)

  const yToastText = ref('')



  import { useGlobalStore } from '@/stores'
  const globalStore = useGlobalStore()

  const options = ref([
    {
      name: '付款状态',
      key: 'status',
      options: [
        {
          label: '待付款',
          value: 1
        },
        {
          label: '已付款',
          value: 2
        }
      ]
    },
    {
      name: '账单类型',
      key: 'type',
      options: [
        {
          label: '房租',
          value: 1
        },
        {
          label: '押金',
          value: 2
        },
        {
          label: '电费',
          value: 3
        }
      ]
    }
  ])

  const onChangeFilter = (child, key) => {
    console.log(child, key)
    params.value[key] = child.value
    fetch()
  }
  const onSearch = (keyword) => {
    params.value.keyword = keyword
    fetch()
  }

  const billList = ref([])
  const billAmount = computed(() => {
    let a = 0
    billList.value.map(b => {
      a = parseFloat(a) + parseFloat(b.amount)
    })
    return a
  })

  const onCheckBill = (bill) => {
    billList.value = []
    items.value.forEach(item => {
      if (item.id === bill.id) {
        item.checked = !item.checked
      }
      if (item.checked) {
        billList.value.push(item)
      }
    })
    console.log(billList.value)
  }

  const payData = ref({
    billId: [],
    amount: 0,
    rate: 0,
    fee: 0
  })
  const showPay = ref(false)
  const onShowPay = () => {
    if (billAmount.value <= 0) {
      Taro.showToast({
        title: '请选择账单',
        icon: 'none'
      })
      return
    }
    payData.value.billId = billList.value.map(b => {
      return b.id
    })
    payData.value.amount = billAmount.value
    payData.value.fee = toFixed(payData.value.rate * payData.value.amount, 2)
    payData.value.amount = billAmount.value + parseFloat(payData.value.fee)
    changeShowPay()
  }
  const onShowPayOne = (item) => {
    console.log(item)
    payData.value.billId = [item.id]
    payData.value.amount = item.amount
    payData.value.fee = toFixed(payData.value.rate * parseFloat(payData.value.amount), 2)
    payData.value.amount = parseFloat(item.amount) + parseFloat(payData.value.fee)
    changeShowPay()
  }
  const changeShowPay = () => {
    showPay.value = !showPay.value
  }

  const onPay = () => {
    request.post({
      url: 'payment/billPay',
      data: {
        billId: payData.value.billId,
        amount: payData.value.amount,
        fee: payData.value.fee,
        pay_channel: process.env.TARO_ENV === 'alipay' ? 'alipay_lite' : 'wx_lite',
      },
      showToast:false
    }).then(res => {
      console.log(res, 'payment params')
      let payConfig = JSON.parse(res.data.expend.pay_info)
      if (process.env.TARO_ENV === 'weapp') {
        wx.requestPayment({
          timeStamp: payConfig.timeStamp,
          nonceStr: payConfig.nonceStr,
          package: payConfig.package,
          signType: payConfig.signType,
          paySign: payConfig.paySign,
          success() {
            console.log('success')
            Taro.showToast({
              title: '支付成功！'
            })
            globalStore.homeHasChange = true
            // 刷新列表
            fetch()
          },
          fail() {
            console.log('fail')
            Taro.showToast({
              title: '支付失败',
              icon: 'error'
            })
          }
        })
      }
      if (process.env.TARO_ENV === 'alipay') {
        my.tradePay ({
          // 调用统一收单交易创建接口（alipay.trade.create），获得返回字段支付宝交易号 trade_no
          tradeNO: payConfig.tradeNO,
          success: res => {
            console.log('success', res)
            if (res.resultCode === 9000) {
              Taro.showToast({
                title: '支付成功！'
              })
              globalStore.homeHasChange = true
              // 刷新列表
              fetch()
            } else {
              Taro.showToast({
                title: '支付失败',
                icon: 'error'
              })
            }
          },
          fail: res => {
            console.log('fail', res)
            Taro.showToast({
              title: '支付失败',
              icon: 'error'
            })
          },
        });
      }
    }).catch(err => {
      console.log(err);
      if (err.data.code != 200) {
        yToastText.value = err.data.message
        showToast.value = true
      }
    })
  }


  /** ----------------------接口数据-begin----------------------------------- */
  const items = ref([])
  const total = ref(0)
  const conf = ref({})

  const params = ref({
    page: 1,
    status: 1,
    contractId: 0
  })

  const onChangeStatus = (val) => {
    params.value.status = val
    fetch()
  }

  const fetch = () => {
    items.value = []
    params.value.page = 1
    getList()
  }

  const getList = () => {
    request.get({
      url: 'tenant/bills',
      data: {
        ... params.value
      }
    }).then(res => {
      if (items.value.length > 0) {
        items.value = [items.value, ... res.data.items]
      } else {
        items.value = res.data.items
      }
      total.value = res.data.total
      if (res.data.currentPage >= res.data.lastPage) {
        isLastPage.value = true
      }
      if (!conf.value.id) {
        request.get({
          url: 'business/conf',
          data: {
            businessId: items.value[0].business_id
          }
        }).then(res => {
          conf.value = res.data
          if (res.data.fee_payer === 2) {
            //payData.value.rate = 0.003;
            payData.value.rate = 0.006;
          }
        })
      }
    })
  }

  /** ----------------------接口数据-end----------------------------------- */

  useLoad((options) => {
    if (options.status) {
      params.value.status = options.status
    }
    if (options.contractId) {
      params.value.contractId = options.contractId
    }
    updateOpenId()
    console.log(1)
  })

  useDidShow(() => {
    fetch()
    console.log(2)
    showPay.value = false
  })

  usePullDownRefresh(() => {
    Taro.stopPullDownRefresh()
    fetch()
  })

  const isLastPage = ref(false)

  useReachBottom(() => {
    if (!isLastPage.value) {
      params.value.page += 1
      getList()
    }
  })

  const updateOpenId = () => {
    if (process.env.TARO_ENV === 'weapp') {
      wx.login({
        success(res) {
          if (res.code) {
            request.post({
              url: 'user/updateOpenId',
              showLoading: false,
              data: {
                code: res.code
              }
            }).then(_ => {

            })
          }
        },
        fail() {
          Taro.showToast({
            title: '获取openid出错',
            icon: 'error'
          })
        }
      })
    } else {
      my.getAuthCode({
        scopes: 'auth_base',
        success: res => {
          // 在服务端获取用户信息
          if (res.authCode) {
            request.post({
              url: 'user/updateAlipayId',
              showLoading: false,
              data: {
                code: res.authCode
              }
            }).then(_ => {

            })
          }
        },
        fail: err => {
          console.log('my.getAuthCode 调用失败', err)
        }
      });
    }
  }


</script>
