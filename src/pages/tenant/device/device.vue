<template>
  <View class="container">

    <DeviceRender :items="items" @detail="onDetail"></DeviceRender>

  </View>
</template>

<script setup>
  import { ref } from 'vue'
  import './device.scss'
  import Taro, { useDidShow, useDidHide, useReady, useLoad, usePullDownRefresh, useReachBottom} from '@tarojs/taro'
  import request from '@/utils/request'

  import MySearch from '@/components/MySearch'
  import MyIcon from '@/components/MyIcon'
  import DeviceRender from '@/components/DeviceRender'

  import { useGlobalStore } from '@/stores'
  const globalStore = useGlobalStore()

  usePullDownRefresh(() => {
    console.log('onPullDownRefresh')
  })

  const onDetail = (id) => {
    Taro.navigateTo({
      url: '/pages/tenant/deviceDetail/deviceDetail?id=' + id
    })
  }

  /** ----------------------接口数据-begin----------------------------------- */
  const items = ref([])
  const houseId = ref(0)

  const params = ref({
    page: 1,
    status: 0,
    keyword: '',
    sort: 'created_at',
    order: 'desc'
  })

  const fetch = () => {
    items.value = []
    params.value.page = 1
    getList()
  }

  const getList = () => {
    request.get({
      url: 'tenant/device',
      data: {
        houseId: houseId.value
      }
    }).then(res => {
      items.value = res.data
    })
  }
  /** ----------------------接口数据-end----------------------------------- */

  useLoad((options) => {
    houseId.value = options.houseId
    fetch()
  })


</script>

