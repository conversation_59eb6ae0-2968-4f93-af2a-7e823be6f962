<template>
  <view>
    <view class="container">
      <MyCell prefix="选择房源" align="right" arrow @tap="handleChooseContract">
        <template #content>
          {{ houseName || '请选择' }}
        </template>
      </MyCell>
      <MyCell prefix="报修项目" align="right" arrow @tap="onChooseTitle">
        <template #content>
          {{ formState.title || '请选择' }}
        </template>
      </MyCell>
      <view class="form-item">
        <view>维修说明</view>
        <textarea name="content" v-model="formState.content" placeholder="请填写维修说明" />
      </view>

      <view class="up-img">
        <view class="up-img-t"><text>上传问题照片</text><text class="tip">（单张照片大小限制在8M以内）</text></view>
        <view class="mt20 flex flex-row">
          <view class="upload-btn" @tap="onUploadImage"><MyIcon icon="icon-upload-plus" width="40rpx" height="40rpx"></MyIcon></view>
          <view class="upload-image" v-for="(img,index) in formState.images" :key="img">
            <image class="image" :src="img" @tap="onPreview(img)" mode="widthFix"></image>
            <view class="img-del" @tap="onDel(index)"><MyIcon icon="icon-img-del" width="43rpx" height="44rpx"></MyIcon></view>
          </view>
        </view>
      </view>

    </view>

    <view class="footer-fixed">
      <view class="p20">
        <button class="btn-primary" @tap="onSubmit">确认提交</button>
      </view>
    </view>

  </view>
</template>

<script setup>
  import { ref } from 'vue'
  import Taro, { useDidShow, useDidHide, useReady, useLoad } from '@tarojs/taro'
  import request from '@/utils/request'
  import './repairAdd.scss'

  import MyIcon from '@/components/MyIcon'
  import MyCell from '@/components/MyCell'
  import {validation} from "@/utils/validation"

  import { useGlobalStore } from '@/stores'
  import {getApiRoot} from "@/config";
  import upload from "@/utils/upload";
  const globalStore = useGlobalStore()

  const formState = ref({
    contract_id: '',
    house_id: '',
    title: '',
    content: '',
    images: []
  })
  const houseName = ref('')

  const contractList = ref([])
  const getContractList = () => {
    request.get({
      url: 'index/tenant',
    }).then(res => {
      contractList.value = res.data.contract.slice(0, 6)
      if (res.data.contract.length === 1) {
        const item = res.data.contract[0]
        houseName.value = item.name
        formState.value.contract_id = item.id
        formState.value.house_id = item.house_id
      }
    })
  }

  const handleChooseContract = () => {
    const options = contractList.value.map(v => {
      return v.name
    })
    if (!options || options.length === 0) {
      Taro.showToast({
        title: '您还没有房源',
        icon: 'error'
      })
      return
    }
    Taro.showActionSheet({
      itemList: options,
      success: (res) => {
        console.log(res.tapIndex)
        const item = contractList.value[res.tapIndex]
        houseName.value = item.name
        formState.value.contract_id = item.id
        formState.value.house_id = item.house_id
      },
      fail (res) {
        console.log(res.errMsg)
      }
    })

  }

  const onUploadImage = () => {
    console.log()
    upload.chooseImage(1).then(images => {
      images.map(img => {
        upload.uploadFile(img).then(url => {
          if (formState.value.images) {
            formState.value.images.push(url);
          } else {
            formState.value.images = [url];
          }
        })
      })
    })
  }

  const onSubmit = () => {

    validation(formState.value, {
      contract_id: {
        type: 'required',
        message: '请选择房源'
      },
      title: {
        type: 'required',
        message: '请输入报修项目',
      }
    }).then(() => {
      request.post({
        url: `tenant/repair/create`,
        data: {
          ... formState.value
        }
      }).then(res => {
        Taro.navigateBack()
      })
    }).catch((err) => {
      Taro.showToast({
        title: err,
        icon: 'error'
      })
    })

  }

  const onPreview = (img) => {
    Taro.previewImage({
      current: '', // 当前显示图片的http链接
      urls: [img] // 需要预览的图片http链接列表
    })
  }

  const onDel = (index) => {
    formState.value.images.splice(index, 1)
  }

  const onChooseTitle = () => {
    const options = [
      '电器',
      '家具',
      '门窗',
      '水电',
      '地板',
      '其它'
    ]
    Taro.showActionSheet({
      itemList: options,
      success: (res) => {
        formState.value.title = options[res.tapIndex]
      },
      fail (res) {
        console.log(res.errMsg)
      }
    })
  }

  useLoad(() => {
    getContractList()
  })

</script>
