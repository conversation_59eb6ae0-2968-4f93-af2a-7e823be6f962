<template>
  <view>
    <view class="container">
      <view class="repair-item" >
        <view class="house-name">
          <view class="estate-name">{{ detail.house.estate_name }}</view>
          <view class="fang">{{ detail.house.name }}</view>
        </view>
        <view class="repair-info">
          <view class="text-ellipsis">报修项目：{{detail.title}}</view>
          <view class="text-ellipsis">维护说明：{{detail.content}}</view>
          <view class="mt10">报修日期：{{detail.created_at}}</view>
        </view>
        <view class="mt20 flex flex-row">
          <view class="upload-image" v-for="img in detail.images" :key="img">
            <image class="image" :src="img" @tap="onPreview(img)" mode="widthFix"></image>
          </view>
        </view>
        <view class="repair-status">
          <text class="status1" v-if="detail.status === 1">待处理</text>
          <text class="status2" v-if="detail.status === 2">已处理</text>
        </view>
      </view>

    </view>

    <view class="footer-fixed" v-if="detail.status === 1">
      <view class="p20">
        <button class="btn-primary" @tap="handleConfirm">确认已处理</button>
      </view>
    </view>

  </view>
</template>

<script setup>
  import { ref } from 'vue'
  import Taro, { useDidShow, useDidHide, useReady, useLoad } from '@tarojs/taro'
  import request from '@/utils/request'
  import './repairDetail.scss'

  import MyIcon from '@/components/MyIcon'
  import MyCell from '@/components/MyCell'

  import { useGlobalStore } from '@/stores'
  import {getApiRoot} from "@/config";
  const globalStore = useGlobalStore()

  const detail = ref({
    house: {}
  })
  const id = ref(0)

  const onPreview = (img) => {
    Taro.previewImage({
      current: '', // 当前显示图片的http链接
      urls: [img] // 需要预览的图片http链接列表
    })
  }

  const handleConfirm = () => {
    request.post({
      url: 'tenant/repair/' + id.value + '/confirm'
    }).then(res => {
      detail.value.status = 2
    })
  }

  const getDetail = () => {
    request.get({
      url: 'repair/' + id.value
    }).then(res => {
      detail.value = res.data
    })
  }


  useLoad((options) => {
    id.value = options.id
    getDetail()
  })

</script>
