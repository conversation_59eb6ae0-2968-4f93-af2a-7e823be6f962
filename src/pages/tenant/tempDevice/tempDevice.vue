<template>
  <View class="container">

    <DeviceRender :items="items" @detail="onDetail"></DeviceRender>

  </View>
</template>

<script setup>
  import { ref } from 'vue'
  import Taro, { useDidShow, useDidHide, useReady, useLoad, usePullDownRefresh, useReachBottom} from '@tarojs/taro'
  import request from '@/utils/request'

  import MySearch from '@/components/MySearch'
  import MyIcon from '@/components/MyIcon'
  import DeviceRender from '@/components/DeviceRender'

  import { useGlobalStore } from '@/stores'
  const globalStore = useGlobalStore()

  definePageConfig({
    navigationBarTitleText: "设备列表",
    navigationBarBackgroundColor: "#1352FD",
    navigationBarTextStyle: "white",
  });

  usePullDownRefresh(() => {
    console.log('onPullDownRefresh')
  })

  const onDetail = (id) => {
    Taro.navigateTo({
      url: '/pages/tenant/deviceDetail/deviceDetail?id=' + id
    })
  }

  /** ----------------------接口数据-begin----------------------------------- */
  const items = ref([])
  const ids = ref([])

  const getList = () => {
    ids.value = []
    globalStore.tempDeviceList.map(d => {
      ids.value.push(d.id)
    })
    request.post({
      url: 'getDeviceListByIds',
      data: {
        ids: ids.value
      }
    }).then(res => {
      items.value = res.data
    })
  }
  /** ----------------------接口数据-end----------------------------------- */

  useLoad((options) => {
    getList()
  })

</script>

<style lang="scss">
.container {
  padding: 0 0 240px 0;

  .status-filter {
    background-color: #1352FD;
    padding: 0 13px 25px 13px;
    text-align: left;

    .tag {
      margin: 0 10px;
    }
  }

  .device-list {
    padding: 0 23px;
  }

  .device-item {
    background: #FFFFFF;
    box-shadow: 0rpx 4rpx 32rpx 0rpx rgba(182,190,201,0.38);
    border-radius: 20rpx;
    padding: 27px;
    margin-top: 23px;

    .house-box-title {
      font-size: 36rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #000000;
      line-height: 31rpx;
    }
  }

}

.device-top {
  margin-bottom: 25px;

  .device-name {
    font-size: 24rpx;
    font-family: OPPOSans;
    font-weight: 500;
    color: #000000;
  }
  .device-sn {
    font-size: 20rpx;
    font-family: OPPOSans;
    font-weight: 500;
    color: #000000;
  }
  .device-icon {
    width: 60rpx;
    height: 60rpx;
    background: #E9EFFA;
    border-radius: 10rpx;
    margin-right: 12px;
    text-align: center;
    padding: 2px 0;
  }
}
.device-info {
  background: #E9EFFA;
  border-radius: 20px;
  padding: 25px;
  text-align: center;

  .device-num {
    font-size: 36rpx;
    font-family: Bahnschrift;
    font-weight: 400;
    color: #000000;
    margin-bottom: 10px;
  }
  .device-lab {
    font-size: 22rpx;
    font-family: OPPOSans;
    font-weight: 500;
    color: #B6BEC5;
  }
}
</style>
