<template>
  <View class="container">
    <view>
      <MyCell prefix="头像" arrow>
        <template #content>
          <view class="text-right">
            <!-- <image :src="globalStore.userInfo.avatar" class="avatar-img"></image> -->
            <button open-type="chooseAvatar" plain="true"  class="avatar-wrapper"  @chooseavatar="onChooseAvatar"  @tap="chhoseImgHandel">
            <image
              :src="imgUrl"
              v-if="globalStore.userInfo.avatar"
              class="avatar-img"
            ></image>
            <view class="text-right color-low" v-else>暂无头像</view>
          </button>
          </view>
        </template>
      </MyCell>
      <MyCell prefix="昵称">
        <template #content><input type="nickname" :value="nickname" ref="input" @input="handleInputNickname" @change="handleInputNickname"  placeholder="暂无呢称" /></template>
      </MyCell>
    </view>
    <view class="p20">
        <button class="btn-primary" @tap="handleSubmit">保存</button>
    </view>
  </View>
</template>

<script setup>
  import { ref } from 'vue'
  import Taro, { useDidShow, useDidHide, useReady, useLoad } from '@tarojs/taro'
  import request from '@/utils/request'
  import './userInfo.scss'

  import { useGlobalStore } from '@/stores'

  import CustomNavbar from '@/components/CustomNavbar'

  import MyCell from '@/components/MyCell'
  import { getApiRoot } from '@/config'
  import { watch } from 'vue'

  const isAp = process.env.TARO_ENV === 'alipay'

  const nickname = ref('')

  const input = ref()


  const globalStore = useGlobalStore()
  const userInfo = ref(globalStore.getUserInfo())


  watch(() => globalStore.userInfo, (newVal) => { 
    nickname.value = globalStore.userInfo.nickname
    imgUrl.value = globalStore.userInfo.avatar
  })


  const handleInputNickname = (e) => {
		console.log(e.detail.value);
		nickname.value = e.detail.value
  }

  const handleSubmit = () => {
    request.get({
      url: 'user/editName',
      data: {
        name: nickname.value,
        avatar: imgUrl.value
      }
    }).then(() => {
      globalStore.getUserInfo()
      Taro.showToast({
        title: '修改成功',
      })
    })
  }

  const handleLogout = () => {
    globalStore.logout()
    Taro.navigateBack()
  }

  const imgUrl  = ref('')

  	// 获取头像
	const onChooseAvatar =  (res) => {
		console.log(res);
		// const url = await uploadFilePromise(res.detail.avatarUrl)
    Taro.uploadFile({
      url: getApiRoot("upload/image"),
      header: { Token: Taro.getStorageSync("token") },
      filePath: res.detail.avatarUrl,
      name: "file",
      formData: {},
      success: (res) => {
        const url = JSON.parse(res.data).data.url;
        console.log(url);
        imgUrl.value = url
      },
    });
	}

  	// 为了兼容支付宝
	const chhoseImgHandel = () => {
    console.log('chhoseImgHandel');
    if(!isAp) return
    Taro.chooseMedia({
      count: 1,
      mediaType: ["image"],
      sourceType: ["album", "camera"],
      success: (res) => {
        const tempFilePaths = res.tempFiles;
        console.log(tempFilePaths);
        tempFilePaths.map((file) => {
          Taro.uploadFile({
            url: getApiRoot("upload/image"),
            header: { Token: Taro.getStorageSync("token") },
            filePath: file.tempFilePath,
            name: "file",
            formData: {},
            success: (res) => {
              const url = JSON.parse(res.data).data.url;
              imgUrl.value = url
            },
          });
        });
      },
    });

	}


</script>
