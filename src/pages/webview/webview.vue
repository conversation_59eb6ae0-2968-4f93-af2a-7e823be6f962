<template>
  <web-view v-if="url" :src="url"></web-view>
</template>
<script setup>
import { ref } from "vue";
import Taro, {
  useDidShow,
  useDidHide,
  useReady,
  useLoad,
  usePullDownRefresh,
  useReachBottom,
} from "@tarojs/taro";
definePageConfig({
  navigationBarTitleText: "",
  navigationBarBackgroundColor: "#1352FD",
  navigationBarTextStyle: "white",
});

const url = ref('')

useLoad((options) => {
  url.value = decodeURIComponent(options.url)
})

</script>
