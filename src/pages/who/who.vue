<template>
  <view class="container">
    <view class="title">请选择您的身份 </view>
    <view class="who-list">
      <view class="who-item" @tap="handleChangeWho('business')">
        <view class="who-avatar"
          ><MyIcon icon="avatar-b" width="225rpx" height="250rpx"></MyIcon
        ></view>
        <view class="flex flex-v-center">
          <view class="who">我是房东</view
          ><view class="who-active" v-if="globalStore.who === 'business'"
            >当前身份</view
          >
        </view>
      </view>
      <view class="who-item" @tap="handleChangeWho('tenant')">
        <view class="who-avatar"
          ><MyIcon icon="avatar-t" width="225rpx" height="250rpx"></MyIcon
        ></view>
        <view class="flex flex-v-center">
          <view class="who">我是租客</view
          ><view class="who-active" v-if="globalStore.who === 'tenant'"
            >当前身份</view
          >
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from "vue";
import Taro, { useDidShow, useDidHide, useReady, useLoad } from "@tarojs/taro";
import "./who.scss";

import { useGlobalStore } from "@/stores";
import request from "@/utils/request";
import { role, active } from "@/utils/tabActive";

import { tenant, landlord, curent } from "@/utils/tabbar";

import MyIcon from "@/components/MyIcon";

const globalStore = useGlobalStore();

const handleChangeWho = (who) => {
  globalStore.setWho(who);
  globalStore.removeTTlToken()
  active.value = 0;
  Taro.getStorageSync('qrcodeSn') && Taro.removeStorageSync('qrcodeSn')

  if (who == "tenant") {
    Taro.setStorageSync("tablist", JSON.stringify(tenant));
    curent.value = tenant;
  } else {
    Taro.setStorageSync("tablist", JSON.stringify(landlord));
    curent.value = landlord;
  }
  globalStore.homeHasChange = true;
  request
    .post({
      url: "user/update",
      data: {
        type: who,
      },
    })
    .then((res) => {
      // list.value.splice(2, 1);
      // 缓存下页面防止闪烁
      Taro.switchTab({
        url: "/pages/my/my",
      });
      Taro.switchTab({
        url: "/pages/index/index",
      });
    });
};
</script>
