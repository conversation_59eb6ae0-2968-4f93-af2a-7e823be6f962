//    简单实现-settimeout
const throttle = (fn, context, interval) => {
  console.log(">>>>|--------15 ------- cmm  throttle", context, fn)
  let canRun = true; // 通过闭包保存一个标记
  if (typeof fn != "function") {
    console.log("fn 变量需要是函数")
    return;
  }
  interval = interval | 500
  console.log(interval)

  return function () {//匿名函数
    console.log(">>限流return")
    console.log(">>>args", arguments)
    if (!canRun) return; // 在函数开头判断标记是否为true，不为true则return
    canRun = false; // 立即设置为false
    setTimeout(() => { // 将外部传入的函数的执行放在setTimeout中
      fn.apply(context, arguments);
      // 最后在setTimeout执行完毕后再把标记设置为true(关键)表示可以执行下一次循环了。当定时器没有执行的时候标记永远是false，在开头被return掉
      canRun = true;
    }, interval);
  };
}

/**
 * 创建一个节流函数
 * @param {Function} func 要节流的函数
 * @param {number} wait 节流的等待时间，单位为毫秒
 * @param {Object} options 可选参数
 * @param {boolean} options.leading 是否在节流开始时立即执行一次函数
 * @param {boolean} options.trailing 是否在节流结束后执行最后一次被延迟的函数
 * @returns {Function} 节流后的函数
 */
function throttle2(func, wait, options = {}) {
  let timeoutId = null;
  let previous = 0;
  let context, args;

  const later = () => {
    previous = options.leading === false ? 0 : Date.now();
    timeoutId = null;
    func.apply(context, args);
  };

  const throttled = function (...params) {
    const now = Date.now();
    if (!previous && options.leading === false) previous = now;
    const remaining = wait - (now - previous);
    context = this;
    args = params;

    if (remaining <= 0 || remaining > wait) {
      if (timeoutId) {
        clearTimeout(timeoutId);
        timeoutId = null;
      }
      previous = now;
      func.apply(context, args);
    } else if (!timeoutId && options.trailing !== false) {
      timeoutId = setTimeout(later, remaining);
    }
  };

  throttled.cancel = () => {
    clearTimeout(timeoutId);
    timeoutId = null;
    previous = 0;
  };

  return throttled;
}

module.exports = {
  throttle: throttle,
  throttle2: throttle2
}
