import request from "@/utils/request";
import Taro from "@tarojs/taro";
import { clientId, clientSecret } from "@/config";
import { useGlobalStore } from "@/stores";
const globalStore = useGlobalStore();
import { AES_Encrypt, AES_Decrypt, MD5_Encrypt } from "@/utils/crypto";



// const clientId = "7946f0d923934a61baefb3303de4d132";
// const accessToken = "91d30a70ba5bdc691d1004a5e6daef94";
const accessToken = globalStore.ttlaccessToken || Taro.getStorageSync("ttlaccessToken") || '';

/**
 * 停止所有蓝牙操作
 */

export const handleStopAllOperations = () => {
  requirePlugin("myPlugin", ({ stopAllOperations }) => {
    // 停止所有蓝牙操作
    stopAllOperations().then(res => {
      // TODO 停止蓝牙操作返回
      console.log(res.errorCode == 0 ? "蓝牙操作已关闭" : `停止蓝牙操作失败：该接口无法打断正在连接的动作`);
    });
  });
}

/**
 * 获取云端随机密码
 */
export const getCloudPassword = async ({ lockId, keyboardPwdName, keyboardPwdType, startDate = new Date().getTime(), endDate }) => {
  return new Promise((resolve, reject) => {
    request
      .post({
        url: "/v3/keyboardPwd/get",
        LOCKYAPI: true,
        data: {
          lockId,
          clientId,
          accessToken: globalStore.ttlaccessToken,
          keyboardPwdName,
          keyboardPwdType,//3	在开始和结束时间内有效，必需在开始时间24小时内使用一次，1单次
          startDate,
          endDate,
          date: new Date().getTime(),
        },
      })
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject()
      })
  })
}

/**
 * 发钥匙
 */
export const sendKeyHandel = async ({ lockId, keyName, startDate, receiverUsername, endDate, phone, house_id }) => {
  return new Promise((resolve, reject) => {
    request
      .post({
        url: "/v3/key/send",
        LOCKYAPI: true,
        data: {
          lockId,
          clientId,
          accessToken: globalStore.ttlaccessToken,
          keyName,
          receiverUsername, //接收人用户名
          startDate: 0, //开始时间
          endDate: 0, //结束时间
          date: new Date().getTime(),
          keyRight: 1//是否授权管理员钥匙: 1-是、0-否，默认不授权
        },
      }).then((keyidRes) => {
        console.log(keyidRes, "keyid");
        request
          .post({
            url: "ttlock/bind",
            showToast: false,
            data: {
              lockId,
              keyId: keyidRes?.keyId,
              house_id,
              type: "2",
              mobile: phone
            },
          })
      })
  })
}

/**
 * 发钥匙
 */
export const sendKey = async ({ lockId, phone, house_id }) => {
  if(process.env.TARO_ENV === "alipay") return 
  return new Promise((resolve, reject) => {
    if (!lockId || !phone) return resolve();
    const option = {
      username: `${phone}shanzupotenant`,
      password: MD5_Encrypt(phone),
      date: new Date().getTime(),
    };
    request
      .post({
        url: "/v3/user/register",
        LOCKYAPI: true,
        data: {
          clientId,
          clientSecret,
          ...option,
        },
      })
      .then((res) => {
        console.log(res);
        sendKeyHandel({
          lockId,
          receiverUsername: `didfj_${phone}shanzupotenant`,
          keyName: `${"租客"}-${phone}`,
          phone,
          house_id
        })
          .then(() => {
            resolve();
          })
          .catch((err) => {
            resolve();
          });
      })
      .catch((err) => {
        resolve();
      });
  });
};

/**
 * 获取钥匙
 */
export const getKeyHandel = async ({ lockId }) => {
  if(process.env.TARO_ENV === "alipay") return 
  return new Promise((resolve, reject) => {
    request
      .post({
        url: "/v3/lock/listKey",
        LOCKYAPI: true,
        data: {
          clientId,
          lockId,
          accessToken: globalStore.ttlaccessToken,
          date: new Date().getTime(),
          pageNo: 1,
          pageSize: 20,
        },
      }).then(res => {
        resolve(res)
      })
  })
}

/**
 * 删除钥匙
 */
export const delKeyHandel = async ({ keyId }) => {
  if(process.env.TARO_ENV === "alipay") return 
  console.log('删除钥匙');
  return new Promise((resolve, reject) => {
    request
      .post({
        url: "/v3/key/delete",
        LOCKYAPI: true,
        data: {
          clientId,
          accessToken: globalStore.ttlaccessToken,
          date: new Date().getTime(),
          keyId,
        },
      })
  })
}


/**
 * 修改密码(自定义密码)
 */
export const modifyPwdHandel = async ({ keyboardPwd, oldKeyboardPwd, keyInfo, keyboardPwdId, lockId, keyboardPwdType, startDate, endDate }) => {
  return new Promise((resolve, reject) => {
    Taro.showLoading()
    requirePlugin("myPlugin", ({ modifyPasscode }) => {
      modifyPasscode({
        originalPasscode: oldKeyboardPwd,
        passcode: keyboardPwd,
        keyboardPwdType: !keyboardPwdType ? 2 : keyboardPwdType, //密码类型：2-永久，3-限期，不传默认为3
        startDate: keyboardPwdType == 3 ? startDate : 0,//0表示永久
        endDate: keyboardPwdType == 3 ? endDate : 0,//0表示永久
        lockData: keyInfo.lockData
      }).then(res => {
        console.log(res);
        if (res.errorCode == 0) {
          request
            .post({
              url: "/v3/keyboardPwd/change",
              LOCKYAPI: true,
              data: {
                lockId,
                // clientId: "42362269013d48b3bdaa85c7b83f6356",
                clientId,
                accessToken: globalStore.ttlaccessToken,
                keyboardPwdId,
                newKeyboardPwd: keyboardPwd, //新密码
                changeType: 1, //1-通过APP走蓝牙删除，不传默认1，必需先通过APP蓝牙删除后再调用该接口 2-通过网关或WiFi锁删除，如果是WiFi锁或有连接网关，则可以传2，直接调用该接口从锁里删除密码
                date: new Date().getTime(),
              },
            })
            .then((res) => {
              console.log(res);
              resolve(res);
            });
        } else {
          Taro.hideLoading();
          Taro.showToast({
            title: `修改失败：${res.errorMsg}`,
            icon: "none",
            duration: 5000,
          });
        }
      })
    });
  });
};

/**
 * 删除锁密码
 */
export const delPwdHandel = async ({ keyboardPwd, lockData, keyboardPwdId, lockId }) => {
  return new Promise((resolve, reject) => {
    requirePlugin("myPlugin", ({ deletePasscode }) => {
      deletePasscode({
        passcode: keyboardPwd,
        lockData: lockData
      }).then(res => {
        if (res.errorCode == 0) {
          console.log(`密码已删除, 正在上传, 操作时间: ${Date.now()}ms.`);
          request
            .post({
              url: "/v3/keyboardPwd/delete",
              LOCKYAPI: true,
              data: {
                lockId: lockId,
                clientId,
                accessToken: globalStore.ttlaccessToken,
                keyboardPwdId,
                deleteType: 1, //1-通过APP走蓝牙删除，不传默认1，必需先通过APP蓝牙删除后再调用该接口 2-通过网关或WiFi锁删除，如果是WiFi锁或有连接网关，则可以传2，直接调用该接口从锁里删除密码
                date: new Date().getTime(),
              },
            })
            .then((res) => {
              console.log(res);
              resolve()
            });
        } else {
          Taro.hideLoading();
          console.log(`密码删除失败,${res.errorMsg} 操作时间: ${Date.now()}ms.`);
          reject(`密码删除失败,${res.errorMsg} 操作时间: ${Date.now()}ms.`)
        }
      });
    });
  })
}

/**
 * 创建密码
 */
export const createPwdHandel = async ({ keyboardPwd, keyboardPwdName, keyInfo, keyboardPwdType, startDate, endDate }) => {
  return new Promise((resolve, reject) => {
    Taro.showLoading()
    requirePlugin("myPlugin", ({ createCustomPasscode }) => {
      // 添加自定义密码
      createCustomPasscode({
        passcode: keyboardPwd,
        startDate: 0,//0表示永久
        endDate: 0,//0表示永久
        lockData: keyInfo.lockData
      }).then(res => {
        console.log(res);
        if (res.errorCode == 0) {
          request
            .post({
              url: "/v3/keyboardPwd/add",
              LOCKYAPI: true,
              data: {
                lockId: keyInfo.lockId,
                // clientId: "42362269013d48b3bdaa85c7b83f6356",
                clientId,
                accessToken: globalStore.ttlaccessToken,
                keyboardPwd, //密码
                keyboardPwdName, //名称
                keyboardPwdType: !keyboardPwdType ? 2 : keyboardPwdType, //密码类型：2-永久，3-限期，不传默认为3
                addType: 1, //1-通过APP走蓝牙删除，不传默认1，必需先通过APP蓝牙删除后再调用该接口 2-通过网关或WiFi锁删除，如果是WiFi锁或有连接网关，则可以传2，直接调用该接口从锁里删除密码
                startDate: keyboardPwdType == 3 ? startDate : undefined,
                endDate: keyboardPwdType == 3 ? endDate : undefined,
                date: new Date().getTime(),
              },
              showLoading: false
            })
            .then((res) => {
              console.log(res);
              Taro.hideLoading();
              resolve(res);
            });
        } else {
          Taro.hideLoading();
          Taro.showToast({
            title: `自定义密码添加失败：${res.errorMsg}`,
            icon: "none",
            duration: 5000,
          });
        }
      })
    });

  });
};

/**
 * 读取操作记录并上传
 */
export const toReadRecord = ({ keyInfo }) => {
  console.log("读取操作记录并上传");
  return new Promise((resolve, reject) => {
    requirePlugin("myPlugin", ({ getOperationLog }) => {
      // 读取智能锁操作记录
      getOperationLog({
        /* 读取操作记录方式 1 -全部, 2 -最信 */
        logType: 1,
        lockData: keyInfo.lockData,
      }).then((res) => {
        if (res.errorCode === 0) {
          const logList = res.log ? JSON.parse(res.log) : [];
          resolve(logList)
          if (logList.length > 0) {
            Taro.showLoading({ title: "正在上传操作记录" });
            request
              .post({
                url: "/v3/lockRecord/upload",
                LOCKYAPI: true,
                data: {
                  lockId: keyInfo.lockId,
                  records: res.log,
                  // clientId: "42362269013d48b3bdaa85c7b83f6356",
                  clientId,
                  accessToken: globalStore.ttlaccessToken,
                  date: new Date().getTime(),
                },
              })
              .then((res) => {
                console.log(res);
                Taro.hideLoading();
              })
              .catch((err) => {
                Taro.hideLoading();
              });
          } else Taro.hideLoading();
        } else {
          Taro.hideLoading();
        }
      });
    });
  })
};

/**
 * 获取云端日志
 */
export const getCloudLog = ({ keyInfo, pageNo = 1, pageSize = 20, endDate, startDate }) => {
  return new Promise((resolve, reject) => {
    request
      .post({
        url: "/v3/lockRecord/list",
        LOCKYAPI: true,
        data: {
          lockId: keyInfo.lockId,
          clientId,
          accessToken: globalStore.ttlaccessToken,
          date: new Date().getTime(),
          pageNo,
          pageSize,
          startDate,
          endDate
        },
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
  })
}

/**
 * 获取锁的密码列表
 */
// export const getLockPwdList = ({ keyInfo }) => {
//   return new Promise((resolve, reject) => {
//     request
//       .post({
//         url: "/v3/lock/listKeyboardPwd",
//         LOCKYAPI: true,
//         data: {
//           lockId: keyInfo.lockId,
//           // clientId: "42362269013d48b3bdaa85c7b83f6356",
//           clientId,
//           accessToken: globalStore.ttlaccessToken,
//           pageNo: 1,
//           pageSize: 200,
//           orderBy: "1", //排序方式：0-按名称正序、1-按生成时间倒序、2-按名称倒序
//           date: new Date().getTime(),
//         },
//       })
//       .then((res) => {
//         console.log(res);
//         resolve(res.list);
//       });
//   });
// };

export const getLockPwdList = ({ keyInfo }) => {
  let results = [];
  const pagesize = 200

  const fetchPage = (pageNo = 1) => {
    return new Promise((resolve, reject) => {
      request
        .post({
          url: "/v3/lock/listKeyboardPwd",
          LOCKYAPI: true,
          data: {
            pageNo: pageNo,
            pageSize: pagesize,
            clientId,
            lockId: keyInfo.lockId,
            accessToken: globalStore.ttlaccessToken,
            orderBy: "1", //排序方式：0-按名称正序、1-按生成时间倒序、2-按名称倒序
            date: new Date().getTime(),
          },
        })
        .then((res) => {
          if (res) {
            const { list, total } = res;

            // Add current page data to the result array
            results = results.concat(list);

            // Check if we need to fetch more pages
            const totalPages = Math.ceil(total / pagesize);

            if (pageNo < totalPages) {
              // Fetch the next page
              resolve(fetchPage(pageNo + 1));
            } else {
              // All pages fetched, resolve with the complete data
              resolve(results);
            }
          } else {
            reject('No data received from the API.');
          }
        })
        .catch((error) => {
          reject(error);
        });
    });
  };

  // Start fetching from page 1
  return fetchPage();
};

/**
 * 获取锁的详细信息
 */
export const getLockDetail = ({ keyInfo }) => {
  return new Promise((resolve, reject) => {
    request
      .get({
        url: "/v3/lock/detail",
        LOCKYAPI: true,
        data: {
          lockId: keyInfo.lockId,
          // clientId: "42362269013d48b3bdaa85c7b83f6356",
          clientId,
          accessToken: globalStore.ttlaccessToken,
          date: new Date().getTime(),
        },
      })
      .then((res) => {
        console.log(res);
        resolve(res);
      });
  });
};

/**
 * 获取钥匙列表
 */
export const getLockList = () => {
  let results = [];
  const pagesize = 20
  console.log('0000');
  // let pageNo = 1
  // return new Promise((resolve, reject) => {
  //   request
  //     .post({
  //       url: "/v3/key/list",
  //       LOCKYAPI: true,
  //       data: {
  //         pageNo: 1,
  //         pageSize: pagesize,
  //         // clientId: "42362269013d48b3bdaa85c7b83f6356",
  //         clientId,
  //         accessToken: globalStore.ttlaccessToken,
  //         date: new Date().getTime(),
  //       },
  //     })
  //     .then((res) => {
  //       resolve(res)
  //       if (res) {
  //         const { list, total } = res;

  //         // Add current page data to the result array
  //         results = results.concat(list);

  //         // Check if we need to fetch more pages
  //         const totalPages = Math.ceil(total / pagesize);

  //         if (pageNo < totalPages) {
  //           // Fetch the next page
  //           resolve(fetchPage(pageNo + 1));
  //         } else {
  //           // All pages fetched, resolve with the complete data
  //           resolve(results);
  //         }
  //       } else {
  //         reject('No data received from the API.');
  //       }
  //     });
  // })

  const fetchPage = (pageNo = 1) => {
    return new Promise((resolve, reject) => {
      if(process.env.TARO_ENV === 'alipay') return resolve()
      request
        .post({
          url: "/v3/key/list",
          LOCKYAPI: true,
          data: {
            pageNo: pageNo,
            pageSize: pagesize,
            clientId,
            accessToken: globalStore.ttlaccessToken,
            date: new Date().getTime(),
          },
        })
        .then((res) => {
          if (res) {
            const { list, total } = res;

            // Add current page data to the result array
            results = results.concat(list);

            // Check if we need to fetch more pages
            const totalPages = Math.ceil(total / pagesize);

            if (pageNo < totalPages) {
              // Fetch the next page
              resolve(fetchPage(pageNo + 1));
            } else {
              // All pages fetched, resolve with the complete data
              resolve({
                list: results,
              });
            }
          } else {
            reject('No data received from the API.');
          }
        })
        .catch((error) => {
          reject(error);
        });
    });
  };

  // Start fetching from page 1
  return fetchPage();
}



/**
 * 扫描蓝牙设备
 */
export const startScanBleDevice = () => {
  return new Promise((resolve, reject) => {
    requirePlugin("myPlugin", ({ startScanBleDevice }) => {
      // 开启蓝牙设备扫描
      startScanBleDevice((lockDevice, lockList) => {
        // TODO 成功扫描到设备
        console.log(lockList, "扫描到设备lockList");
        console.log("蓝牙设备扫描中");
        resolve(lockList);
      }, (err) => {
        reject(err);
        Taro.showToast({
          title: `蓝牙扫描开启失败：${err.errorMsg}`,
          icon: "none",
          duration: 5000,
        })
      });
    });
  })
}

/**
 * 停止扫描蓝牙设备
 */
export const stopScanBleDevice = () => {
  return new Promise((resolve, reject) => {
    requirePlugin("myPlugin", ({ stopScanBleDevice }) => {
      // 关闭蓝牙设备扫描
      stopScanBleDevice().then(res => {
        // TODO 关闭蓝牙设备扫描返回
        if (res.errorCode == 0) {
          resolve(res);
        } else {
          reject(res);
          console.log(`关闭蓝牙扫描失败：${res.errorMsg}`);
          Taro.showToast({
            title: `关闭蓝牙扫描失败：${res.errorMsg}`,
            icon: "none",
            duration: 5000,
          })
        }
      });
    });
  })
}

/**
 * 重置锁
 */
export const handleResetLock = ({ lockData, lockId }) => {
  return new Promise((resolve, reject) => {
    requirePlugin("myPlugin", ({ resetLock }) => {
      // 重置智能锁
      resetLock({ lockData }).then(res => {
        console.log(res, '智能锁重置结果');
        if (res.errorCode == 0) {
          console.log(`本地智能锁已重置正在上传服务器，请重新添加智能锁`);
          request.post({
            url: "/v3/lock/delete",
            LOCKYAPI: true,
            data: {
              lockId,
              clientId,
              accessToken: globalStore.ttlaccessToken,
              date: new Date().getTime(),
            },
          }).then((resApi) => {
            console.log(resApi, "智能锁重置结果");
            if (resApi.errcode && resApi.errcode !== 0) {
              reject()
              // 失败
              Taro.showToast({
                title: `智能锁重置失败：${resApi?.errmsg}`,
                icon: "none",
                duration: 5000,
              })
              console.log("智能锁重置失败", resApi?.errmsg);
            } else {
              // 成功
              // Taro.showToast({
              //   title: "智能锁已解绑",
              //   icon: "none",
              //   duration: 5000,
              // })
              console.log('重置成功');
              resolve(resApi)
            }
          })
          resolve()
        } else {
          reject()
          console.log(`智能锁重置失败，请长按重置键进行设备重置：${res.errorMsg}`);
          Taro.showToast({
            title: `智能锁重置失败，请长按重置键进行设备重置：${res.errorMsg}`,
            icon: "none",
            duration: 5000,
          })
        }
      });
    });
  })
}

/**
 * 初始化锁
 * deviceFromScan 锁信息 lockList中的item
 * @description 点击扫描的设备列表初始化的时候触发
 */
export const initLockSdk = ({ deviceFromScan, accessToken, lockAlias }) => {
  console.log("accessToken", accessToken);
  return new Promise((resolve, reject) => {
    requirePlugin("myPlugin", ({ getLockVersion, initLock }) => {
      // 更新智能锁版本信息
      getLockVersion({ deviceFromScan }).then(res => {
        if (res.errorCode == 0) {
          // TODO 更新版本信息成功
          console.log("智能锁版本信息已更新，正在初始化设备");
          // 调用添加锁接口, serverTime传入服务器时间
          initLock({ deviceFromScan }).then(result => {
            if (result.errorCode == 0) {
              // 设备已成功初始化，请调用开放平台接口上传lockData
              console.log("设备已成功初始化，正在调用开放平台接口上传锁数据");
              request.post({
                url: "/v3/lock/initialize",
                LOCKYAPI: true,
                data: {
                  lockData: result.lockData,
                  clientId,
                  accessToken: `${accessToken}`,
                  date: new Date().getTime(),
                  nbInitSuccess: 1,
                  lockAlias
                },
              }).then((resApi) => {
                console.log(resApi, "智能锁数据上传结果");
                if (resApi.errcode && resApi.errcode !== 0) {
                  reject()
                  // 失败
                  Taro.showToast({
                    title: `初始化智能锁失败：${resApi?.errmsg}`,
                    icon: "none",
                    duration: 5000,
                  })
                  console.log("智能锁数据上传失败, 正在重置智能锁");
                  setTimeout(() => {
                    handleResetLock({ lockData: result.lockData, lockId: resApi.lockId });
                  }, 1500)
                } else {
                  // 成功
                  Taro.showToast({
                    title: "智能锁已添加",
                    icon: "none",
                    duration: 5000,
                  })
                  resolve(resApi)
                }
              }).catch(err => {
                reject()
                Taro.showToast({
                  title: `初始化智能锁失败`,
                  icon: "none",
                  duration: 5000,
                })
                console.log("智能锁数据上传失败, 正在重置智能锁");
                handleResetLock({ lockData: result.lockData, lockId: resApi.lockId });
              })
            }
            else {
              Taro.showToast({
                title: `初始化智能锁失败：${result.errorMsg}`,
                icon: "none",
                duration: 5000,
              })
              console.log(`初始化智能锁失败：${result.errorMsg}`);
            }
          })
        }
        else {
          Taro.showToast({
            title: `更新智能锁版本信息失败：${res.errorMsg}`,
            icon: "none",
            duration: 5000,
          })
          console.log(`更新智能锁版本信息失败：${res.errorMsg}`);
        }
      });
    });
  })
}

/**
 * 获取管理员密码
 */
export const getAdminPasscode = ({ keyInfo }) => {
  return new Promise((resolve, reject) => {
    Taro.showLoading({ title: "" });
    console.log("正在查询管理员密码");
    const start = Date.now();
    requirePlugin("myPlugin", ({ getAdminPasscode }) => {
      getAdminPasscode({ lockData: keyInfo.lockData }).then(result => {
        if (result.errorCode === 0) {
          console.log(`查询管理员密码成功, 密码: ${result.passcode}, 操作时间：${Date.now() - start}ms.`);
          resolve(result.passcode);
          console.log(`正在上传服务器中, 密码: ${result.passcode}`);
          request.post({
            url: "/v3/lock/changeAdminKeyboardPwd",
            LOCKYAPI: true,
            data: {
              clientId,
              accessToken: globalStore.ttlaccessToken,
              date: new Date().getTime(),
              lockId: keyInfo.lockId, // 智能锁ID
              password: result.passcode, // 管理员密码
            },
          }).then(resApi => {
            if (resApi.errcode && resApi.errcode !== 0) {
              // 失败
              Taro.showToast({
                title: `服务器上传失败：${resApi.description || resApi.errmsg}`,
                icon: "none",
                duration: 5000,
              })
            } else {
              console.log("服务器上传成功");
            }
          }).catch(err => {
            Taro.showToast({
              title: `服务器上传失败：${err.description || err.errmsg}`,
              icon: "none",
              duration: 5000,
            })
          }
          )
        } else {
          Taro.hideLoading();
          console.log(`查询管理员密码失败, 错误码：${result.errorCode}, 错误信息：${result.errorMsg}, 操作时间：${Date.now() - start}ms.`);
        }
      })
    });
  })
}

/**
 * 修改管理员密码
 */
export const changeAdminPasscode = ({ keyInfo, newPasscode }) => {
  return new Promise((resolve, reject) => {
    requirePlugin("myPlugin", ({ modifyAdminPasscode }) => {
      // 设置管理员密码
      modifyAdminPasscode({
        newPasscode: newPasscode,
        lockData: keyInfo.lockData
      }).then(res => {
        if (res.errorCode === 0) {
          Taro.showLoading();
          console.log("上传服务器中");
          request.post({
            url: "/v3/lock/changeAdminKeyboardPwd",
            LOCKYAPI: true,
            data: {
              clientId,
              accessToken: globalStore.ttlaccessToken,
              date: new Date().getTime(),
              lockId: keyInfo.lockId, // 智能锁ID
              password: newPasscode, // 管理员密码
            },
          }).then(resApi => {
            if (resApi.errcode && resApi.errcode !== 0) {
              // 失败
              Taro.showToast({
                title: `服务器上传失败：${resApi.description || resApi.errmsg}`,
                icon: "none",
                duration: 5000,
              })
              reject()
            } else {
              resolve()
              console.log("服务器上传成功");
            }
          })
        } else {
          Taro.hideLoading();
          console.log(`修改管理员密码失败, 错误码：${res.errorCode}, 错误信息：${res.errorMsg}`);
          Taro.showToast({
            title: `管理员密码设置失败：${res.errorMsg}`,
            icon: "none",
            duration: 5000,
          })
        }
      })
    });
  })
}

export const getAllValidPasscode = ({ keyInfo }) => {
  Taro.showLoading({ title: "" });
  console.log("正在读取锁内所有密码");
  requirePlugin("myPlugin", ({ getAllValidPasscode }) => {
    // 读取所有有效密码
    getAllValidPasscode({ lockData: keyInfo.lockData }).then(res => {
      if (res.errorCode == 0) {
        Taro.hideLoading();
        Taro.showToast({
          title: `读取密码成功`,
          icon: "none",
          duration: 3000,
        })
        console.log(`共读取到 ${res.keyboardPwdList.length} 条密码`);
      } else {
        Taro.hideLoading();
        Taro.showToast({
          title: `读取有效密码失败：${res.errorMsg}`,
          icon: "none",
          duration: 5000,
        })
        console.log(`读取有效密码失败, 错误码：${res.errorCode}, 错误信息：${res.errorMsg}`);
      }
    })
  })
}


export function getUnlockStatusMessage (statusCode) {
  const statusMessages = {
    1: "蓝牙开锁",
    4: "密码开锁成功",
    5: "在锁上修改密码",
    6: "在锁上删除一个密码",
    7: "密码开锁失败，未知密码",
    8: "清空密码",
    9: "密码被挤掉",
    10: "带删除功能密码第一次开锁，之前密码被清空",
    11: "密码开锁失败，密码已过期或未生效",
    12: "密码开锁失败，存储容量不足",
    13: "密码开锁失败—密码在黑名单",
    14: "门锁重新上电启动了",
    15: "添加IC卡成功",
    16: "清空IC卡",
    17: "IC卡开门成功",
    18: "删除单个IC卡",
    19: "Bong手环开门成功",
    20: "指纹开锁成功",
    21: "指纹添加成功",
    22: "指纹开门失败，已过期或者未生效",
    23: "删除单个指纹",
    24: "清空指纹",
    25: "IC卡开门失败，已过期或者未生效",
    26: "蓝牙闭锁",
    27: "机械钥匙开锁",
    28: "网关开锁",
    29: "非法开锁（比如车位锁被强制打开）",
    30: "门磁合上（关门）",
    31: "门磁打开（开门）",
    32: "从内部打开门",
    33: "指纹关锁",
    34: "密码关锁",
    35: "IC卡关锁",
    36: "机械钥匙关锁",
    37: "用APP按键控制锁（上升、下降、停止、上锁），多用于卷帘门",
    38: "密码开锁失败，门反锁了",
    39: "IC卡开锁失败，门锁反锁了",
    40: "指纹开锁失败，门反锁了",
    41: "App开锁失败，门反锁了",
    42: "邮局本地邮件",
    43: "邮局外地邮件",
    44: "防撬报警",
    45: "超时自动闭锁（比如时间段常开模式下超时闭锁，超声波传感器超时自动闭锁）",
    46: "开锁按键开锁",
    47: "闭锁按键闭锁",
    48: "系统被锁定（如输入密码连续错误5次）",
    49: "酒店卡开锁",
    50: "高温开锁",
    51: "IC卡开锁失败，黑名单卡",
    52: "用APP锁定锁",
    53: "用密码锁定锁",
    54: "车离开（车位锁才有）",
    55: "遥控开、闭锁",
    56: "无线键盘电量",
    57: "二维码开锁成功",
    58: "二维码开锁失败，过期",
    59: "开启反锁",
    60: "关闭反锁",
    61: "二维码闭锁成功",
    62: "二维码开锁失败，门已反锁",
    63: "常开时间段自动开锁",
    64: "门未关报警",
    65: "开锁超时",
    66: "闭锁超时",
    67: "3D人脸开锁成功",
    68: "3D人脸开锁失败-门反锁了",
    69: "3D人脸闭锁",
    70: "注册3D人脸成功",
    71: "3D人脸开门失败—已过期或者未生效",
    72: "删除人脸成功",
    73: "清空人脸成功",
    74: "IC卡开锁失败—CPU安全信息错",
    75: "App授权按键开锁成功",
    76: "网关授权按键开锁成功",
    77: "双重认证蓝牙开锁验证成功，等待第二用户",
    78: "双重认证密码开锁验证成功，等待第二用户",
    79: "双重认证指纹开锁验证成功，等待第二用户",
    80: "双重认证IC卡开锁验证成功，等待第二用户",
    81: "双重认证人脸卡开锁验证成功，等待第二用户",
    82: "双重认证遥控开锁验证成功，等待第二用户",
    83: "双重认证掌静脉开锁验证成功，等待第二用户",
    84: "掌静脉开锁成功",
    85: "掌静脉开锁失败-门反锁了",
    86: "掌静脉闭锁",
    87: "注册掌静脉成功",
    88: "掌静脉开门失败—已过期或者未生效",
    89: "删除掌静脉成功",
    90: "清空掌静脉成功",
    91: "IC卡开锁失败",
    92: "管理员密码开锁",
    93: "添加密码成功（自定义密码、键盘添加密码）"
  };

  return statusMessages[statusCode] || "未知状态码";
}


