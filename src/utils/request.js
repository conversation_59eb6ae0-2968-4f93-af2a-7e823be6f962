import Taro from '@tarojs/taro'
import { getApiRoot } from "@/config";
import { useGlobalStore } from '@/stores'

const globalStore = useGlobalStore()

import { active } from './tabActive'
import { curent } from './tabbar'


const _request = (options) => {
  return new Promise((resolve, reject) => {
    if (options.LOCKYAPI === true) {
      options.url = 'https://cnapi.sciener.com' + options.url
      options.header = {
          "Content-Type": "application/x-www-form-urlencoded", // 设置请求头
      }
    } else {
      if (options.ISCUSTOM === true) {
        options.url = getApiRoot(options.url).replace('/mp/', '')
      } else {
        options.url = getApiRoot(options.url)
      }
    }
    options.header = options.header || {}
    options.fail = (res) => {
      if (options.showLoading !== false) {
        Taro.hideLoading()
      }
      if (!res.data) {
        if(globalStore.isErrorShow) return
        globalStore.setErroShow(true)
        // Taro.showModal({
        //   title: '网络异常，请稍后再试',
        //   content: '可能本机设备时间有误或者网络断开请设置为网络时间并检查手机是否可连接互联网后重启小程序！',
        //   success: () => {
        //     globalStore.setErroShow(false)
        //   }
        // })
      }
      if (res?.data?.code === 403) {
        Taro.showToast({
          title:'过期'
        })
        Taro.clearStorageSync('token')
        globalStore.setIsLogin(false)
        globalStore.logout()
        return Taro.switchTab({
          url: '/pages/my/my', success () {
            Taro.showToast({
              title: '登录过期，请重新登录',
              icon: 'none'
            })
            active.value = curent.value.length - 1
          }
        })
      }
      if (res?.data?.code != 200 && options.showToast !== false) {
        Taro.showToast({
          title: res?.data?.message || '糟糕，请求失败了',
          icon: 'none'
        })
      }
      reject(res)
    }
    options.success = (res) => {
      // console.log(options,"请求参数");
      if (options.showLoading !== false) {
        Taro.hideLoading()
      }
      if (res.statusCode !== 200) {
        if (res?.data?.code === 403) {
          Taro.showToast({
            title:'过期'
          })
          Taro.clearStorageSync('token')
          globalStore.setIsLogin(false)
          return Taro.switchTab({
            url: '/pages/my/my', success () {
              Taro.showToast({
                title: '登录过期，请重新登录',
                icon: 'none'
              })
              active.value = curent.value.length - 1
            }
          })
        }
        if (res.data?.code != 200 && options.showToast !== false) {
          console.log(res,options,"请求失败参数");
          Taro.showToast({
            title: res.data?.message || '糟糕，请求失败了',
            icon: 'none'
          })
        }
        reject(res)
        return
      }
      resolve(res.data)
    }
    options.header.Token = Taro.getStorageSync('token')
    if (options.showLoading !== false) {
      Taro.showLoading({ title: '加载中...' })
    }
    Taro.request(options)
  })
}

const request = {
  get: (options) => {
    options.method = 'GET'
    return _request(options)
  },
  post: (options) => {
    options.method = 'POST'
    return _request(options)
  },
  delete: (options) => {
    options.method = 'DELETE'
    return _request(options)
  }
}

export default request
