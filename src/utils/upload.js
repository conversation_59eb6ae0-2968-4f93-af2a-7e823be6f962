import Taro from '@tarojs/taro'
import { getApiRoot } from "@/config";

const _chooseImageWx = (count) => {
  return new Promise((resolve, reject) => {
    Taro.chooseMedia({
      count: 1,
      mediaType: ["image"],
      sourceType: ["album", "camera"],
      success: (res) => {
        const tempFilePaths = res.tempFiles;
        let images = []
        tempFilePaths.map(img => {
          images.push(img.tempFilePath)
        })
        resolve(images)
      },
    });
  })
}

const _chooseImage = (count) => {
  return new Promise((resolve, reject) => {
    Taro.chooseImage({
      count: count,
      sizeType: ['original', 'compressed'],
      sourceType: ["album", "camera"],
      success: (res) => {
        console.log(res, 'chooseImage')
        let images = []
        res.tempFiles.map(img => {
          images.push(img.path)
        })
        resolve(images)
      },
    });
  })
}

const upload = {
  chooseImage: (count) => {
    if (process.env.TARO_ENV === 'weapp') {
      return _chooseImageWx(count)
    } else {
      return _chooseImage(count)
    }
  },
  uploadFile: (path) => {
    return new Promise((resolve, reject) => {
      Taro.uploadFile({
        url: getApiRoot("upload/image"),
        header: { Token: Taro.getStorageSync("token") },
        filePath: path,
        name: "file",
        formData: {},
        success: (result) => {
          const url = JSON.parse(result.data).data?.url;
          resolve(url)
        },
        fail: (e) => {
          reject(e)
        }
      });
    })
  }
}

export default upload
