
export const validation = (data, rules) => {
    return new Promise((resolve, reject) => {
        for (let rule in rules) {
            if (rules[rule].type === 'required') {
                if (!data[rule]) {
                    reject(rules[rule].message || '表单填写不全')
                }
            }
            if (rules[rule].type === 'number') {
                if (data[rule] < rules[rule].min) {
                    reject(rules[rule].message || '数字不符合要求')
                }
            }
        }
        resolve()
    })
}