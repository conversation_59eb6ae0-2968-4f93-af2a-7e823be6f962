import Taro from "@tarojs/taro";
import { useGlobalStore } from "@/stores";
import { onBLECharacteristicValueChange } from "./waterDevice";
import { ab2hex, hexToArrayBuffer, int2hex2 } from '.';
const globalStore = useGlobalStore();


// 发送指定
const sendCmd = (buffer) => {
  console.log(ab2hex(buffer), 'cmd')
  return new Promise((resolve, reject) => {
    Taro.writeBLECharacteristicValue({
      // 这里的 deviceId 需要在 getBluetoothDevices 或 onBluetoothDeviceFound 接口中获取
      deviceId: globalStore.waterDeviceBleInfo?.deviceId,
      // 这里的 serviceId 需要在 getBLEDeviceServices 接口中获取
      serviceId: globalStore.waterDeviceBleInfo?.characteristicId,
      // 这里的 characteristicId 需要在 getBLEDeviceCharacteristics 接口中获取
      characteristicId: globalStore.waterDeviceBleInfo?.characteristicId,
      // 这里的value是ArrayBuffer类型
      value: buffer,
      success (res) {
        console.log('writeBLECharacteristicValue success', res.errMsg)
        resolve()
      },
      fail (res) {
        Taro.showToast({
          title: '发送指令失败',
          icon: 'error'
        })
        console.log('发送指令失败', res)
        reject(res)
        if (res.errno == 1509003) {
          // Taro.showModal({
          //   showCancel: false,
          //   title: '连接断开请重新连接',
          //   success () {
          //   }
          // })
        } else if (res.errno == 1500102) {
          // Taro.showModal({
          //   showCancel: false,
          //   title: '请打开蓝牙重新连接',
          //   success () {
          //   }
          // })
        }
      }
    })
  })
}

/**
 * 查询水表
 */
export const queryWaterBleDevice = () => {
  return new Promise((resolve, reject) => {
    const hex = `0C`;
    console.log(hex, "查询水表(hex)");
    sendCmd(hexToArrayBuffer(hex))
      .then(() => {
        onBLECharacteristicValueChange().then((resObj) => {
          console.log(resObj, "resObj");
          const { hexStr, byteArray } = resObj;
          const hexStrUpper = hexStr.toUpperCase();
          let command = hexStrUpper.slice(0, 2); //命令字
          let code = hexStrUpper.slice(2, 2); //是否成功 00 成功 01 失败
          if (command == "0C" && code == "00") {
            const obj = {
              water: (parseInt(hexStrUpper.slice(4, 12), 16) / 1000).toFixed(2), //水表剩余水量
              totalWater: (
                parseInt(hexStrUpper.slice(12, 20), 16) / 1000
              ).toFixed(2), //累计用水量
              status: parseInt(hexStrUpper.slice(28, 30), 16).toFixed(0), //闸门状态 1-阀开， 2-阀关
            };
            console.log(obj, "obj");
            resolve(obj);
          }
        });
      })
      .catch((e) => {
        reject(e);
        console.log("抄水表失败断开连接：", e);
        globalStore.setGlobDialog({
          show: true,
          type: "waterConnectionFailed",
        });
      })
  });
};

/**
 * 开关闸
 * status 1-阀开， 2-阀关
 */
export const openOrCloseWaterBleDevice = ({
  status,
}) => {
  return new Promise((resolve, reject) => {
    const hex = `0D${status == 1 ? "01" : "02"}`; //1-阀开， 2-阀关
    console.log(hex, "开关闸(hex)");
    sendCmd(hexToArrayBuffer(hex))
      .then(() => {
        onBLECharacteristicValueChange().then((resObj) => {
          console.log(resObj, "resObj");
          const { hexStr, byteArray } = resObj;
          const hexStrUpper = hexStr.toUpperCase();
          let command = hexStrUpper.slice(0, 2); //命令字
          let code = hexStrUpper.slice(2, 2); //是否成功 00 成功 01 失败
          if (command == "0D" && code == "00") {
            const obj = {
              status: parseInt(hexStrUpper.slice(4, 6), 16).toFixed(0), //闸门状态 1-阀开， 2-阀关
            };
            console.log(obj, "obj");
            resolve(obj);
          }
        });
      })
      .catch((e) => {
        console.log("开关闸失败：", e);
        reject(e);
        globalStore.setGlobDialog({
          show: true,
          type: "waterConnectionFailed",
        });
      })
  });
};

/**
 * 充值
 */
export const rechargeWaterBleDevice = ({
  du
}) => {
  return new Promise((resolve, reject) => {
    let hex = '0A01' + int2hex2(du * 1000, 8)
    sendCmd(hexToArrayBuffer(hex)).then(_ => {
      console.log('充值成功，du：' + du);
      resolve()
      onBLECharacteristicValueChange().then((resObj) => {
        console.log(resObj, "resObjRechargeWaterBleDevice");
        const { hexStr, byteArray } = resObj;
        const hexStrUpper = hexStr.toUpperCase();
        let command = hexStrUpper.slice(0, 2); //命令字
        let code = hexStrUpper.slice(2, 2); //是否成功 00 成功 01 失败
        if (command == "0A" && code == "00") {
          console.log("充值成功");
          // resolve(obj);
        }
      });
      // request.post({
      //   url: 'device/recharge',
      //   data: {
      //     mac,
      //     du,
      //     amount,
      //     isRecharged: 1,
      //     isPaid
      //   },
      //   showLoading: false
      // }).then(res => {
      //   resolve()
      // })
    }).catch(_ => {
      reject()
    })
  })
}
